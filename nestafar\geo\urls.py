from django.urls import path, include
from .views import *
from rest_framework.routers import SimpleRouter

router = SimpleRouter()
router.register('city', CityModelViewSet)
router.register('state', StateModelViewSet)
router.register('administration-area', AdministrationAreaViewSet)
router.register(r"locations", LocationViewSet)
router.register(r"location/metadata", LocationMetadataViewSet)

urlpatterns = [
    path('', include(router.urls))
]

