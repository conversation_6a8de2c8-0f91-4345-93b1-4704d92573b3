import uuid
from django.db import models
from .property import Property
import enum

class RoomAmenities(enum.Enum):
    DEDICATED_KITCHEN = "Dedicated Kitchen"
    WORKING_DESK = "Working Desk"
    AC = "Air Conditioner"
    TV = "Television"
    LIVING_AREA = "Living Area"
    KEYCARD_ACCESS = "Key Card Access"
    KING_BED = "King Bed"
    QUEEN_BED = "Queen Bed"
    SANITIZED_ROOM = "Sanitized Room"
    BATH_TUB = "Bath Tub"

    @staticmethod
    def __from__name__( name):
        for amenity in RoomAmenities:
            if amenity.name == name:
                return amenity
        return None
    
    @staticmethod
    def list_amenities():
        return [(amenity.name, amenity.value) for amenity in RoomAmenities]
    
    @staticmethod
    def validate_amenities(amenities):
        for amenity in amenities:
            if not RoomAmenities.__from__name__(amenity):
                return False
        return True

class Room(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    room_no = models.CharField(max_length=100)
    property = models.ForeignKey(
        Property, on_delete=models.CASCADE, related_name="property_rooms"
    )
    type_of_room = models.CharField(max_length=100, blank=True, null=True)
    description = models.TextField(null=True, blank=True)
    rate = models.FloatField(null=True, blank=True)
    bed = models.IntegerField(default=1)
    max_guests = models.IntegerField()
    occupied = models.BooleanField(default=False)
    checked_in = models.BooleanField(default=False)
    running_total = models.FloatField(default=0.0)
    amenities = models.JSONField(null=True, blank=True)

    def __str__(self):
        return self.property.name + " " + self.type_of_room + " " + self.room_no


class RoomPhotos(models.Model):
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name="photos")
    image = models.ImageField(upload_to="property/rooms", blank=True, null=True)
    description = models.CharField(blank=True, null=True, max_length=100)
    #TODO: replace with rank
    primary = models.BooleanField(default=False)
    
    class Meta:
        verbose_name_plural = "Room Photos"
