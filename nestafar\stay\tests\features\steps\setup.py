from pytest_bdd import given, parsers, then, when
from ..helper import create_user
from distutils.util import strtobool
from django.core.files.uploadedfile import SimpleUploadedFile
from geo.models import Location
from stay.models import Property
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)


@given(parsers.parse("Setup User\n{table}"))
def setup_user(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        create_user(
            name=row["name"],
            password=row["password"],
            phone=row["phone"],
            partner=strtobool(row["partner"]),
        )


@given(parsers.parse("Setup Locations\n{table}"))
def setup_locations(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        Location.objects.create(
            name=row["name"],
            address=row["address"],
            latitude=row["latitude"],
            longitude=row["longitude"],
            description=row["description"],
            type=row["type"],
            timezone=row["timezone"],
        )


@given(parsers.parse("Setup Properties\n{table}"))
def setup_properties(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    payloads = []
    image = SimpleUploadedFile("hotel.jpg", b"", content_type="image/jpg")
    for row in context.table:
        payload = {
            "name": row["name"],
            "location": Location.objects.get(name=row["location"]).id,
            "description": row["description"],
            "type": row["type"],
            "avg_price": row["avg_price"],
            "po_address": row["po_address"],
            "rooms": row["rooms"],
            "rating": row["rating"],
            "meal_cost": row["meal_cost"],
            "directions": row["directions"],
            "image": image,
        }
        payloads.append(payload)

    for row in payloads:
        response = auth_client.post(reverse("stay:property-list"), data=row, format="multipart")
        assert response.status_code == 201, response.json()


@given(parsers.parse('Setup Rooms\n{table}'))
def setup_rooms(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    payloads = []
    for row in context.table:
        _property = Property.objects.get(name=row['property'])
        payload = {
            'type_of_room': row['type_of_room'],
            'rate': row['rate'],
            'property': _property.id,
            'description': row['description'],
            'bed': row['bed'],
            'max_guests': row['max_guests'],
            'room_no': row['room_no'],
        }
        response = auth_client.post(reverse('stay:room-list'), data=payload,
                                    format='json')
        assert response.status_code == 201
