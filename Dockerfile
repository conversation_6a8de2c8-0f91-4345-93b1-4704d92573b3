FROM python:3.10.5
EXPOSE 8000
ENV PYTHONUNBUFFERED 1
WORKDIR /app
COPY ./requirements.txt ./
RUN pip install --upgrade pip
RUN pip install -r requirements.txt 
COPY . /app
RUN cd nestafar && python manage.py makemigrations
CMD [ "sh","-c", "cd nestafar && python3 manage.py migrate &&  python3 manage.py collectstatic --noinput && gunicorn --bind 0.0.0.0:8000 nestafar.wsgi && celery -A nestafar worker -l info "]