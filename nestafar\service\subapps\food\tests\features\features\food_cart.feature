Scenario Outline: Managing food cart
  Given I am an authenticated user
  When I add a food service item to the cart
  Then the response status code is 201
  And the response data contains the added cart item details

  When I remove a food service item from the cart
  Then the response status code is 200
  And the removed item is no longer present in the cart

  When I try to add an invalid item to the cart
  Then the response status code is 400
  And the response data contains error messages

  Examples:
    | Action        | Description                                           |
    |----------------|----------------------------------------------------|
    | Add item       | Add a valid food service item to the cart        |
    | Remove item    | Remove an existing item from the cart              |
    | Add invalid    | Try adding an item that doesn't exist or is invalid |
