from django.db import transaction
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from service.serializers import (ServicePartnerSerializer, ServicePartnerListSerializer,
                                 ServicePartnerRetrieveSerializer,
                                 ServicePartnerCreateSerializer,
                                 PropertyPartnerSerializer,HideServiceSerializer,
                                 ServiceVendorSerializer)
from core.permissions import PartnerPermission, PropertyPermission, ServicePermission
from stay.models import PropertyPartner
from rest_framework.views import APIView
from service.models import ServicePartner
from nestafar.responses import SuccessResponse, BadRequestResponse, CreateResponse
from django_filters import rest_framework as filters
from rest_framework.filters import OrderingFilter, SearchFilter
from service.service_factory import *
from rest_framework.parsers import MultiPartParser, FormParser
from geo.utils import calculate_distance
from rest_framework.pagination import PageNumberPagination
from django.db.models import F, ExpressionWrapper, FloatField, Value
from django.db.models import Q


class ServicePartnerFilter(filters.FilterSet):
    in_house = filters.BooleanFilter(field_name="in_house", method="filter_in_house")

    def filter_in_house(self, queryset, name, value):
        if value is not None:
            return queryset.filter(partner__in_house=value)
        return queryset

    class Meta:
        model = ServicePartner
        fields = ["type_of_service", "name", "location", "in_house"]


class ServicePartnerViewSet(ModelViewSet):
    permission_classes = [PropertyPermission, PartnerPermission]
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = ServicePartnerFilter

    def get_serializer_class(self):
        if self.action == "list":
            return ServicePartnerListSerializer
        elif self.action == "retrieve":
            return ServicePartnerRetrieveSerializer
        return ServicePartnerSerializer

    def get_queryset(self):
        _property = self.request.property
        if _property:
            property_partners = _property.property_partner.all()
            service_partner_ids = [p.partner.id for p in property_partners]
            return ServicePartner.objects.filter(id__in=service_partner_ids)
        else:
            return ServicePartner.objects.none()

    def retrieve(self, request, pk=None):
        try:
            if not hasattr(self.request, "property"):
                return BadRequestResponse(message="Property not found")
            context = {"_property": self.request.property}
            service_partner = ServicePartner.objects.get(id=pk)
            serializer = ServicePartnerRetrieveSerializer(service_partner, context=context)
            return SuccessResponse(serializer.data)
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def create(self, request):
        try:
            serializer = ServicePartnerCreateSerializer(data=request.data)
            if serializer.is_valid():
                if hasattr(self.request, "property"):
                    location = request.property.location
                    partner_type = [pt for pt in ServicePartner.PartnerTypes if pt.value == serializer.data.get("type_of_service")][0]
                    perm_name = next(k for k,v in url_mappings.items() if v==partner_type)
                    if not getattr(request.user.partner_profile, f'has_{perm_name}', False):
                        return BadRequestResponse(message="You are not allowed to create this type of service")
                    with transaction.atomic():
                        name = ""
                        service_partner = ServicePartner.objects.create(
                            name=serializer.data.get("name"),
                            location=location,
                            type_of_service=serializer.data.get("type_of_service"),
                            description=serializer.data.get("description"),
                            phone_number=serializer.data.get("phone_number"),
                            is_visible=not serializer.data.get("in_house", False),
                        )
                        if serializer.data.get("in_house", False):
                            name = request.property.name.title()
                        else:
                            name = service_partner.name.title()
                        service = service_partner.service(
                            partner=service_partner,
                            name=name + " Menu",
                            tax_rate=serializer.data.get("tax_rate"),
                            charges=serializer.data.get("charges"),
                            **serializer.data.get("service", {})
                        )
                        service.save()
                        PropertyPartner.objects.create(
                            name=service_partner.name + "-" + self.request.property.name,
                            property=self.request.property, partner=service_partner,
                            commission=serializer.data.get("commission"),
                            pickup_charges=serializer.data.get("pickup_charges"),
                            delivery_charges=serializer.data.get("delivery_charges"),
                            in_house=serializer.data.get("in_house", False)
                        )

                        return CreateResponse(
                            message="Service Partner successfully created"
                        )
                else:
                    return BadRequestResponse(message="Property not found")
            else:
                return BadRequestResponse(message=serializer.errors)
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def update(self, request, pk=None):
        try:
            serializer = ServicePartnerCreateSerializer(data=request.data)
            if serializer.is_valid():
                if hasattr(self.request, "property"):
                    location = request.property.location
                    with transaction.atomic():
                        sp = ServicePartner.objects.get(id=pk)
                        sp.name = serializer.data.get("name")
                        sp.location = location
                        sp.description = serializer.data.get("description")
                        sp.phone_number = serializer.data.get("phone_number")
                        sp.save(update_fields=["name", "location", "description", "phone_number"])

                        service = sp.service.objects.filter(partner=sp).first()
                        service.tax_rate = serializer.data.get("tax_rate")
                        service.charges = serializer.data.get("charges")
                        service.save(update_fields=["tax_rate", "charges"])

                        service_args = serializer.data.get("service", {})
                        sp.service.objects.filter(pk=service.pk).update(**service_args)

                        property_partner = PropertyPartner.objects.get(
                            property=self.request.property, partner=sp
                        )
                        property_partner.pickup_charges = serializer.data.get("pickup_charges")
                        property_partner.delivery_charges = serializer.data.get("delivery_charges")
                        property_partner.commission = serializer.data.get("commission")
                        property_partner.save(update_fields=["pickup_charges", "delivery_charges", "commission"])

                        return CreateResponse(
                            message="Service Partner successfully updated"
                        )
                else:
                    return BadRequestResponse(message="Property not found")
            else:
                return BadRequestResponse(serializer.errors)
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def destroy(self, request, pk=None):
        try:
            sp = ServicePartner.objects.get(pk=pk)
            property = request.property
            if not property:
                return BadRequestResponse(message="Property not found")
            property_partner = PropertyPartner.objects.filter(property=property, partner=sp)
            property_partner.delete()
            return SuccessResponse(message="Property Partner deleted successfully")
        except Exception as e:
            return BadRequestResponse(message=str(e))


class ServiceItemView(APIView):
    permission_classes = [PropertyPermission, PartnerPermission, ServicePermission]
    parser_classes = (MultiPartParser, FormParser)

    def get(self, request, service_type=None):
        try:
            service_type = url_mappings.get(service_type)
            if service_type:
                if not request.property:
                    return BadRequestResponse(message="Property not found")
                with transaction.atomic():
                    property_partners = PropertyPartner.objects.filter(property=request.property, partner__type_of_service=service_type)
                    in_house= request.query_params.get("in_house")
                    in_house = True if in_house == "true" else False
                    if in_house:
                        property_partners = property_partners.filter(in_house=in_house)
                    service_partners = [p.partner for p in property_partners]
                    objs = service_item_model.get(service_type).objects.filter(service__partner__in=service_partners)
                    filter = service_item_filter.get(service_type)
                    objs = filter(queryset=objs, data=request.query_params).qs
                    serializer = service_item_list_serializer.get(service_type)(objs, many=True)
                    return SuccessResponse(serializer.data)
            else:
                return BadRequestResponse(message="Invalid Service Type")
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def post(self, request, service_type=None):
        try:
            service_name = service_type
            service_type = url_mappings.get(service_type)
            if service_type:
                serializer = service_item_create_serializer.get(service_type)(data=request.data)
                if serializer.is_valid():
                    item = serializer.validated_data
                    item['is_active'] = True
                    with transaction.atomic():
                        obj = service_item_model.get(service_type).objects.create(**item)
                        serializer = service_item_list_serializer.get(service_type)(obj)
                        return CreateResponse(data=serializer.data, message=service_name + " service items created")
                else:
                    return BadRequestResponse(data={}, message=serializer.errors)
            else:
                return BadRequestResponse(message="Invalid Service Type")

        except Exception as e:
            return BadRequestResponse(message=str(e))

    def put(self, request, service_type=None):
        try:
            partner_type = url_mappings.get(service_type)
            if partner_type:
                obj = service_item_model.get(partner_type).objects.filter(id=request.data.get("id"))
                if not obj.exists():
                    return BadRequestResponse(data={}, message="Item not found")
                image = request.FILES.get('image')
                serializer = service_item_create_serializer.get(partner_type)(obj.first(), data=request.data, partial=True)
                if serializer.is_valid():
                    with transaction.atomic():
                        serializer.save()
                        if image:
                            obj.first().image.save(str(obj.first().id) + '/' + image.name, image.file)
                            obj.first().save()
                        return SuccessResponse(data="Updated successfully")
                else:
                    return BadRequestResponse(data={}, message=serializer.errors)
            else:
                return BadRequestResponse(message="Invalid Service Type")
        except Exception as e:
            return BadRequestResponse(message=str(e))


    def delete(self, request, service_type=None):
        try:
            partner_type = url_mappings.get(service_type)
            if partner_type:
                with transaction.atomic():
                    obj = service_item_model.get(partner_type).objects.filter(id=request.data.get("id"))
                    obj.delete()
                    return SuccessResponse(data={}, message=" service items deleted")
            else:
                return BadRequestResponse(message="Invalid Service Type")
        except Exception as e:
            return BadRequestResponse(message=str(e))

class HideServiceView(APIView):
    permission_classes = [PropertyPermission, PartnerPermission]

    @transaction.atomic
    def post(self, request, *args, **kwargs):
        serializer = HideServiceSerializer(data=request.data)
        if serializer.is_valid():
            partner_id = serializer.validated_data.get('partner_id')
            service_id = serializer.validated_data.get('service_id')
            active = not serializer.validated_data['hide']

            if partner_id:
                try:
                    with transaction.atomic():
                        partner = ServicePartner.objects.get(id=partner_id)
                        partner.services().update(is_active=active)
                        partner.service_items().update(is_active=active)
                        return SuccessResponse(message="Partner services hidden")
                except ServicePartner.DoesNotExist:
                    return BadRequestResponse(message="Service partner not found")
            elif service_id:
                service = None
                for service_cls in service_model.values():
                    try:
                        service = service_cls.objects.get(id=service_id)
                        break
                    except service_cls.DoesNotExist:
                        continue

                if service:
                    with transaction.atomic():
                        service.is_active = active
                        service.save()
                        items = service.partner.service_items()
                        items.update(is_active=active)
                        return SuccessResponse(message="Service updated successfully")
                else:
                    return BadRequestResponse(message="Service not found")
            
            else:
                return BadRequestResponse(message="Either partner_id or service_id must be provided")
        else:
            return BadRequestResponse(serializer.errors)


class ServiceVendorPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

class ServiceVendorView(APIView):
    permission_classes = [PropertyPermission, PartnerPermission]
    pagination_class = ServiceVendorPagination

    def get(self, request):
        try:
            if not hasattr(request, "property"):
                return BadRequestResponse(message="Property not found")

            property_location = request.property.location
            if not property_location:
                return BadRequestResponse(message="Property location not found")

            # Get existing service partner IDs for this property
            existing_partner_ids = PropertyPartner.objects.filter(
                property=request.property
            ).values_list('partner_id', flat=True)

            # Start with partners in the same administrative area (pincode), excluding existing ones
            queryset = ServicePartner.objects.filter(
                location__administrative_area=property_location.administrative_area,
                is_visible=True
            ).exclude(
                id__in=existing_partner_ids
            )

            # If no results, expand to city level, still excluding existing ones
            if not queryset.exists():
                queryset = ServicePartner.objects.filter(
                    location__administrative_area__city=property_location.administrative_area.city
                ).exclude(
                    id__in=existing_partner_ids
                )

            # Apply filters if any
            filter_backend = ServicePartnerFilter(
                request.query_params,
                queryset=queryset
            )
            queryset = filter_backend.qs

            # Apply search if any
            search_param = request.query_params.get('search')
            if search_param:
                queryset = queryset.filter(
                    Q(name__icontains=search_param) |
                    Q(description__icontains=search_param)
                )

            # Ensure property coordinates are available
            property_location.ensure_coordinates()
            if not property_location.latitude or not property_location.longitude:
                # Default ordering if no coordinates
                queryset = queryset.order_by('name')
                paginator = self.pagination_class()
                page = paginator.paginate_queryset(queryset, request)
                serializer = ServiceVendorSerializer(page, many=True)
                return SuccessResponse(data=serializer.data)

            # Ensure all service partner locations have coordinates
            for partner in queryset:
                if partner.location:
                    partner.location.ensure_coordinates()

            # Calculate distance using Python function for better numerical stability
            lat1 = float(property_location.latitude)
            lon1 = float(property_location.longitude)

            # Filter partners with valid coordinates
            queryset = queryset.filter(
                location__latitude__isnull=False,
                location__longitude__isnull=False
            )

            # Calculate distance for each partner using the stable Haversine formula
            partners_with_distance = []
            for partner in queryset:
                try:
                    lat2 = float(partner.location.latitude)
                    lon2 = float(partner.location.longitude)
                    distance = calculate_distance(lat1, lon1, lat2, lon2)
                    partner.distance = distance
                    partners_with_distance.append(partner)
                except (ValueError, TypeError, AttributeError):
                    # Skip partners with invalid coordinates
                    continue

            # Sort by distance
            partners_with_distance.sort(key=lambda x: x.distance if x.distance is not None else float('inf'))
            queryset = partners_with_distance

            # Apply pagination manually since we have a list
            paginator = self.pagination_class()
            page_size = paginator.get_page_size(request)
            page_number = request.query_params.get(paginator.page_query_param, 1)
            try:
                page_number = int(page_number)
            except (TypeError, ValueError):
                page_number = 1
            
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            page = queryset[start_index:end_index]
            
            serializer = ServiceVendorSerializer(page, many=True)
            return SuccessResponse(data=serializer.data)

        except Exception as e:
            return BadRequestResponse(message=str(e))
        
    def post(self, request):
        try:
            sp = ServicePartner.objects.get(id=request.data.get('id'))
            with transaction.atomic():
                if PropertyPartner.objects.filter(property=request.property, partner=sp).exists():
                    PropertyPartner.objects.filter(property=request.property, partner=sp).update(
                        commission=request.data.get('commission'),
                        pickup_charges=request.data.get('pickup_charges'),
                        delivery_charges=request.data.get('delivery_charges'),
                        in_house=request.data.get('in_house', False)
                    )
                else:
                    PropertyPartner.objects.create(
                        name=sp.name + "-" + request.property.name,
                        property=request.property,
                        partner=sp,
                        commission=request.data.get('commission'),
                        pickup_charges=request.data.get('pickup_charges'),
                        delivery_charges=request.data.get('delivery_charges'),
                        in_house=request.data.get('in_house', False)
                    )
            return SuccessResponse(message="Service vendor created successfully")
        except Exception as e:
            return BadRequestResponse(message=str(e))
