"""
Comprehensive Django signals for AioSell integration.

This module provides signals for automatic synchronization of PMS data
to AioSell when relevant model changes occur.
"""

import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.db import transaction
from pms.tasks import sync_hotel_initial_data_to_aiosell, sync_hotel_configuration_update
from pms.models import (
    RoomType, RatePlan, Calendar, RoomBlock,
    HotelOTAIntegration, RoomBlockSyncLog
)
from pms.tasks import (
    sync_calendar_to_aiosell, sync_room_block_to_aiosell,
    sync_room_type_to_aiosell, sync_rate_plan_to_aiosell
)

logger = logging.getLogger(__name__)


def _has_aiosell_integration(hotel) -> bool:
    """Check if hotel has active AioSell integration."""
    if not hotel:
        return False
    
    return HotelOTAIntegration.objects.filter(
        hotel=hotel,
        ota_platform__name='aiosell',
        is_active=True,
        ota_platform__is_active=True
    ).exists()


def _should_sync_to_aiosell(hotel) -> bool:
    """Check if hotel should sync to AioSell based on configuration."""
    if not _has_aiosell_integration(hotel):
        return False
    
    # Check hotel-specific sync settings
    try:
        config = hotel.get_channel_manager_config('aiosell') or {}
        # Support both flattened format {'sync_enabled': True, ...}
        # and nested format {'enabled': True, 'config': {'sync_enabled': True, ...}}
        if 'sync_enabled' in config:
            return config.get('sync_enabled', True)
        inner = config.get('config') if isinstance(config, dict) else None
        if isinstance(inner, dict):
            return inner.get('sync_enabled', True)
        return True
    except Exception as e:
        logger.warning(f"Failed to retrieve AioSell config for hotel {hotel.id}: {str(e)}")
        return False  # Default to no sync if config check fails

@receiver(post_save, sender=Calendar)
def sync_calendar_on_save(sender, instance, created, **kwargs):
    """
    Signal handler to sync calendar entries to AioSell when created or updated.
    
    This triggers rate restrictions sync for the affected date and rate plan.
    """
    try:
        hotel = instance.room_type.hotel
        
        if not _should_sync_to_aiosell(hotel):
            logger.debug(f"Skipping calendar sync for hotel {hotel.id} - AioSell not enabled")
            return
        
        # Queue async task to sync calendar entry
        transaction.on_commit(
            lambda: sync_calendar_to_aiosell.delay(
                calendar_id=str(instance.id),
                action='create' if created else 'update'
            )
        )
        
        logger.info(
            f"Queued calendar sync for entry {instance.id} "
            f"(room type: {instance.room_type.name}, date: {instance.date})"
        )
        
    except Exception as e:
        logger.error(f"Error in calendar sync signal for entry {instance.id}: {str(e)}")


@receiver(post_delete, sender=Calendar)
def sync_calendar_on_delete(sender, instance, **kwargs):
    """
    Signal handler to sync calendar entry deletion to AioSell.
    
    This removes rate restrictions for the affected date and rate plan.
    """
    try:
        hotel = instance.room_type.hotel
        
        if not _should_sync_to_aiosell(hotel):
            logger.debug(f"Skipping calendar deletion sync for hotel {hotel.id} - AioSell not enabled")
            return
        
        # Queue async task to remove calendar restrictions
        transaction.on_commit(
            lambda: sync_calendar_to_aiosell.delay(
                calendar_id=str(instance.id),
                action='delete',
                calendar_data={
                    'room_type_id': str(instance.room_type.id),
                    'rate_plan_id': str(instance.rate_plan.id),
                    'date': instance.date.isoformat(),
                    'hotel_id': str(hotel.id)
                }
            )
        )
        
        logger.info(f"Queued calendar deletion sync for entry {instance.id}")
        
    except Exception as e:
        logger.error(f"Error in calendar deletion sync signal for entry {instance.id}: {str(e)}")


@receiver(post_save, sender=RoomType)
def sync_room_type_on_save(sender, instance, created, **kwargs):
    """
    Signal handler to sync room type changes to AioSell.
    
    This may affect inventory restrictions and room code mappings.
    """
    try:
        hotel = instance.hotel
        
        if not _should_sync_to_aiosell(hotel):
            logger.debug(f"Skipping room type sync for hotel {hotel.id} - AioSell not enabled")
            return
        
        # Queue async task to sync room type
        transaction.on_commit(
            lambda: sync_room_type_to_aiosell.delay(
                room_type_id=str(instance.id),
                action='create' if created else 'update'
            )
        )
        
        logger.info(f"Queued room type sync for {instance.name} (hotel: {hotel.name})")
        
    except Exception as e:
        logger.error(f"Error in room type sync signal for {instance.id}: {str(e)}")


@receiver(post_save, sender=RatePlan)
def sync_rate_plan_on_save(sender, instance, created, **kwargs):
    """
    Signal handler to sync rate plan changes to AioSell.
    
    This affects rate restrictions and rate plan code mappings.
    """
    try:
        hotel = instance.room_type.hotel
        
        if not _should_sync_to_aiosell(hotel):
            logger.debug(f"Skipping rate plan sync for hotel {hotel.id} - AioSell not enabled")
            return
        
        # Queue async task to sync rate plan
        transaction.on_commit(
            sync_rate_plan_to_aiosell(
                rate_plan_id=str(instance.id),
                action='create' if created else 'update'
            )
        )
        
        logger.info(
            f"Queued rate plan sync for {instance.name} "
            f"(room type: {instance.room_type.name})"
        )
        
    except Exception as e:
        logger.error(f"Error in rate plan sync signal for {instance.id}: {str(e)}")


@receiver(post_save, sender=RoomBlock)
def sync_room_block_on_save(sender, instance, created, **kwargs):
    """
    Enhanced signal handler to sync room blocks to AioSell.
    
    This uses inventory restrictions to block/unblock room availability.
    """
    try:
        hotel = instance.hotel
        
        if not hotel:
            logger.warning(f"Room block {instance.id} has no associated hotel")
            return
        if not _should_sync_to_aiosell(hotel):
            logger.debug(f"Skipping room block sync for hotel {hotel.id} - AioSell not enabled")
            return
        # Check if auto-sync is enabled for room blocks
        config = hotel.get_channel_manager_config('aiosell') or {}
        if not config.get('auto_sync_on_block', True):
            logger.debug(f"Auto-sync disabled for room blocks in hotel {hotel.id}")
            return
        # Create sync log entry
        action = 'create' if created else 'update'

        try:
            integration = HotelOTAIntegration.objects.get(
                hotel=hotel,
                ota_platform__name='aiosell',
                is_active=True
            )
        except HotelOTAIntegration.DoesNotExist:
            logger.error(f"No active AioSell integration found for hotel {hotel.id}")
            return
        except HotelOTAIntegration.MultipleObjectsReturned:
            logger.warning(f"Multiple active AioSell integrations found for hotel {hotel.id}, using the most recent one")
            integration = HotelOTAIntegration.objects.filter(
                hotel=hotel,
                ota_platform__name='aiosell',
                is_active=True
            ).order_by('-created_at').first()       
        sync_log = RoomBlockSyncLog.objects.create(
            room_block=instance,
            hotel=hotel,
            ota_platform_id=integration.ota_platform.id,
            action=action,
            sync_status='pending'
        )
        # Queue async task
        transaction.on_commit(
            lambda: sync_room_block_to_aiosell.delay(
                room_block_id=str(instance.id),
                sync_log_id=str(sync_log.id),
                action=action
            )
        )
        
        logger.info(
            f"Queued room block sync for block {instance.id} "
            f"(room: {instance.room.room_no if instance.room else 'N/A'}, action: {action})"
        )
        
    except Exception as e:
        logger.error(f"Error in room block sync signal for {instance.id}: {str(e)}")


@receiver(post_delete, sender=RoomBlock)
def sync_room_block_on_delete(sender, instance, **kwargs):
    """
    Enhanced signal handler to sync room block deletion to AioSell.
    
    This removes inventory restrictions to make the room available again.
    """
    try:
        hotel = instance.hotel
        
        if not hotel:
            logger.warning(f"Room block {instance.id} has no associated hotel")
            return
        
        if not _should_sync_to_aiosell(hotel):
            logger.debug(f"Skipping room block deletion sync for hotel {hotel.id} - AioSell not enabled")
            return
        
        # Check if auto-sync is enabled for room unblocking
        config = hotel.get_channel_manager_config('aiosell') or {}
        if not config.get('auto_sync_on_unblock', True):
            logger.debug(f"Auto-sync disabled for room unblocking in hotel {hotel.id}")
            return
        
        # Create sync log entry for deletion
        try:
            ota_integration = HotelOTAIntegration.objects.get(
                hotel=hotel,
                ota_platform__name='aiosell',
                is_active=True
            )
            ota_platform_id = ota_integration.ota_platform.id
        except HotelOTAIntegration.DoesNotExist:
            logger.error(f"No active AioSell integration found for hotel {hotel.id}")
            return

        sync_log = RoomBlockSyncLog.objects.create(
            room_block=None,  # Block is deleted
            hotel=hotel,
            ota_platform_id=ota_platform_id,
            action='delete',
            sync_status='pending',
            request_data={
                'deleted_room_block_id': str(instance.id),
                'room_id': str(instance.room.id) if instance.room else None,
                'room_no': instance.room.room_no if instance.room else None,
                'blocked_from': instance.blocked_from.isoformat() if instance.blocked_from else None,
                'blocked_until': instance.blocked_until.isoformat() if instance.blocked_until else None,
                'reason': instance.reason,
            }
        )        
        # Queue async task
        transaction.on_commit(
            lambda: sync_room_block_to_aiosell.delay(
                room_block_id=str(instance.id),
                sync_log_id=str(sync_log.id),
                action='delete',
                room_block_data=sync_log.request_data
            )
        )
        
        logger.info(f"Queued room block deletion sync for block {instance.id}")
        
    except Exception as e:
        logger.error(f"Error in room block deletion sync signal for {instance.id}: {str(e)}")


@receiver(post_save, sender=HotelOTAIntegration)
def sync_hotel_data_on_integration_setup(sender, instance, created, **kwargs):
    """
    Signal handler to sync existing hotel data when AioSell integration is set up.
    
    This performs an initial sync of room types, rate plans, and active calendar entries.
    """
    try:
        # Only process AioSell integrations
        if instance.ota_platform.name != 'aiosell' or not instance.is_active:
            return
        
        if not created:
            return  # Only sync on new integration setup
        
        hotel = instance.hotel
        
        logger.info(f"New AioSell integration created for hotel {hotel.name}. Starting initial sync...")
        
        # Queue initial sync tasks
        
        transaction.on_commit(
            lambda: sync_hotel_initial_data_to_aiosell.delay(
                hotel_id=str(hotel.id),
                integration_id=str(instance.id)
            )
        )
        
        logger.info(f"Queued initial data sync for hotel {hotel.name}")
        
    except Exception as e:
        logger.error(f"Error in hotel integration setup signal for {instance.id}: {str(e)}")


# Rate and Inventory Update Signals for Property Configuration Changes
@receiver(post_save, sender='stay.Property')
def sync_property_config_changes(sender, instance, **kwargs):
    """
    Signal handler for property configuration changes that affect AioSell.
    
    This monitors changes to channel manager configurations and triggers re-sync if needed.
    """
    try:
        if not _has_aiosell_integration(instance):
            return
        
        # Check if channel manager configuration changed

        if hasattr(instance, '_original_channel_managers') and instance._original_channel_managers is not None:
            old_config = (instance._original_channel_managers or {}).get('aiosell', {})
            new_config = instance.get_channel_manager_config('aiosell') or {}
            
            # Check for significant configuration changes
            significant_changes = [
                'room_mapping',
                'rate_plans',
                'target_channels',
                'sync_enabled'
            ]
            
            config_changed = any(
                old_config.get(key) != new_config.get(key)
                for key in significant_changes
            )
            
            if config_changed:
                logger.info(f"AioSell configuration changed for hotel {instance.name}. Triggering re-sync...")
                
                transaction.on_commit(
                    lambda: sync_hotel_configuration_update.delay(
                        hotel_id=str(instance.id),
                        config_changes=significant_changes
                    )
                )
        
    except Exception as e:
        logger.error(f"Error in property config sync signal for {instance.id}: {str(e)}")


# Store original values for comparison
@receiver(pre_save, sender='stay.Property')
def store_original_property_values(sender, instance, **kwargs):
    if instance.pk:
        try:
            # Get the existing instance from the database
            existing = sender.objects.get(pk=instance.pk)
            instance._original_channel_managers = dict(existing.channel_managers or {})
        except sender.DoesNotExist:
            # Instance was deleted or doesn't exist yet
            instance._original_channel_managers = {}
    else:
        # New instance, set empty original values
        instance._original_channel_managers = {}