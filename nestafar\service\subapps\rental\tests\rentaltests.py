from django.contrib.auth import get_user_model
from rental.models import *
from rest_framework.test import APIClient
from django.urls import reverse
from core.models import User
import json

# Create a reusable client object
def create_user(context, username, password):
    client = APIClient()
    user = User.objects.get(username=username)
    login_response = client.post(
        reverse("login"), {"username": username, "password": password}
    )
    token = json.loads(login_response.content)["access_token"]
    client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
    context.auth_client = client
    context.auth_user = user

def create_staff_user(username="staff_user", password="secret"):
    """
    Creates a test user object with staff privileges.
    """
    user = create_user(username, password)
    user.is_staff = True
    user.save()
    return user


def authenticate_user(client, user):
    """
    Authenticates a user with the test client.
    """
    client.force_authenticate(user=user)

def create_rental_service(name="Test Service", partner=None):
    """
    Creates a test rental service object.
    """
    if not partner:
        partner = ServicePartner.objects.create(name="Test Partner", type_of_service="RENTAL")
    service = RentalService.objects.create(name=name, partner=partner)
    return service

def create_rental_service_item(service, name="Test Item", price=10.50, deposit=20.0, type_of_rental="bike"):
    """
    Creates a test rental service item object.
    """
    item = RentalServiceItem.objects.create(
        service=service, name=name, price=price, deposit=deposit, type_of_rental=type_of_rental
    )
    return item
