"""
Tests for the Channel Manager adapter system.
"""

from django.test import TestCase
from unittest.mock import Mock, patch
from booking.channel_managers.factory import ChannelManagerFactory
from booking.channel_managers.aiosell import AIOSellChannelManager


class ChannelManagerFactoryTests(TestCase):
    """Test the Channel Manager Factory."""
    
    def test_create_aiosell_adapter(self):
        """Test creating AIOSell adapter."""
        adapter = ChannelManagerFactory.create_adapter('aiosell')
        self.assertIsInstance(adapter, AIOSellChannelManager)
        self.assertEqual(adapter.channel_name, 'aiosell')
    

    
    def test_create_unknown_adapter(self):
        """Test creating unknown adapter returns None."""
        adapter = ChannelManagerFactory.create_adapter('unknown_ota')
        self.assertIsNone(adapter)
    
    def test_list_available_adapters(self):
        """Test listing available adapters."""
        adapters = ChannelManagerFactory.list_available_adapters()
        self.assertIn('aiosell', adapters)
        self.assertEqual(adapters['aiosell'], AIOSellChannelManager)
    
    def test_get_adapter_by_webhook_path(self):
        """Test getting adapter by webhook path."""
        # Test AIOSell path
        adapter = ChannelManagerFactory.get_adapter_by_webhook_path('/webhook/aiosell/')
        self.assertIsInstance(adapter, AIOSellChannelManager)

        # Test invalid path
        adapter = ChannelManagerFactory.get_adapter_by_webhook_path('/invalid/path/')
        self.assertIsNone(adapter)


class AIOSellChannelManagerTests(TestCase):
    """Test the AIOSell Channel Manager adapter."""
    
    def setUp(self):
        self.adapter = AIOSellChannelManager()
    
    def test_validate_webhook_data_valid(self):
        """Test validation with valid AIOSell data."""
        data = {
            'action': 'book',
            'hotelCode': 'HOTEL123',
            'bookingId': 'BK123456',
            'checkin': '2024-01-01',
            'checkout': '2024-01-02',
            'guest': {'firstName': 'John', 'lastName': 'Doe'},
            'rooms': [{'occupancy': {'adults': 2}}],
            'amount': {'amountAfterTax': 100}
        }
        
        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertTrue(is_valid)
        self.assertIsNone(error)
    
    def test_validate_webhook_data_missing_fields(self):
        """Test validation with missing required fields."""
        data = {
            'action': 'book',
            'hotelCode': 'HOTEL123'
            # Missing bookingId
        }
        
        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertFalse(is_valid)
        self.assertIn('Missing required fields', error)
    
    def test_parse_guest_data(self):
        """Test parsing guest data."""
        data = {
            'guest': {
                'firstName': 'John',
                'lastName': 'Doe',
                'email': '<EMAIL>',
                'phone': '1234567890',
                'address': {
                    'line1': '123 Main St',
                    'city': 'New York',
                    'state': 'NY',
                    'country': 'US',
                    'zipCode': '10001'
                }
            }
        }
        
        guest_info = self.adapter.parse_guest_data(data)
        
        self.assertEqual(guest_info['first_name'], 'John')
        self.assertEqual(guest_info['last_name'], 'Doe')
        self.assertEqual(guest_info['email'], '<EMAIL>')
        self.assertEqual(guest_info['phone'], '1234567890')
        self.assertEqual(guest_info['address']['line1'], '123 Main St')
    
    def test_parse_room_data(self):
        """Test parsing room data."""
        data = {
            'rooms': [
                {
                    'occupancy': {'adults': 2, 'children': 1}
                },
                {
                    'occupancy': {'adults': 1, 'children': 0}
                }
            ]
        }
        
        room_info = self.adapter.parse_room_data(data)
        
        self.assertEqual(room_info['total_guests'], 4)  # 2+1+1+0
        self.assertEqual(len(room_info['rooms_data']), 2)

    def test_validate_aiosell_documentation_data(self):
        """Test validation with exact data from AIOSell documentation."""
        # Sample data from AIOSell documentation
        data = {
            "action": "book",
            "hotelCode": "SANDBOX-PMS",
            "channel": "Goingo",
            "bookingId": "*********",
            "cmBookingId": "AAABBBCCC",
            "bookedOn": "2022-12-08 15:25:35",
            "checkin": "2022-12-10",
            "checkout": "2022-12-12",
            "segment": "OTA",
            "specialRequests": "Airport Taxi Required",
            "pah": False,
            "amount": {
                "amountAfterTax": 1204.0,
                "amountBeforeTax": 1075.0,
                "tax": 129.0,
                "currency": "INR"
            },
            "guest": {
                "firstName": "Akshay",
                "lastName": "Kumar",
                "email": "<EMAIL>",
                "phone": "9988776655",
                "address": {
                    "line1": "51",
                    "city": "Bangalore",
                    "state": "Karnataka",
                    "country": "India",
                    "zipCode": "560035"
                }
            },
            "rooms": [
                {
                    "roomCode": "SUITE",
                    "rateplanCode": "SUITE-S-101",
                    "guestName": "Akshay Kumar",
                    "occupancy": {
                        "adults": 1,
                        "children": 0
                    },
                    "prices": [
                        {
                            "date": "2022-12-10",
                            "sellRate": 537.5
                        },
                        {
                            "date": "2022-12-11",
                            "sellRate": 537.5
                        }
                    ]
                }
            ]
        }

        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertTrue(is_valid)
        self.assertIsNone(error)

    def test_validate_aiosell_cancel_data(self):
        """Test validation with AIOSell cancellation data."""
        # Sample cancellation data from AIOSell documentation
        data = {
            "action": "cancel",
            "hotelCode": "SANDBOX-PMS",
            "channel": "Goingo",
            "bookingId": "*********"
        }

        is_valid, error = self.adapter.validate_webhook_data(data)
        self.assertTrue(is_valid)
        self.assertIsNone(error)

    def test_parse_aiosell_guest_data(self):
        """Test parsing AIOSell guest data from documentation."""
        data = {
            "guest": {
                "firstName": "Akshay",
                "lastName": "Kumar",
                "email": "<EMAIL>",
                "phone": "9988776655",
                "address": {
                    "line1": "51",
                    "city": "Bangalore",
                    "state": "Karnataka",
                    "country": "India",
                    "zipCode": "560035"
                }
            }
        }

        guest_info = self.adapter.parse_guest_data(data)

        self.assertEqual(guest_info['first_name'], 'Akshay')
        self.assertEqual(guest_info['last_name'], 'Kumar')
        self.assertEqual(guest_info['email'], '<EMAIL>')
        self.assertEqual(guest_info['phone'], '9988776655')
        self.assertEqual(guest_info['address']['line1'], '51')
        self.assertEqual(guest_info['address']['city'], 'Bangalore')
        self.assertEqual(guest_info['address']['zip_code'], '560035')



