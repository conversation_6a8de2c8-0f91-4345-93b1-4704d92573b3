#!/usr/bin/env python
"""
Test script to verify the race condition fix for rate plan creation.

This script tests that calendar entries are created before the rate plan sync signal fires.
"""

import os
import sys
import django
from datetime import date, timedelta
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nestafar.settings')
sys.path.append('/nestafar')
django.setup()

from django.test import TestCase, TransactionTestCase
from django.db import transaction
from unittest.mock import patch, MagicMock
from pms.models import RatePlan, RoomType, Calendar
from stay.models import Property


class RatePlanRaceConditionTest(TransactionTestCase):
    """Test that calendar entries exist when rate plan sync signal fires."""
    
    def setUp(self):
        """Set up test data."""
        self.property = Property.objects.create(
            name="Test Hotel",
            address="123 Test St",
            city="Test City",
            state="Test State",
            country="Test Country",
            postal_code="12345"
        )
        
        self.room_type = RoomType.objects.create(
            hotel=self.property,
            name="SUITE",
            max_occupancy=4,
            base_price=Decimal('200.00')
        )
    
    @patch('pms.signals.sync_rate_plan_to_aiosell.apply_async')
    @patch('pms.signals._should_sync_to_aiosell', return_value=True)
    def test_calendar_entries_exist_when_signal_fires(self, mock_should_sync, mock_sync_task):
        """Test that calendar entries are created before the sync signal fires."""
        
        # Track when the signal fires and what calendar entries exist at that time
        calendar_count_when_signal_fired = None
        
        def capture_calendar_count(*args, **kwargs):
            nonlocal calendar_count_when_signal_fired
            # Count calendar entries for this rate plan when signal fires
            rate_plan_id = args[0] if args else kwargs.get('args', [None])[0]
            if rate_plan_id:
                calendar_count_when_signal_fired = Calendar.objects.filter(
                    rate_plan_id=rate_plan_id
                ).count()
            return MagicMock()
        
        mock_sync_task.side_effect = capture_calendar_count
        
        # Create rate plan with date range
        valid_from = date.today()
        valid_to = valid_from + timedelta(days=2)  # 3 days total
        
        rate_plan_data = {
            'room_type': self.room_type,
            'name': 'Test Rate Plan',
            'base_rate': Decimal('150.00'),
            'valid_from': valid_from,
            'valid_to': valid_to,
            'is_active': True
        }
        
        # Create rate plan (this should trigger the signal)
        with transaction.atomic():
            rate_plan = RatePlan.objects.create(**rate_plan_data)
        
        # Verify signal was called
        self.assertTrue(mock_sync_task.called)
        
        # Verify calendar entries existed when signal fired
        self.assertIsNotNone(calendar_count_when_signal_fired)
        self.assertEqual(calendar_count_when_signal_fired, 3)  # 3 days = 3 calendar entries
        
        # Verify calendar entries were actually created
        actual_calendar_count = Calendar.objects.filter(rate_plan=rate_plan).count()
        self.assertEqual(actual_calendar_count, 3)
        
        # Verify calendar entries have correct data
        calendar_entries = Calendar.objects.filter(rate_plan=rate_plan).order_by('date')
        expected_dates = [valid_from + timedelta(days=i) for i in range(3)]
        
        for i, entry in enumerate(calendar_entries):
            self.assertEqual(entry.date, expected_dates[i])
            self.assertEqual(entry.daily_rate, Decimal('150.00'))
            self.assertEqual(entry.room_type, self.room_type)
    
    def test_calendar_entries_created_correctly(self):
        """Test that calendar entries are created with correct data."""
        valid_from = date.today()
        valid_to = valid_from + timedelta(days=1)  # 2 days
        
        rate_plan = RatePlan.objects.create(
            room_type=self.room_type,
            name='Test Rate Plan 2',
            base_rate=Decimal('100.00'),
            valid_from=valid_from,
            valid_to=valid_to,
            is_active=True
        )
        
        # Check calendar entries were created
        calendar_entries = Calendar.objects.filter(rate_plan=rate_plan).order_by('date')
        self.assertEqual(calendar_entries.count(), 2)
        
        # Check first entry
        self.assertEqual(calendar_entries[0].date, valid_from)
        self.assertEqual(calendar_entries[0].daily_rate, Decimal('100.00'))
        
        # Check second entry
        self.assertEqual(calendar_entries[1].date, valid_from + timedelta(days=1))
        self.assertEqual(calendar_entries[1].daily_rate, Decimal('100.00'))


if __name__ == '__main__':
    import unittest
    unittest.main()
