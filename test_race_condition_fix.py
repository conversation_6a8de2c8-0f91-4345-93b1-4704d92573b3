#!/usr/bin/env python
"""
Test script to verify the new date range approach for rate plan sync.

This script tests that rate plans sync their entire date range to AioSell
without relying on individual calendar entries.
"""

import os
import sys
import django
from datetime import date, timedelta
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'nestafar.settings')
sys.path.append('/nestafar')
django.setup()

from django.test import TestCase, TransactionTestCase
from django.db import transaction
from unittest.mock import patch, MagicMock
from pms.models import RatePlan, RoomType, Calendar
from stay.models import Property


class RatePlanDateRangeTest(TransactionTestCase):
    """Test the new date range approach for rate plan sync."""

    def setUp(self):
        """Set up test data."""
        self.property = Property.objects.create(
            name="Test Hotel",
            address="123 Test St",
            city="Test City",
            state="Test State",
            country="Test Country",
            postal_code="12345"
        )
        
        self.room_type = RoomType.objects.create(
            hotel=self.property,
            name="SUITE",
            max_occupancy=4,
            base_price=Decimal('200.00')
        )
    
    @patch('pms.services.aiosell.AioSellService.sync_rate_plan_range')
    @patch('pms.signals._should_sync_to_aiosell', return_value=True)
    def test_rate_plan_syncs_date_range_directly(self, mock_should_sync, mock_sync_range):
        """Test that rate plans sync their entire date range directly to AioSell."""

        # Mock the sync_rate_plan_range method to capture the call
        mock_sync_range.return_value = {'status': 'success', 'synced_dates': 3}

        # Create rate plan with date range
        valid_from = date.today()
        valid_to = valid_from + timedelta(days=2)  # 3 days total

        rate_plan_data = {
            'room_type': self.room_type,
            'name': 'Test Rate Plan',
            'base_rate': Decimal('150.00'),
            'valid_from': valid_from,
            'valid_to': valid_to,
            'is_active': True
        }

        # Create rate plan (this should trigger the signal and sync)
        with transaction.atomic():
            rate_plan = RatePlan.objects.create(**rate_plan_data)

        # Process any pending tasks (simulate Celery execution)
        from pms.tasks.aiosell_tasks import sync_rate_plan_to_aiosell
        from pms.services import get_aiosell_service

        # Mock the service to return our mocked service
        with patch('pms.services.get_aiosell_service') as mock_get_service:
            mock_service = MagicMock()
            mock_service.sync_rate_plan_range = mock_sync_range
            mock_get_service.return_value = mock_service

            # Execute the sync task directly
            sync_rate_plan_to_aiosell(str(rate_plan.id), 'create')

        # Verify the sync_rate_plan_range method was called with correct parameters
        mock_sync_range.assert_called_once()
        call_args = mock_sync_range.call_args

        # Check that the rate plan was passed correctly
        self.assertEqual(call_args[1]['rate_plan'], rate_plan)
        self.assertEqual(call_args[1]['action'], 'create')

        # Verify calendar entries were still created for internal PMS use
        calendar_entries = Calendar.objects.filter(rate_plan=rate_plan)
        self.assertEqual(calendar_entries.count(), 3)  # 3 days = 3 calendar entries

    def test_date_range_approach_eliminates_race_condition(self):
        """Test that the date range approach eliminates race conditions."""

        # Create multiple rate plans simultaneously to test for race conditions
        valid_from = date.today()
        valid_to = valid_from + timedelta(days=1)  # 2 days

        rate_plans = []
        for i in range(3):
            rate_plan = RatePlan.objects.create(
                room_type=self.room_type,
                name=f'Test Rate Plan {i}',
                base_rate=Decimal('100.00') + Decimal(str(i * 10)),
                valid_from=valid_from,
                valid_to=valid_to,
                is_active=True
            )
            rate_plans.append(rate_plan)

        # Verify each rate plan has its own calendar entries
        for i, rate_plan in enumerate(rate_plans):
            calendar_entries = Calendar.objects.filter(rate_plan=rate_plan).order_by('date')
            self.assertEqual(calendar_entries.count(), 2)

            # Check entries have correct rate plan reference
            for entry in calendar_entries:
                self.assertEqual(entry.rate_plan, rate_plan)
                self.assertEqual(entry.daily_rate, Decimal('100.00') + Decimal(str(i * 10)))

    def test_aiosell_payload_structure(self):
        """Test that the AioSell payload matches expected structure."""
        from pms.services.aiosell import AioSellService

        # This test verifies the payload structure matches the sample you provided
        valid_from = date.today()
        valid_to = valid_from + timedelta(days=2)  # 3 days

        rate_plan = RatePlan.objects.create(
            room_type=self.room_type,
            name='Executive Rate',
            base_rate=Decimal('200.00'),
            valid_from=valid_from,
            valid_to=valid_to,
            min_stay_days=2,
            is_active=True
        )

        # Mock the service to capture the payload
        with patch('pms.services.aiosell.AioSellService._make_api_request') as mock_request:
            mock_request.return_value = {'status': 'success'}

            # Create a mock integration for the service
            from pms.models import HotelOTAIntegration, OTAPlatform

            ota_platform, _ = OTAPlatform.objects.get_or_create(
                name='aiosell',
                defaults={'api_endpoint': 'https://live.aiosell.com/api/v2/cm'}
            )

            integration = HotelOTAIntegration.objects.create(
                hotel=self.property,
                ota_platform=ota_platform,
                credentials={'identifier': 'test-token'},
                is_active=True
            )

            service = AioSellService(integration)

            # Call the sync method
            result = service.sync_rate_plan_range(rate_plan, 'create')

            # Verify the API was called
            self.assertTrue(mock_request.called)

            # Get the payload that was sent
            call_args = mock_request.call_args
            payload = call_args[0][1]  # Second argument is the payload

            # Verify payload structure matches AioSell format
            self.assertIn('hotelCode', payload)
            self.assertIn('toChannels', payload)
            self.assertIn('updates', payload)

            # Verify updates structure
            update = payload['updates'][0]
            self.assertEqual(update['startDate'], valid_from.isoformat())
            self.assertEqual(update['endDate'], valid_to.isoformat())
            self.assertIn('rates', update)

            # Verify rates structure
            rates = update['rates']
            self.assertIsInstance(rates, list)
            self.assertGreater(len(rates), 0)

            # Check rate structure
            rate = rates[0]
            self.assertIn('roomCode', rate)
            self.assertIn('rateplanCode', rate)
            self.assertIn('restrictions', rate)

            # Check restrictions structure
            restrictions = rate['restrictions']
            expected_restrictions = [
                'stopSell', 'minimumStay', 'closeOnArrival', 'closeOnDeparture'
            ]
            for restriction in expected_restrictions:
                self.assertIn(restriction, restrictions)


if __name__ == '__main__':
    import unittest
    unittest.main()
