from pytest_bdd import given, parsers, then
from stay.models import Property, Room


@then(parsers.parse("Validate Properties\n{table}"))
def validate_properties(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        prop = Property.objects.get(name=row["name"])
        assert prop.name == row["name"]
        assert prop.avg_price == int(row["avg_price"])
        assert prop.rooms == int(row["rooms"])
        assert prop.location.name == row["location"]
