# Generated by Django 4.2.7 on 2025-08-07 21:09

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0003_reservation_amount_before_tax_reservation_booked_on_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='reservation',
            name='amount_before_tax',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)]),
        ),
        migrations.AlterField(
            model_name='reservation',
            name='tax_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, validators=[django.core.validators.MinValueValidator(0)]),
        ),
    ]
