# Generated by Django 4.2.7 on 2025-08-15 14:53

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('booking', '0005_precheckin_is_completed_precheckin_reservation'),
        ('stay', '0004_remove_property_property_hotel_code_idx_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='OTAPlatform',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('api_endpoint', models.URLField(blank=True, null=True)),
                ('configuration', models.JSONField(blank=True, default=dict)),
                ('is_active', models.BooleanField(default=True)),
            ],
        ),
        migrations.CreateModel(
            name='RoomBlock',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('blocked_from', models.DateTimeField(default=django.utils.timezone.now)),
                ('blocked_until', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('reason', models.CharField(blank=True, choices=[('maintenance', 'Maintenance'), ('cleaning', 'Cleaning'), ('reservation', 'Reservation'), ('inspection', 'Inspection'), ('other', 'Other')], max_length=200, null=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_blocks', to='stay.property')),
                ('reservation', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='room_blocks', to='booking.reservation')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blocks', to='stay.room')),
            ],
        ),
        migrations.CreateModel(
            name='RoomType',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('max_occupancy', models.PositiveIntegerField(default=2)),
                ('amenities', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_types', to='stay.property')),
            ],
            options={
                'unique_together': {('hotel', 'name')},
            },
        ),
        migrations.CreateModel(
            name='RatePlan',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('base_rate', models.DecimalField(decimal_places=2, max_digits=10)),
                ('surge_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('group_discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('long_term_discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('min_stay_days', models.PositiveIntegerField(default=1)),
                ('valid_from', models.DateField()),
                ('valid_to', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('room_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='rate_plans', to='pms.roomtype')),
            ],
        ),
        migrations.CreateModel(
            name='HotelOTAIntegration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('external_hotel_id', models.CharField(blank=True, max_length=150, null=True)),
                ('credentials', models.JSONField(blank=True, default=dict)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ota_integrations', to='stay.property')),
                ('ota_platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hotel_integrations', to='pms.otaplatform')),
            ],
            options={
                'db_table': 'pms_hotel_ota_integration',
            },
        ),
        migrations.CreateModel(
            name='Calendar',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateField()),
                ('available_rooms', models.PositiveIntegerField(default=0)),
                ('daily_rate', models.DecimalField(decimal_places=2, max_digits=10)),
                ('restrictions', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('rate_plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_entries', to='pms.rateplan')),
                ('room_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calendar_entries', to='pms.roomtype')),
            ],
        ),
        migrations.CreateModel(
            name='AvailabilityLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('date', models.DateField()),
                ('available_rooms', models.PositiveIntegerField()),
                ('sync_status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('success', 'Success'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=30)),
                ('synced_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_logs', to='stay.property')),
                ('ota_platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_logs', to='pms.otaplatform')),
                ('room_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_logs', to='pms.roomtype')),
            ],
            options={
                'db_table': 'pms_availability_log',
            },
        ),
        migrations.CreateModel(
            name='RoomBlockSyncLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('action', models.CharField(choices=[('create', 'Create Block'), ('update', 'Update Block'), ('delete', 'Delete Block')], default='create', max_length=20)),
                ('sync_status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('success', 'Success'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=30)),
                ('request_data', models.JSONField(blank=True, default=dict)),
                ('response_data', models.JSONField(blank=True, default=dict)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('synced_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hotel', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_block_sync_logs', to='stay.property')),
                ('ota_platform', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='room_block_sync_logs', to='pms.otaplatform')),
                ('room_block', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sync_logs', to='pms.roomblock')),
            ],
            options={
                'db_table': 'pms_room_block_sync_log',
                'indexes': [models.Index(fields=['hotel', 'sync_status', 'created_at'], name='pms_room_bl_hotel_i_9239b1_idx'), models.Index(fields=['room_block', 'ota_platform'], name='pms_room_bl_room_bl_4c7eba_idx'), models.Index(fields=['sync_status', 'retry_count'], name='pms_room_bl_sync_st_499b44_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='roomblock',
            index=models.Index(fields=['hotel', 'is_active'], name='pms_roomblo_hotel_i_efb1b1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='hotelotaintegration',
            unique_together={('hotel', 'ota_platform')},
        ),
        migrations.AddIndex(
            model_name='calendar',
            index=models.Index(fields=['room_type', 'rate_plan', 'date'], name='pms_calenda_room_ty_4823d3_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='calendar',
            unique_together={('room_type', 'rate_plan', 'date')},
        ),
        migrations.AddIndex(
            model_name='availabilitylog',
            index=models.Index(fields=['hotel', 'sync_status', 'date'], name='pms_availab_hotel_i_f8efa2_idx'),
        ),
        migrations.AddIndex(
            model_name='availabilitylog',
            index=models.Index(fields=['created_at'], name='pms_availab_created_264234_idx'),
        ),
    ]
