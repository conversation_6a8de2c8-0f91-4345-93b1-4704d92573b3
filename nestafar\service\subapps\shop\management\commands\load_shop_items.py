import pandas as pd
import json
from django.core.management.base import BaseCommand, CommandError
from service.subapps.shop.models import ShopServiceItem, ShopService
from openai import OpenAI
import urllib
from django.core.files.base import ContentFile
from django.db import models

class Command(BaseCommand):
    help = 'Add shop items from a CSV file and generate missing images using OpenAI'

    def add_arguments(self, parser):
        parser.add_argument('service_id', type=str, help='ID of the service to which items belong')
        parser.add_argument('file_path', type=str, nargs='?', help='Optional Path to the CSV file with shop items')

    def handle(self, *args, **options):
        service_id = options['service_id']
        file_path = options.get('file_path', None)

        try:
            service = ShopService.objects.get(id=service_id)
        except ShopService.DoesNotExist:
            raise CommandError(f"Service with ID {service_id} does not exist.")

        if file_path:
            self.stdout.write(f"Loading items from {file_path}...")
            self.add_items_from_csv(service, file_path)
        else:
            self.stdout.write(f"Generating images for items in service {service.name}...")
            self.generate_images_for_missing_items(service)

    def add_items_from_csv(self, service, file_path):
        try:
            df = pd.read_csv(file_path)
        except FileNotFoundError:
            raise CommandError(f"File {file_path} not found.")
        except pd.errors.EmptyDataError:
            raise CommandError(f"File {file_path} is empty.")

        required_columns = ['name', 'price', 'description', 'addon']
        if not all(col in df.columns for col in required_columns):
            raise CommandError(f"CSV file must contain the following columns: {', '.join(required_columns)}")

        items_to_create = []
        for _, row in df.iterrows():
            name = row['name']
            price = row['price']
            description = row['description']
            if isinstance(row['addon'], str):
                row['addon'] = row['addon'].replace("'", '"')
                row['addon'] = json.loads(row['addon'])
            else:
                row['addon'] = None

            item = ShopServiceItem(
                service=service,
                name=name,
                price=price,
                description=description,
                addon=row['addon'] 
            )
            items_to_create.append(item)

        # Bulk create all items at once for better performance
        ShopServiceItem.objects.bulk_create(items_to_create)
        self.stdout.write(self.style.SUCCESS(f'Successfully added {len(items_to_create)} items to service {service.name}'))

    def generate_images_for_missing_items(self, service):
        items_without_images = ShopServiceItem.objects.filter(service=service).filter(models.Q(image__isnull=True) | models.Q(image=''))
        
        for item in items_without_images:
            prompt = f"{item.name}: {item.description}"
            self.stdout.write(f"Generating image for '{item.name}' with prompt: {prompt}")

            try:
                client = OpenAI()

                # Generating image using OpenAI API
                response = client.images.generate(
                    model="dall-e-2",
                    prompt=prompt,
                    size="512x512",
                    quality="standard",
                    n=1,
                )

                # Retrieving the generated image URL
                image_url = response.data[0].url
                result = urllib.request.urlretrieve(image_url)

                # Reading the image content and saving it to the model
                with open(result[0], 'rb') as img_file:
                    image_content = ContentFile(img_file.read())
                    item.image.save(f'{item.name}_generated.png', image_content)
                    item.save()

                self.stdout.write(self.style.SUCCESS(f"Image successfully generated and saved for {item.name}"))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error generating image for {item.name}: {e}"))

        self.stdout.write(self.style.SUCCESS('Image generation completed for all items with missing images.'))

