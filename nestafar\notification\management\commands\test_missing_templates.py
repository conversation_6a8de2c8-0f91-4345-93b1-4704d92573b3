from django.core.management.base import BaseCommand
from django.conf import settings
from notification.channel.whatsapp import WhatsAppChannel
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Test templates that need to be created in WhatsApp Business Manager - shows what needs to be configured'

    def add_arguments(self, parser):
        parser.add_argument('--phone', type=str, required=True, help='Phone number to test (with country code, e.g., +2347088212727)')
        parser.add_argument('--template', type=str, help='Specific template category to test (e.g., USER_ORDER_ACCEPTED)')
        parser.add_argument('--debug', action='store_true', help='Enable detailed debug output')

    def handle(self, *args, **options):
        phone = options['phone']
        specific_template = options.get('template')
        debug_mode = options.get('debug', False)
        
        self.stdout.write('🔧 Testing Templates That Need WhatsApp Business Manager Setup\n')
        self.stdout.write('⚠️  These templates are NOT YET configured in WhatsApp Business Manager\n')
        self.stdout.write('📋 Use this to verify which templates need to be created\n')
        
        try:
            # Initialize WhatsApp channel
            whatsapp = WhatsAppChannel()
            
            if debug_mode:
                self.stdout.write('🔍 Debug mode enabled\n')
                self.stdout.write(f'📞 Target phone: {phone}')
                self.stdout.write(f'📱 Phone Number ID: {whatsapp.phone_number_id}')
                self.stdout.write('')

            # Define test templates that need to be created
            test_templates = self.get_templates_needing_setup()
            
            # Filter to specific template if requested
            if specific_template:
                if specific_template in test_templates:
                    test_templates = {specific_template: test_templates[specific_template]}
                else:
                    self.stdout.write(f'❌ Template category "{specific_template}" not found')
                    self.stdout.write(f'Available categories: {", ".join(test_templates.keys())}')
                    return
            
            successful_tests = 0
            failed_tests = 0
            
            self.stdout.write(f'🔄 Testing {len(test_templates)} templates needing setup...\n')
            
            for category, template_data in test_templates.items():
                self.stdout.write(f'🔸 Testing {category}')
                
                # Extract parameters for the template
                kwargs = template_data['kwargs']
                expected_params = template_data['expected_params']
                
                if debug_mode:
                    self.stdout.write(f'   📋 Template name: {whatsapp._get_template_name(category)}')
                    self.stdout.write(f'   📋 Expected parameters: {expected_params}')
                    self.stdout.write(f'   📋 Test parameters: {kwargs}')
                
                # Get template name and components
                template_name = whatsapp._get_template_name(category)
                components = whatsapp._build_template_components(category, **kwargs)
                
                # Format phone number for API
                formatted_phone = whatsapp.format_phone_number(phone)
                
                if debug_mode:
                    self.stdout.write(f'   📞 Formatted phone: {formatted_phone}')
                    self.stdout.write(f'   📋 Components: {components}')
                
                # Send template message directly (this will fail as expected)
                success, result = whatsapp.send_template_message_to_phone(
                    formatted_phone, template_name, components
                )
                
                if success:
                    successful_tests += 1
                    self.stdout.write(f'   ✅ Template sent successfully! Message ID: {result}')
                    self.stdout.write(f'   🎉 Template "{template_name}" exists and is approved!')
                else:
                    failed_tests += 1
                    self.stdout.write(f'   ❌ Template failed: {result}')
                    if "template does not exist" in str(result).lower() or "template name" in str(result).lower():
                        self.stdout.write(f'   📋 Template "{template_name}" needs to be created in WhatsApp Business Manager')
                    elif "parameter" in str(result).lower():
                        self.stdout.write(f'   📋 Template "{template_name}" exists but parameter count mismatch')
                    else:
                        self.stdout.write(f'   📋 Template "{template_name}" may need approval or has other issues')
                
                if debug_mode:
                    self.stdout.write('')
                
                self.stdout.write('')  # Add spacing between tests
                
            # Summary
            self.stdout.write('📊 Test Summary:')
            self.stdout.write(f'   ✅ Working: {successful_tests} (already configured)')
            self.stdout.write(f'   ❌ Failed: {failed_tests} (need to be created)')
            self.stdout.write(f'   📋 Total: {successful_tests + failed_tests}')
            
            if successful_tests > 0:
                self.stdout.write('\n🎉 Some templates are already working!')
                self.stdout.write('   ✅ These templates are properly configured and approved')
            
            if failed_tests > 0:
                self.stdout.write('\n🔧 Templates needing setup:')
                self.stdout.write('   📋 Create these templates in WhatsApp Business Manager')
                self.stdout.write('   ⏰ Wait for approval (24-48 hours)')
                self.stdout.write('   📖 Check WHATSAPP_TEMPLATES.md for suggested template content')
                self.stdout.write('\n📝 Next steps:')
                self.stdout.write('   1. Go to WhatsApp Business Manager → Message Templates')
                self.stdout.write('   2. Create templates with exact parameter counts shown above')
                self.stdout.write('   3. Use UTILITY category for transactional messages')
                self.stdout.write('   4. Wait for approval (24-48 hours)')
                self.stdout.write('   5. Re-run this test to verify')
            
        except Exception as e:
            self.stdout.write(f'❌ Error: {str(e)}')
            import traceback
            if debug_mode:
                self.stdout.write(traceback.format_exc())

    def get_templates_needing_setup(self):
        """Define test data for templates that need to be created in WhatsApp Business Manager"""
        return {
            # User order flow templates
            'USER_CHECKOUT': {
                'expected_params': 2,
                'kwargs': {
                    'username': 'John Doe',
                    'property_name': 'Grand Hotel'
                }
            },
            'USER_ORDER_ACCEPTED': {
                'expected_params': 6,
                'kwargs': {
                    'username': 'John Doe',
                    'order_id': 'ORD-12345',
                    'vendor_name': 'Restaurant ABC',
                    'estimated_time': '30 minutes',
                    'total_amount': '₹250',
                    'vendor_contact': '+919876543210'
                }
            },
            'USER_ORDER_CANCELLED': {
                'expected_params': 6,
                'kwargs': {
                    'username': 'John Doe',
                    'order_id': 'ORD-12345',
                    'service_type': 'Food',
                    'reason': 'Vendor unavailable',
                    'refund_amount': '₹250',
                    'additional_info': 'Refund will be processed in 3-5 working days'
                }
            },
            'USER_ORDER_ONGOING': {
                'expected_params': 5,
                'kwargs': {
                    'username': 'John Doe',
                    'order_id': 'ORD-12345',
                    'vendor_name': 'Restaurant ABC',
                    'estimated_time': '15 minutes',
                    'contact_number': '+919876543210'
                }
            },
            'USER_ORDER_REJECTED': {
                'expected_params': 5,
                'kwargs': {
                    'username': 'John Doe',
                    'order_id': 'ORD-12345',
                    'service_type': 'Food',
                    'rejection_reason': 'Item out of stock',
                    'refund_amount': '₹250'
                }
            },

            # Critical missing templates for core business flows
            'CHECKOUT_BILL': {
                'expected_params': 4,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'property_name': 'Grand Hotel',
                    'checkout_date': '2025-01-15',
                    'total_amount': '₹4500'
                }
            },
            'ORDER_CONFIRMED': {
                'expected_params': 6,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'service_type': 'Food',
                    'order_id': 'ORD-12345',
                    'order_items': 'Chicken Biryani, Raita, Dessert',
                    'total_amount': '₹450',
                    'delivery_time': '30 minutes'
                }
            },
            'CHECKIN_SUCCESSFUL': {
                'expected_params': 4,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'property_name': 'Grand Hotel',
                    'room_details': 'Room 101 - Deluxe AC',
                    'checkout_date': '2025-01-18'
                }
            },
            'PRECHECKIN_CANCELLATION_WARNING': {
                'expected_params': 3,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'property_name': 'Grand Hotel',
                    'hours_remaining': '6'
                }
            },
            'PRECHECKIN_REMINDER': {
                'expected_params': 4,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'property_name': 'Grand Hotel',
                    'checkin_date': '2025-01-15',
                    'hours_remaining': '24'
                }
            },
            'ONBOARDING_REMINDER': {
                'expected_params': 4,
                'kwargs': {
                    'partner_name': 'Hotel Owner',
                    'property_name': 'Grand Hotel',
                    'missing_steps': ['Property photos', 'Room photos', 'Services setup'],
                    'completion_percentage': '60'
                }
            },
            'SIGNUP_SUCCESSFUL': {
                'expected_params': 2,
                'kwargs': {
                    'partner_name': 'Hotel Owner',
                    'property_name': 'Grand Hotel'
                }
            },

            # Partner order flow templates
            'PARTNER_ORDER_PLACED': {
                'expected_params': 4,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'order_id': 'ORD-12345',
                    'room_no': '101',
                    'guest_name': 'John Doe'
                }
            },
            'PARTNER_ORDER_ACCEPTED': {
                'expected_params': 4,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'order_id': 'ORD-12345',
                    'room_no': '101',
                    'guest_name': 'John Doe'
                }
            },
            'PARTNER_ORDER_ONGOING': {
                'expected_params': 5,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'order_id': 'ORD-12345',
                    'room_no': '101',
                    'guest_name': 'John Doe',
                    'estimated_completion': '15 minutes'
                }
            },
            'PARTNER_ORDER_COMPLETED': {
                'expected_params': 4,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'order_id': 'ORD-12345',
                    'room_no': '101',
                    'guest_name': 'John Doe'
                }
            },
            'PARTNER_ORDER_CANCELLED': {
                'expected_params': 4,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'order_id': 'ORD-12345',
                    'room_no': '101',
                    'guest_name': 'John Doe'
                }
            },
            'PARTNER_ORDER_REJECTED': {
                'expected_params': 7,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'order_id': 'ORD-12345',
                    'room_no': '101',
                    'guest_name': 'John Doe',
                    'rejection_reason': 'Item out of stock',
                    'refund_amount': '₹250',
                    'dashboard_link': 'https://dashboard.example.com'
                }
            },
            
            # Service management templates
            'DINNER_REMINDER': {
                'expected_params': 2,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'property_name': 'Grand Hotel'
                }
            },
            'VENDOR_ORDER_REMINDER': {
                'expected_params': 4,
                'kwargs': {
                    'vendor_name': 'Restaurant ABC',
                    'minutes_pending': '15',
                    'order_id': 'ORD-12345',
                    'total_amount': '₹250'
                }
            },
            'SERVICE_HIDDEN_NOTIFICATION': {
                'expected_params': 3,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'service_name': 'Food Delivery',
                    'property_name': 'Grand Hotel'
                }
            },
            'SERVICE_RESTORED_NOTIFICATION': {
                'expected_params': 3,
                'kwargs': {
                    'guest_name': 'John Doe',
                    'service_name': 'Food Delivery',
                    'property_name': 'Grand Hotel'
                }
            },
            
            # Summary templates
            'DAILY_SUMMARY_GUEST': {
                'expected_params': 5,
                'kwargs': {
                    'username': 'John Doe',
                    'total_orders': '3',
                    'total_spent': '₹750',
                    'property_name': 'Grand Hotel',
                    'most_ordered_service': 'Food Delivery'
                }
            },
            'DAILY_SUMMARY_PARTNER': {
                'expected_params': 4,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'total_orders': '12',
                    'total_revenue': '₹3000',
                    'property_name': 'Grand Hotel'
                }
            },
            'WEEKLY_REPORT': {
                'expected_params': 10,
                'kwargs': {
                    'partner_name': 'Restaurant ABC',
                    'property_name': 'Grand Hotel',
                    'week_start': '2025-07-21',
                    'week_end': '2025-07-27',
                    'reservations': '45',
                    'occupancy_rate': '85',
                    'avg_orders': '2.3',
                    'gmv': '₹15000',
                    'commission': '₹1500',
                    'recommendations': 'Great performance! Consider expanding menu.'
                }
            }
        }
