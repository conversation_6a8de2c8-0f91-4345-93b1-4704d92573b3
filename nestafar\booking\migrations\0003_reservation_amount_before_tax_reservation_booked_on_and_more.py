# Generated by Django 4.2.7 on 2025-08-04 19:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='reservation',
            name='amount_before_tax',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='booked_on',
            field=models.DateTimeField(blank=True, help_text='When the booking was originally made', null=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='channel',
            field=models.CharField(blank=True, help_text='Booking channel name', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='cm_booking_id',
            field=models.CharField(blank=True, help_text='Channel Manager booking ID', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='currency',
            field=models.CharField(default='INR', max_length=3),
        ),
        migrations.AddField(
            model_name='reservation',
            name='external_booking_id',
            field=models.CharField(blank=True, help_text='External OTA booking ID', max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='pah',
            field=models.BooleanField(default=False, help_text='Payment At Hotel - True if payment collected at hotel'),
        ),
        migrations.AddField(
            model_name='reservation',
            name='room_details',
            field=models.JSONField(blank=True, help_text='Room-wise pricing and occupancy details', null=True),
        ),
        migrations.AddField(
            model_name='reservation',
            name='segment',
            field=models.CharField(choices=[('OTA', 'Online Travel Agency'), ('DIRECT', 'Direct Booking'), ('CORPORATE', 'Corporate'), ('GROUPS', 'Groups'), ('WALKIN', 'Walk-in')], default='DIRECT', max_length=20),
        ),
        migrations.AddField(
            model_name='reservation',
            name='tax_amount',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
    ]
