import uuid
from django.db import models
from core.models import User
from .property import Property

class CheckinRequest(models.Model):
    class Status(models.TextChoices):
        PENDING = 'PENDING', 'Pending'
        COMPLETED = 'COMPLETED', 'Completed'
        REJECTED = 'REJECTED', 'Rejected'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="checkin_requests")
    property = models.ForeignKey(Property, on_delete=models.CASCADE, related_name="checkin_requests")
    room_no = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=10, choices=Status.choices, default=Status.PENDING)

    def __str__(self):
        return f"CheckinRequest({self.user}, {self.property}, {self.room_no})"
