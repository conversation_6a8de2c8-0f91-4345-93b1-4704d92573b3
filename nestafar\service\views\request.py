from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from core.permissions import PropertyPermission, ServicePermission
from service.service_factory import *
from nestafar.responses import SuccessResponse, BadRequestResponse
from service.serializers import *


class RequestView(APIView):
    permission_classes = [PropertyPermission, IsAuthenticated, ServicePermission]
    partner_type = None

    def get_requests(self):
        if self.request.user.is_partner:
            checked_in_rooms = self.request.property.property_rooms.filter(checked_in=True)
            carts = []
            for room in checked_in_rooms:
                cart_model = service_cart_model.get(self.partner_type)
                cart = cart_model.objects.filter(guest__room=room).exclude(status=cart_model.CartStatus.PENDING)
                cart_filter = service_cart_filter.get(self.partner_type)
                cart = cart_filter(queryset=cart, data=self.request.query_params).qs
                serializer = service_cart_serializer.get(self.partner_type)(cart, many=True)
                carts.extend(serializer.data)
            return carts
        else:
            guest = self.request.user.guest.filter(checked_in=True, checked_out=False).last()
            cart = service_cart_model.get(self.partner_type).objects.filter(guest=guest)
            cart_filter = service_cart_filter.get(self.partner_type)
            cart = cart_filter(queryset=cart, data=self.request.query_params).qs
            serializer = service_cart_serializer.get(self.partner_type)(cart, many=True)
            return serializer.data

    def all(self):
        response = {}
        for service_type, partner_type in url_mappings.items():
            self.partner_type = partner_type
            response[service_type] = self.get_requests()
        return response

    def get(self, request, service_type=None):
        try:
            if service_type == 'all':
                return SuccessResponse(data=self.all(), message="All requests")
            self.partner_type = url_mappings.get(service_type)
            data = self.get_requests()
            return SuccessResponse(data=data, message=service_type + " requests")
        except Exception as e:
            return BadRequestResponse(str(e))
