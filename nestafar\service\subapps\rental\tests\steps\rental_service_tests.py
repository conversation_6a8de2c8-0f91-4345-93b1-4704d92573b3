from pytest_bdd import scenario, given, then, when
from rental.models import RentalService
from rental.tests import rentaltests

@scenario("features/rental_service.feature")
def test_manage_rental_services(client):
    pass

@given("a rental service exists with name '<service_name>'")
def get_rental_service_by_name(service_name, service_factory=rentaltests.create_rental_service):
    service = service_factory(name=service_name)
    return service

@when("I get the list of rental services")
def get_rental_services(client):
    response = client.get("/api/rental-services/")
    response.json()  # trigger data parsing

@then("Then the response status code is 200")
def check_get_services_status_code(response):
    assert response.status_code == 200

@then("And the response data contains a list of rental services")
def check_get_services_response(response):
    data = response.json()
    assert isinstance(data, list)
    #further assertions based on API response structure(to be added)


@when("I retrieve the rental service details")
def retrieve_rental_service_details(client, service):
    response = client.get(f"/api/rental-services/{service.id}/")
    response.json()  # trigger data parsing

@then("Then the response status code is 200")
def check_get_service_details_status_code(response):
    assert response.status_code == 200

@then("And the response data contains the rental service details")
def check_get_service_details_response(response, service):
    data = response.json()
    assert data["id"] == service.id
    assert data["name"] == service.name
    # Assert partner & type
