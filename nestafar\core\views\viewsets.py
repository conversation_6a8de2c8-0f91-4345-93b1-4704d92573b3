from rest_framework.viewsets import ModelViewSet, ViewSet
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.db.models import Sum, Avg
from datetime import  timedelta
from dateutil.relativedelta import relativedelta
from decimal import Decimal
import io
from django.http import HttpResponse

from ..models import UserProfile, PartnerProfile
from ..permissions import PartnerPermission
from ..serializers import (
    PartnerSerializer,
    UserProfileSerializer,
    FinancialReportSerializer,
    VendorReportSerializer,
    GuestReportSerializer,
)
from nestafar.responses import SuccessResponse, BadRequestResponse
from stay.models import Property, Guest
from service.models import ServicePartner
from stay.models import PropertyPartner
from service import service_factory


class PartnerUserViewSet(ModelViewSet):
    queryset = PartnerProfile.objects.all()
    serializer_class = PartnerSerializer
    permission_classes = [IsAuthenticated]


class UserProfileViewSet(ModelViewSet):
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]


class ReportsViewSet(ViewSet):
    """
    ViewSet for generating partner reports.
    Supports financial, vendor, and guest reports with JSON and PDF output.
    """
    permission_classes = [IsAuthenticated, PartnerPermission]

    # Define Nestafar brand colors as class constants
    NESTAFAR_PRIMARY = '#219EB4'  # Teal/turquoise blue
    NESTAFAR_BLACK = '#000000'
    NESTAFAR_WHITE = '#FFFFFF'

    def list(self, request):
        """
        Generate reports based on query parameters.

        Query Parameters:
        - q: Report type ('financial', 'vendor', 'guest')
        - out: Output format ('json', 'pdf')
        """
        report_type = request.query_params.get('q', '').lower()
        output_format = request.query_params.get('out', 'json').lower()

        # Validate query parameters
        valid_report_types = ['financial', 'vendor', 'guest']
        valid_output_formats = ['json', 'pdf']

        if not report_type or report_type not in valid_report_types:
            return BadRequestResponse(
                message=f"Invalid report type. Must be one of: {', '.join(valid_report_types)}"
            )

        if output_format not in valid_output_formats:
            return BadRequestResponse(
                message=f"Invalid output format. Must be one of: {', '.join(valid_output_formats)}"
            )

        # Get partner's active property with ownership validation
        try:
            partner_profile = request.user.partner_profile
        except AttributeError:
            return BadRequestResponse(
                message="User is not a partner"
            )

        active_property = partner_profile.active_property

        if not active_property:
            # Try to get any property associated with this partner
            partner_properties = Property.objects.filter(staffs=partner_profile)
            if partner_properties.exists():
                active_property = partner_properties.first()
                # Update active property for future requests
                partner_profile.active_property = active_property
                partner_profile.save()
            else:
                return BadRequestResponse(
                    message="No properties found for partner"
                )

        # Verify partner has access to this property
        if not active_property.staffs.filter(id=partner_profile.id).exists():
            return BadRequestResponse(
                message="Partner does not have access to this property"
            )

        try:
            # Generate report data based on type
            if report_type == 'financial':
                report_data = self._generate_financial_report(partner_profile, active_property)
                if not report_data:
                    return BadRequestResponse(
                        message="Unable to generate financial report. No data available."
                    )
                serializer = FinancialReportSerializer(report_data)
            elif report_type == 'vendor':
                report_data = self._generate_vendor_report(partner_profile, active_property)
                if not report_data:
                    return BadRequestResponse(
                        message="Unable to generate vendor report. No data available."
                    )
                serializer = VendorReportSerializer(report_data)
            elif report_type == 'guest':
                report_data = self._generate_guest_report(partner_profile, active_property)
                if not report_data:
                    return BadRequestResponse(
                        message="Unable to generate guest report. No data available."
                    )
                serializer = GuestReportSerializer(report_data)

            # Note: No validation needed for output serializers

            # Return JSON response
            if output_format == 'json':
                return SuccessResponse(
                    data=serializer.data,
                    message=f"{report_type.title()} report generated successfully"
                )

            # Generate PDF response
            elif output_format == 'pdf':
                # Pass the original report_data (with datetime objects) for PDF generation
                pdf_response = self._generate_pdf_report(report_data, report_type)
                if not pdf_response:
                    return BadRequestResponse(
                        message="Error generating PDF report"
                    )
                return pdf_response

        except ValueError as e:
            return BadRequestResponse(
                message=f"Invalid data format: {str(e)}"
            )
        except KeyError as e:
            return BadRequestResponse(
                message=f"Missing required data field: {str(e)}"
            )
        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Report generation error for partner {partner_profile.id}: {str(e)}")

            return BadRequestResponse(
                message="An unexpected error occurred while generating the report. Please try again later."
            )

    def _generate_financial_report(self, partner_profile, property_obj):
        """Generate financial report data for the partner's property"""
        try:
            current_date = timezone.now()
            year_start = current_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

            # Validate inputs
            if not partner_profile or not property_obj:
                return None

            # Get all service partners for this property
            property_partners = PropertyPartner.objects.filter(property=property_obj)
            service_partner_ids = [pp.partner.id for pp in property_partners]

            # Get all guests for this property
            property_guests = Guest.objects.filter(room__property=property_obj)

            # Fetch all relevant data for the year in bulk queries
            year_end = year_start + relativedelta(years=1)

            # Get all checked-in guests for the year with related room data
            year_guests = property_guests.filter(
                check_in_date__gte=year_start,
                check_in_date__lt=year_end,
                checked_in=True
            ).select_related('room')

            # Get all completed service orders for the year from all service types
            year_service_orders = []
            for service_type_id in service_factory.service_order_model.keys():
                order_model = service_factory.service_order_model[service_type_id]
                orders = order_model.objects.filter(
                    guest__in=property_guests,
                    created_at__gte=year_start,
                    created_at__lt=year_end,
                    status=5  # COMPLETED status (from BaseOrder.OrderStatus.COMPLETED)
                ).select_related('guest')
                year_service_orders.extend(orders)

            # Pre-calculate monthly data structures
            monthly_earnings = []
            monthly_booking_earnings = {}
            monthly_service_earnings = {}
            monthly_commission_earnings = {}

            # Initialize monthly dictionaries
            for month_offset in range(12):
                month_start = year_start + relativedelta(months=month_offset)
                month_key = (month_start.year, month_start.month)
                monthly_booking_earnings[month_key] = Decimal('0.00')
                monthly_service_earnings[month_key] = Decimal('0.00')
                monthly_commission_earnings[month_key] = Decimal('0.00')

            # Process booking earnings in bulk
            for guest in year_guests:
                if guest.room.rate and guest.check_in_date and guest.check_out_date:
                    stay_duration = (guest.check_out_date - guest.check_in_date).days
                    booking_amount = Decimal(str(guest.room.rate)) * stay_duration

                    month_key = (guest.check_in_date.year, guest.check_in_date.month)
                    if month_key in monthly_booking_earnings:
                        monthly_booking_earnings[month_key] += booking_amount

            # Process service earnings in bulk
            for order in year_service_orders:
                month_key = (order.created_at.year, order.created_at.month)
                if month_key in monthly_service_earnings:
                    monthly_service_earnings[month_key] += Decimal(str(order.total))
                    monthly_commission_earnings[month_key] += Decimal(str(order.commissions))
            
            # Build monthly earnings summary
            for month_offset in range(12):
                month_start = year_start + relativedelta(months=month_offset)
                month_key = (month_start.year, month_start.month)
                
                booking_earnings = monthly_booking_earnings[month_key]
                service_earnings = monthly_service_earnings[month_key]
                commission_earned = monthly_commission_earnings[month_key]
                total_earnings = booking_earnings + service_earnings

                monthly_earnings.append({
                    'month': month_start.strftime('%B'),
                    'year': month_start.year,
                    'total_earnings': total_earnings,
                    'booking_earnings': booking_earnings,
                    'service_earnings': service_earnings,
                    'commission_earned': commission_earned
                })

            # Commission breakdown by service type - optimized with bulk queries
            commission_breakdown = []
            service_types = {
                1: 'Food',
                2: 'Laundry',
                3: 'Transport',
                4: 'Rental',
                5: 'Others',
                6: 'Shop',
                7: 'Tourism'
            }

            # Get all relevant orders for the year with service partner data
            year_orders_with_partners = []
            for service_type_id in service_factory.service_order_model.keys():
                order_model = service_factory.service_order_model[service_type_id]
                orders = order_model.objects.filter(
                    guest__in=property_guests,
                    status=5,  # COMPLETED status
                    created_at__gte=year_start
                ).select_related('service_partner')
                year_orders_with_partners.extend(orders)

            # Get property partners with commission rates
            property_partners_dict = {
                pp.partner_id: pp.commission for pp in property_partners
            }

            # Group orders by service type
            orders_by_service_type = {}
            for order in year_orders_with_partners:
                service_type = order.service_partner.type_of_service
                if service_type not in orders_by_service_type:
                    orders_by_service_type[service_type] = []
                orders_by_service_type[service_type].append(order)

            for service_type_id, service_name in service_types.items():
                if service_type_id in orders_by_service_type:
                    type_orders = orders_by_service_type[service_type_id]
                    
                    total_orders = len(type_orders)
                    total_revenue = sum(Decimal(str(order.total)) for order in type_orders)
                    commission_earned = sum(Decimal(str(order.commissions)) for order in type_orders)
                    
                    # Calculate average commission rate from property partners
                    relevant_partner_ids = [order.service_partner_id for order in type_orders]
                    relevant_commissions = [
                        property_partners_dict.get(partner_id, 0) 
                        for partner_id in relevant_partner_ids
                    ]
                    avg_commission = sum(relevant_commissions) / len(relevant_commissions) if relevant_commissions else Decimal('0.00')
                else:
                    total_orders = 0
                    total_revenue = Decimal('0.00')
                    commission_earned = Decimal('0.00')
                    avg_commission = Decimal('0.00')

                commission_breakdown.append({
                    'service_type': service_name,
                    'total_orders': total_orders,
                    'total_revenue': total_revenue,
                    'commission_rate': avg_commission,
                    'commission_earned': commission_earned
                })

            # Delivery charges analysis - optimized using already fetched data
            delivery_charges_analysis = []

            for service_type_id, service_name in service_types.items():
                if service_type_id in orders_by_service_type:
                    type_orders = orders_by_service_type[service_type_id]

                    total_orders = len(type_orders)
                    total_charges = sum(Decimal(str(order.charges)) for order in type_orders)
                    avg_charge = total_charges / total_orders if total_orders > 0 else Decimal('0.00')
                else:
                    total_orders = 0
                    total_charges = Decimal('0.00')
                    avg_charge = Decimal('0.00')

                delivery_charges_analysis.append({
                    'service_type': service_name,
                    'total_orders': total_orders,
                    'total_delivery_charges': total_charges,
                    'average_delivery_charge': avg_charge
                })

            # Group year orders by month for profit and payout calculations
            monthly_orders = {}
            for order in year_service_orders:
                month_key = (order.created_at.year, order.created_at.month)
                if month_key not in monthly_orders:
                    monthly_orders[month_key] = []
                monthly_orders[month_key].append(order)

            # Monthly payout summary - optimized using pre-fetched data
            payout_summary = []
            for month_offset in range(12):
                month_start = year_start + relativedelta(months=month_offset)
                month_key = (month_start.year, month_start.month)

                month_orders = monthly_orders.get(month_key, [])

                total_revenue = sum(Decimal(str(order.total)) for order in month_orders)
                total_commissions = sum(Decimal(str(order.commissions)) for order in month_orders)

                vendor_payouts = total_revenue - total_commissions
                staff_payouts = Decimal('0.00')  # Would need staff payment tracking

                payout_summary.append({
                    'month': month_start.strftime('%B'),
                    'year': month_start.year,
                    'vendor_payouts': vendor_payouts,
                    'staff_payouts': staff_payouts,
                    'total_payouts': vendor_payouts + staff_payouts
                })

            # Profit metrics - optimized using pre-fetched order data
            profit_metrics = []

            for month_offset in range(12):
                month_start = year_start + relativedelta(months=month_offset)
                month_key = (month_start.year, month_start.month)

                month_orders = monthly_orders.get(month_key, [])

                total_orders = len(month_orders)
                total_revenue = sum(Decimal(str(order.total)) for order in month_orders)
                total_profit = sum(Decimal(str(order.commissions)) for order in month_orders)
                avg_profit_per_order = total_profit / total_orders if total_orders > 0 else Decimal('0.00')

                profit_metrics.append({
                    'month': month_start.strftime('%B'),
                    'year': month_start.year,
                    'total_revenue': total_revenue,
                    'total_costs': total_revenue - total_profit,
                    'total_profit': total_profit,
                    'average_profit_per_order': avg_profit_per_order,
                    'total_orders': total_orders
                })

            # Calculate year-to-date totals using already fetched data
            ytd_stats = {
                'total_revenue': sum(Decimal(str(order.total)) for order in year_service_orders),
                'total_profit': sum(Decimal(str(order.commissions)) for order in year_service_orders),
                'total_orders': len(year_service_orders)
            }

            return {
                'report_type': 'financial',
                'generated_at': current_date,
                'partner_name': partner_profile.user.name,
                'property_name': property_obj.name,
                'monthly_earnings': monthly_earnings,
                'commission_breakdown': commission_breakdown,
                'delivery_charges_analysis': delivery_charges_analysis,
                'payout_summary': payout_summary,
                'profit_metrics': profit_metrics,
                'total_revenue_ytd': ytd_stats['total_revenue'],
                'total_profit_ytd': ytd_stats['total_profit'],
                'total_orders_ytd': ytd_stats['total_orders']
            }
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Financial report generation error: {str(e)}")
            return None

    def _generate_vendor_report(self, partner_profile, property_obj):
        """Generate vendor report data for the partner's property"""
        try:
            current_date = timezone.now()
            year_start = current_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

            # Validate inputs
            if not partner_profile or not property_obj:
                return None

            # Get all service partners for this property
            property_partners = PropertyPartner.objects.filter(property=property_obj)
            service_partners = ServicePartner.objects.filter(
                id__in=[pp.partner.id for pp in property_partners]
            )

            # Get all guests for this property
            property_guests = Guest.objects.filter(room__property=property_obj)

            # Count vendors by service type
            service_types = {
                1: 'Food',
                2: 'Laundry',
                3: 'Transport',
                4: 'Rental',
                5: 'Others',
                6: 'Shop',
                7: 'Tourism'
            }

            vendors_by_service = {}
            for service_type_id, service_name in service_types.items():
                count = service_partners.filter(type_of_service=service_type_id).count()
                vendors_by_service[service_name] = count

            # Get top performing vendors
            top_vendors = []
            for vendor in service_partners:
                # Get orders for this vendor from the appropriate service type
                vendor_orders = []
                if vendor.type_of_service in service_factory.service_order_model:
                    order_model = service_factory.service_order_model[vendor.type_of_service]
                    vendor_orders = order_model.objects.filter(
                        service_partner=vendor,
                        guest__in=property_guests,
                        status=5,  # COMPLETED status
                        created_at__gte=year_start
                    )

                stats = {
                    'total_orders': vendor_orders.count() if vendor_orders else 0,
                    'total_revenue': vendor_orders.aggregate(total=Sum('total'))['total'] if vendor_orders else Decimal('0.00'),
                    'total_commissions': vendor_orders.aggregate(total=Sum('commissions'))['total'] if vendor_orders else Decimal('0.00')
                }

                # Handle None values
                for key in stats:
                    if stats[key] is None:
                        stats[key] = Decimal('0.00') if 'total' in key else 0

                total_orders = stats['total_orders'] or 0
                total_revenue = stats['total_revenue'] or Decimal('0.00')
                commission_earned = stats['total_commissions'] or Decimal('0.00')
                avg_order_value = total_revenue / total_orders if total_orders > 0 else Decimal('0.00')

                # Calculate outstanding dues (simplified - revenue minus commission)
                outstanding_dues = total_revenue - commission_earned

                vendor_data = {
                    'vendor_id': vendor.id,
                    'vendor_name': vendor.name,
                    'service_type': service_types.get(vendor.type_of_service, 'Others'),
                    'total_orders': total_orders,
                    'total_revenue': total_revenue,
                    'average_order_value': avg_order_value,
                    'commission_earned': commission_earned,
                    'outstanding_dues': outstanding_dues
                }

                if total_orders > 0:  # Only include vendors with orders
                    top_vendors.append(vendor_data)

            # Sort by total revenue and take top 10
            top_vendors.sort(key=lambda x: x['total_revenue'], reverse=True)
            top_vendors = top_vendors[:10]

            # Calculate total outstanding dues
            total_outstanding_dues = sum(vendor['outstanding_dues'] for vendor in top_vendors)

            return {
                'report_type': 'vendor',
                'generated_at': current_date,
                'partner_name': partner_profile.user.name,
                'property_name': property_obj.name,
                'total_vendors': service_partners.count(),
                'vendors_by_service': vendors_by_service,
                'top_vendors': top_vendors,
                'total_outstanding_dues': total_outstanding_dues
            }
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Vendor report generation error: {str(e)}")
            return None

    def _generate_guest_report(self, partner_profile, property_obj):
        """Generate guest report data for the partner's property"""
        try:
            current_date = timezone.now()
            year_start = current_date.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)

            # Validate inputs
            if not partner_profile or not property_obj:
                return None

            # Get all guests for this property
            property_guests = Guest.objects.filter(room__property=property_obj)

            # Monthly stay metrics
            stay_metrics = []
            total_room_nights = 0
            total_possible_nights = 0

            for month_offset in range(12):
                month_start = year_start + relativedelta(months=month_offset)
                month_end = (month_start + relativedelta(months=1)) - timedelta(days=1)

                # Guests who checked in during this month
                month_guests = property_guests.filter(
                    check_in_date__gte=month_start,
                    check_in_date__lte=month_end,
                    checked_in=True
                )

                total_guests = month_guests.count()

                # Calculate average stay duration
                stay_durations = []
                for guest in month_guests:
                    if guest.check_in_date and guest.check_out_date:
                        duration = (guest.check_out_date - guest.check_in_date).days
                        stay_durations.append(duration)
                    elif guest.check_in_date and not guest.checked_out:
                        # For current guests, calculate duration up to now
                        duration = (current_date - guest.check_in_date).days
                        stay_durations.append(duration)

                avg_stay_duration = sum(stay_durations) / len(stay_durations) if stay_durations else 0

                # Calculate occupancy rate
                total_rooms = property_obj.rooms
                days_in_month = (month_end - month_start).days + 1
                possible_room_nights = total_rooms * days_in_month
                actual_room_nights = sum(stay_durations)
                occupancy_rate = (actual_room_nights / possible_room_nights * 100) if possible_room_nights > 0 else 0

                total_room_nights += actual_room_nights
                total_possible_nights += possible_room_nights

                stay_metrics.append({
                    'month': month_start.strftime('%B'),
                    'year': month_start.year,
                    'total_guests': total_guests,
                    'average_stay_duration': Decimal(str(round(avg_stay_duration, 2))),
                    'occupancy_rate': Decimal(str(round(occupancy_rate, 2)))
                })

            # Top guests by spending and frequency
            guest_stats = {}
            for guest in property_guests:
                # Count stays
                stays = property_guests.filter(user=guest.user).count()

                # Calculate total spending from orders across all service types
                total_spending = Decimal('0.00')
                for service_type_id in service_factory.service_order_model.keys():
                    order_model = service_factory.service_order_model[service_type_id]
                    guest_orders = order_model.objects.filter(
                        guest=guest,
                        status=5,  # COMPLETED status
                        created_at__gte=year_start
                    )
                    spending = guest_orders.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
                    # Ensure spending is Decimal type
                    if spending is not None:
                        spending = Decimal(str(spending))
                    else:
                        spending = Decimal('0.00')
                    total_spending += spending

                # Calculate average stay duration for this guest
                guest_stays = property_guests.filter(user=guest.user, checked_out=True)
                stay_durations = []
                for stay in guest_stays:
                    if stay.check_in_date and stay.check_out_date:
                        duration = (stay.check_out_date - stay.check_in_date).days
                        stay_durations.append(duration)

                avg_duration = sum(stay_durations) / len(stay_durations) if stay_durations else 0

                # Get last visit
                last_visit = property_guests.filter(user=guest.user).order_by('-check_in_date').first()
                last_visit_date = last_visit.check_in_date if last_visit else None

                guest_key = guest.user.id
                if guest_key not in guest_stats:
                    guest_stats[guest_key] = {
                        'guest_id': guest.id,
                        'guest_name': guest.user.name,
                        'total_stays': stays,
                        'total_spending': total_spending,
                        'average_stay_duration': Decimal(str(round(avg_duration, 2))),
                        'last_visit': last_visit_date
                    }
                else:
                    # Update with higher spending if multiple guest records for same user
                    if total_spending > guest_stats[guest_key]['total_spending']:
                        guest_stats[guest_key].update({
                            'guest_id': guest.id,
                            'total_spending': total_spending,
                            'last_visit': last_visit_date
                        })

            # Sort by total spending and take top 10
            top_guests = sorted(guest_stats.values(), key=lambda x: x['total_spending'], reverse=True)[:10]

            # Guest ordering metrics by service type
            service_types = {
                1: 'Food',
                2: 'Laundry',
                3: 'Transport',
                4: 'Rental',
                5: 'Others',
                6: 'Shop',
                7: 'Tourism'
            }

            ordering_metrics = []
            for service_type_id, service_name in service_types.items():
                if service_type_id in service_factory.service_order_model:
                    order_model = service_factory.service_order_model[service_type_id]
                    type_orders = order_model.objects.filter(
                        guest__in=property_guests,
                        status=5,  # COMPLETED status
                        created_at__gte=year_start
                    )

                    total_orders = type_orders.count()
                    unique_guests = type_orders.values('guest').distinct().count()
                    total_guests_count = property_guests.filter(checked_in=True).count()
                    ordering_rate = (unique_guests / total_guests_count * 100) if total_guests_count > 0 else 0
                    avg_order_value = type_orders.aggregate(avg=Avg('total'))['avg'] or Decimal('0.00')
                    # Ensure avg_order_value is Decimal type
                    if avg_order_value is not None:
                        avg_order_value = Decimal(str(avg_order_value))
                    else:
                        avg_order_value = Decimal('0.00')
                else:
                    total_orders = 0
                    unique_guests = 0
                    ordering_rate = 0
                    avg_order_value = Decimal('0.00')

                ordering_metrics.append({
                    'service_type': service_name,
                    'total_orders': total_orders,
                    'unique_guests': unique_guests,
                    'ordering_rate': Decimal(str(round(ordering_rate, 2))),
                    'average_order_value': avg_order_value
                })

            # Calculate summary totals
            total_unique_guests = property_guests.values('user').distinct().count()
            average_occupancy_rate = (total_room_nights / total_possible_nights * 100) if total_possible_nights > 0 else 0

            # Count total guest orders across all service types
            total_guest_orders = 0
            for service_type_id in service_factory.service_order_model.keys():
                order_model = service_factory.service_order_model[service_type_id]
                orders_count = order_model.objects.filter(
                    guest__in=property_guests,
                    status=5,  # COMPLETED status
                    created_at__gte=year_start
                ).count()
                total_guest_orders += orders_count

            return {
                'report_type': 'guest',
                'generated_at': current_date,
                'partner_name': partner_profile.user.name,
                'property_name': property_obj.name,
                'stay_metrics': stay_metrics,
                'top_guests': top_guests,
                'ordering_metrics': ordering_metrics,
                'total_unique_guests': total_unique_guests,
                'average_occupancy_rate': Decimal(str(round(average_occupancy_rate, 2))),
                'total_guest_orders': total_guest_orders
            }
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Guest report generation error: {str(e)}")
            return None

    def _generate_pdf_report(self, report_data, report_type):
        """Generate PDF report from report data"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.lib.enums import TA_CENTER, TA_LEFT
        except ImportError:
            return BadRequestResponse(
                message="PDF generation not available. Please install reportlab: pip install reportlab"
            )

        # Define Nestafar brand colors first
        nestafar_primary = colors.Color(0.129, 0.620, 0.706)  # #219EB4 (teal/turquoise blue)

        # Create PDF buffer
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(
            buffer,
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=72
        )
        styles = getSampleStyleSheet()
        story = []

        # Title style with Nestafar branding
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=20,
            fontName='Helvetica-Bold',
            spaceAfter=6,
            alignment=TA_CENTER,
            textColor=nestafar_primary
        )

        # Subtitle style
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Normal'],
            fontSize=12,
            fontName='Helvetica',
            spaceAfter=20,
            alignment=TA_CENTER,
            textColor=colors.black
        )

        # Add title and subtitle
        title = f"{report_type.title()} Report"
        subtitle = f"{report_data['property_name']} - Partner Analytics"
        story.append(Paragraph(title, title_style))
        story.append(Paragraph(subtitle, subtitle_style))

        # Add a horizontal line separator
        line_style = ParagraphStyle(
            'LineStyle',
            parent=styles['Normal'],
            fontSize=1,
            spaceAfter=15,
            alignment=TA_CENTER
        )
        separator_line = f'<para><font color="{nestafar_primary}">{"_" * 60}</font></para>'
        story.append(Paragraph(separator_line, line_style))

        # Add professional header with logo and branding
        try:
            import os
            from reportlab.platypus import Image
            from reportlab.lib.units import inch

            logo_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'Assets', 'logo.jpg')
            if os.path.exists(logo_path):
                # Smaller logo (45x45 pixels)
                logo = Image(logo_path, width=0.6*inch, height=0.6*inch)

                # Smaller Nestafar branding text in black
                brand_style = ParagraphStyle(
                    'BrandStyle',
                    parent=styles['Normal'],
                    fontSize=18,
                    fontName='Helvetica-Bold',
                    textColor=colors.black,
                    alignment=TA_LEFT,
                    leftIndent=10
                )
                brand_text = Paragraph("Nestafar", brand_style)

                # Create header table with logo and brand text
                header_table = Table([[logo, brand_text]], colWidths=[0.8*inch, 2*inch])
                header_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (0, 0), 'CENTER'),  # Logo center aligned
                    ('ALIGN', (1, 0), (1, 0), 'LEFT'),    # Brand text left aligned
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ('LEFTPADDING', (0, 0), (-1, -1), 0),
                    ('RIGHTPADDING', (0, 0), (-1, -1), 0),
                    ('TOPPADDING', (0, 0), (-1, -1), 0),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 0),
                ]))

                story.append(header_table)
                story.append(Spacer(1, 15))
        except Exception:
            # Fallback: Just add smaller Nestafar text in black if logo fails
            brand_style = ParagraphStyle(
                'BrandStyleFallback',
                parent=styles['Normal'],
                fontSize=18,
                fontName='Helvetica-Bold',
                textColor=colors.black,
                alignment=TA_CENTER
            )
            story.append(Paragraph("Nestafar", brand_style))
            story.append(Spacer(1, 15))

        # Add generation info with improved styling
        info_style = ParagraphStyle(
            'InfoStyle',
            parent=styles['Normal'],
            fontSize=10,
            fontName='Helvetica',
            textColor=colors.black,
            alignment=TA_LEFT,
            leftIndent=20,
            spaceAfter=15
        )

        info_text = f"<b>Report Details:</b><br/>"
        info_text += f"Generated: {report_data['generated_at'].strftime('%B %d, %Y at %I:%M %p')}<br/>"
        info_text += f"Partner: <b>{report_data['partner_name']}</b><br/>"
        info_text += f"Property: <b>{report_data['property_name']}</b>"
        story.append(Paragraph(info_text, info_style))

        # Generate content based on report type
        if report_type == 'financial':
            self._add_financial_pdf_content(story, report_data, styles)
        elif report_type == 'vendor':
            self._add_vendor_pdf_content(story, report_data, styles)
        elif report_type == 'guest':
            self._add_guest_pdf_content(story, report_data, styles)

        # Add footer with Nestafar branding
        story.append(Spacer(1, 40))

        # Add separator line
        separator_style = ParagraphStyle(
            'SeparatorStyle',
            parent=styles['Normal'],
            fontSize=1,
            spaceAfter=15,
            alignment=TA_CENTER
        )
        separator_line = f'<para><font color="{nestafar_primary}">{"_" * 80}</font></para>'
        story.append(Paragraph(separator_line, separator_style))

        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=9,
            fontName='Helvetica',
            alignment=TA_CENTER,
            textColor=nestafar_primary,
            spaceAfter=5
        )

        # Enhanced footer with branding
        footer_text = f"<b>Generated by NESTAFAR</b><br/>"
        footer_text += f"{report_data['generated_at'].strftime('%B %d, %Y at %I:%M %p')}<br/>"
        footer_text += f"<font size='7' color='black'>Simplifying Tourism</font>"
        story.append(Paragraph(footer_text, footer_style))

        # Build PDF
        doc.build(story)
        buffer.seek(0)

        # Create HTTP response with descriptive filename
        response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
        property_name_clean = report_data['property_name'].replace(' ', '_').replace('/', '_')
        filename = f"Nestafar_{report_type.title()}_Report_{property_name_clean}_{timezone.now().strftime('%Y%m%d_%H%M')}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response

    def _add_financial_pdf_content(self, story, data, styles):
        """Add financial report content to PDF"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.styles import ParagraphStyle
        from reportlab.lib.enums import TA_LEFT
        from reportlab.lib.units import inch

        # Define Nestafar brand colors
        nestafar_primary = colors.Color(0.129, 0.620, 0.706)  # #219EB4

        # Section header style
        section_style = ParagraphStyle(
            'SectionHeader',
            parent=styles['Heading2'],
            fontSize=14,
            fontName='Helvetica-Bold',
            textColor=nestafar_primary,
            spaceAfter=10,
            spaceBefore=12,
            alignment=TA_LEFT
        )

        # Monthly Earnings Table
        story.append(Paragraph("Monthly Earnings Overview (INR)", section_style))

        earnings_data = [['Month', 'Total Earning', 'Booking Earnings' , 'Service Earnings', 'Commission']]
        for item in data['monthly_earnings']:
            earnings_data.append([
                f"{item['month']} {item['year']}",
                f"{item['total_earnings']:.2f}",
                f"{item['booking_earnings']:.2f}",
                f"{item['service_earnings']:.2f}",
                f"{item['commission_earned']:.2f}"
            ])

        earnings_table = Table(earnings_data, colWidths=[1.2*inch, 1.4*inch, 1.4*inch, 1.4*inch, 1.2*inch])
        earnings_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), nestafar_primary),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('TOPPADDING', (0, 0), (-1, 0), 12),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),
            ('GRID', (0, 0), (-1, -1), 1, nestafar_primary),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('LEFTPADDING', (0, 0), (-1, -1), 6),
            ('RIGHTPADDING', (0, 0), (-1, -1), 6)
        ]))
        story.append(earnings_table)
        story.append(Spacer(1, 20))

        # Commission Breakdown Table
        story.append(Spacer(1, 20))
        story.append(Paragraph("Commission Breakdown by Service", section_style))

        commission_data = [['Service Type', 'Orders', 'Revenue ', 'Commission Rate (%)', 'Commission Earned']]
        for item in data['commission_breakdown']:
            commission_data.append([
                item['service_type'],
                str(item['total_orders']),
                f"{item['total_revenue']:.2f}",
                f"{item['commission_rate']:.1f}",
                f"{item['commission_earned']:.2f}"
            ])

        commission_table = Table(commission_data)
        commission_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), nestafar_primary),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
            ('TOPPADDING', (0, 0), (-1, 0), 15),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),
            ('GRID', (0, 0), (-1, -1), 1, nestafar_primary),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8)
        ]))
        story.append(commission_table)

        # Summary
        story.append(Spacer(1, 20))
        story.append(Paragraph("Year-to-Date Summary", section_style))
        summary_text = f"Total Revenue: {data['total_revenue_ytd']:.2f} INR<br/>"
        summary_text += f"Total Profit: {data['total_profit_ytd']:.2f} INR<br/>"
        summary_text += f"Total Orders: {data['total_orders_ytd']}<br/>"
        if data['total_orders_ytd'] > 0:
            avg_order_value = float(data['total_revenue_ytd']) / data['total_orders_ytd']
            summary_text += f"Average Order Value: {avg_order_value:.2f} INR<br/>"
        story.append(Paragraph(summary_text, styles['Normal']))

    def _add_vendor_pdf_content(self, story, data, styles):
        """Add vendor report content to PDF"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.styles import ParagraphStyle
        from reportlab.lib.enums import TA_LEFT

        # Define Nestafar brand colors
        nestafar_primary = colors.Color(0.129, 0.620, 0.706)  # #219EB4

        # Section header style
        section_style = ParagraphStyle(
            'SectionHeader',
            parent=styles['Heading2'],
            fontSize=14,
            fontName='Helvetica-Bold',
            textColor=nestafar_primary,
            spaceAfter=10,
            spaceBefore=12,
            alignment=TA_LEFT
        )

        # Vendor Summary
        story.append(Paragraph("Vendor Performance Overview", section_style))
        summary_text = f"Total Vendors: {data['total_vendors']}<br/>"
        summary_text += f"Total Outstanding Dues: {data['total_outstanding_dues']:.2f} INR<br/><br/>"

        # Add vendors by service breakdown
        summary_text += "<b>Vendors by Service Type:</b><br/>"
        for service_type, count in data['vendors_by_service'].items():
            if count > 0:
                summary_text += f"• {service_type}: {count}<br/>"

        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(Spacer(1, 25))

        # Top Vendors Table
        story.append(Paragraph("Top Performing Vendors", section_style))

        vendor_data = [['Vendor Name', 'Service Type', 'Orders', 'Revenue', 'Outstanding Dues']]
        for vendor in data['top_vendors']:
            vendor_data.append([
                vendor['vendor_name'],
                vendor['service_type'],
                str(vendor['total_orders']),
                f"{vendor['total_revenue']:.2f}",
                f"{vendor['outstanding_dues']:.2f}"
            ])

        vendor_table = Table(vendor_data)
        vendor_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), nestafar_primary),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
            ('TOPPADDING', (0, 0), (-1, 0), 15),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),
            ('GRID', (0, 0), (-1, -1), 1, nestafar_primary),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8)
        ]))
        story.append(vendor_table)

    def _add_guest_pdf_content(self, story, data, styles):
        """Add guest report content to PDF"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.styles import ParagraphStyle
        from reportlab.lib.enums import TA_LEFT
        from reportlab.lib.units import inch

        # Define Nestafar brand colors
        nestafar_primary = colors.Color(0.129, 0.620, 0.706)  # #219EB4

        # Section header style
        section_style = ParagraphStyle(
            'SectionHeader',
            parent=styles['Heading2'],
            fontSize=14,
            fontName='Helvetica-Bold',
            textColor=nestafar_primary,
            spaceAfter=10,
            spaceBefore=12,
            alignment=TA_LEFT
        )

        # Guest Summary
        story.append(Paragraph("Guest Analytics Overview", section_style))
        summary_text = f"Total Unique Guests: {data['total_unique_guests']}<br/>"
        summary_text += f"Average Occupancy Rate: {data['average_occupancy_rate']:.2f}%<br/>"
        summary_text += f"Total Guest Orders: {data['total_guest_orders']}<br/><br/>"

        # Add guest ordering metrics
        summary_text += "<b>Guest Ordering Behavior:</b><br/>"
        for metric in data['ordering_metrics']:
            if metric['total_orders'] > 0:
                summary_text += f"• {metric['service_type']}: {metric['total_orders']} orders, "
                summary_text += f"{metric['ordering_rate']:.1f}% ordering rate<br/>"

        story.append(Paragraph(summary_text, styles['Normal']))
        story.append(Spacer(1, 25))

        # Top Guests Table
        story.append(Paragraph("Top Guests by Spending", section_style))

        guest_data = [['Guest Name', 'Total Stays', 'Total Spending (\u20B9)', 'Avg Stay Duration (Days)']]
        for guest in data['top_guests']:
            guest_data.append([
                guest['guest_name'],
                str(guest['total_stays']),
                f"{guest['total_spending']:.2f}",
                f"{guest['average_stay_duration']:.1f}"
            ])

        guest_table = Table(guest_data)
        guest_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), nestafar_primary),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
            ('TOPPADDING', (0, 0), (-1, 0), 15),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),
            ('GRID', (0, 0), (-1, -1), 1, nestafar_primary),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8)
        ]))
        story.append(guest_table)
