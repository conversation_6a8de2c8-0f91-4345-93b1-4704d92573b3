from pytest_bdd import scenario, given, then, when
from food.models import *
from food.tests import foodtest

@scenario("features/tests/food_order_test.feature")
def test_manage_food_order(client, user):
    pass

@given("I am an authenticated user with a food cart")
def create_food_cart_with_items(client, user, service_factory=foodtest.create_food_service_item):
    # Create a food cart with some items
    cart = FoodCart.objects.create(guest=user.guest)
    item1 = service_factory()
    item2 = service_factory()
    FoodCartItems.objects.create(cart=cart, item=item1, quantity=2)
    FoodCartItems.objects.create(cart=cart, item=item2, quantity=1)
    return cart

@when("I create a food order")
def create_food_order(client, user, cart):
    data = {"cart": cart.id}
    response = client.post("/api/food-orders/", data=data, format="json")
    response.json()  # trigger data parsing

@then("the response status code is 201")
def check_create_order_status_code(response):
    assert response.status_code == 201

@then("And a new food order is created with the cart items")
def check_created_order(client, cart):
    order = FoodOrder.objects.get(cart=cart)
    # Assert order details (cart, service, status, etc.)
    assert order.cart == cart
    # Add assertions for other expected order data

@scenario("features/tests/food_order_test.feature")
def test_get_food_order_details(client):
    pass

@given("a food order exists with ID '<order_id>'")
def get_food_order(client, order_id):
    order = FoodOrder.objects.get(pk=order_id)
    return order

@when("I retrieve the food order details")
def retrieve_food_order(client, order):
    response = client.get(f"/api/food-orders/{order.id}/")
    response.json()  # trigger data parsing

@then("the response status code is 200")
def check_get_order_details_status_code(response):
    assert response.status_code == 200

@then("And the response data contains the food order details and associated items")
def check_order_details(response, order):
    data = response.json()
    # Assert order details (id, cart, service, status, etc.)
    assert data["id"] == order.id
    assert data["cart"] == order.cart.id
    # Add assertions for other expected order data and associated items
