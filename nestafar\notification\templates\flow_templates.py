# WhatsApp Template Functions for All Flows

def signup_successful_template(partner_name, property_name, **kwargs):
    """Welcome message after successful signup"""
    subject = f"Welcome to Nestafar! 🎉"
    message = f"""Hello {partner_name}! 🌟

Welcome to Nestafar! We're thrilled to have {property_name} on our platform.

To get started and maximize your bookings, please complete your property setup:
📝 Add property details
📸 Upload property photos  
🏠 Add room information
🖼️ Upload room photos
🛎️ Set up services

Complete your setup to start receiving bookings immediately!

Need help? Contact our support team anytime."""
    return subject, message


def onboarding_reminder_template(partner_name, property_name, missing_steps, completion_percentage, **kwargs):
    """Daily reminder for incomplete onboarding"""
    subject = f"Complete Your {property_name} Setup - {completion_percentage}% Done"
    missing_list = "• " + "\n• ".join(missing_steps)
    
    message = f"""Hi {partner_name}! 👋

Your {property_name} setup is {completion_percentage}% complete. You're almost there!

Still needed:
{missing_list}

Complete your setup today to:
✅ Start receiving bookings
💰 Increase revenue
📈 Improve visibility

Complete now: portal.nestafar.com"""
    return subject, message


def onboarding_completed_template(partner_name, property_name, **kwargs):
    """Onboarding completion confirmation"""
    subject = f"🎉 {property_name} Setup Complete!"
    message = f"""Congratulations {partner_name}! 🌟

Your {property_name} is now live on Nestafar! 🚀

✅ Property details added
✅ Photos uploaded
✅ Rooms configured
✅ Services activated

You're ready to:
• Receive bookings
• Serve guests
• Earn commissions

Monitor your performance: dashboard.nestafar.com

Welcome to the Nestafar family! 🏨✨"""
    return subject, message


def precheckin_reminder_template(guest_name, property_name, checkin_date, hours_remaining, **kwargs):
    """Reminder to complete pre-checkin"""
    subject = f"Complete Pre-Check-in for {property_name}"
    message = f"""Hi {guest_name}! 🏨

Your check-in at {property_name} is in {hours_remaining} hours ({checkin_date}).

⚠️ Please complete pre-check-in to avoid delays:
📋 Upload ID proof
📍 Confirm address
✍️ Fill guest details

Complete now: guest.nestafar.com

Incomplete pre-check-ins may be auto-cancelled."""
    return subject, message


def precheckin_cancellation_warning_template(guest_name, property_name, hours_remaining, **kwargs):
    """Warning before auto-cancellation"""
    subject = f"⚠️ Pre-Check-in Auto-Cancellation Warning"
    message = f"""URGENT: Hi {guest_name}! ⚠️

Your pre-check-in for {property_name} will be auto-cancelled in {hours_remaining} hours if not completed.

Complete immediately:
📋 Upload ID proof
📍 Confirm address  
✍️ Fill guest details

Don't lose your booking!
Complete now: guest.nestafar.com"""
    return subject, message


def room_allotment_template(guest_name, property_name, room_number, checkin_date, **kwargs):
    """Room allotment notification"""
    subject = f"Room {room_number} Allocated - {property_name}"
    message = f"""Great news {guest_name}! 🏠

Your room is ready at {property_name}:

🏠 Room: {room_number}
📅 Check-in: {checkin_date}
🏨 Property: {property_name}

Save this message for easy reference during check-in.

Looking forward to hosting you! 🌟"""
    return subject, message




def dinner_reminder_template(guest_name, property_name, **kwargs):
    """Daily 8 PM dinner reminder"""
    subject = f"🍽️ Dinner Time at {property_name}"
    message = f"""Hi {guest_name}! 🌅

It's dinner time! Don't miss out on delicious meals available through Nestafar:

🍽️ Fresh, hot meals delivered to your room
⏰ Quick delivery
💰 Best prices
⭐ Top-rated restaurants

Order now:
📱 Scan QR code in your room
🌐 Visit: guest.nestafar.com

Bon appétit! 🍽️✨"""
    return subject, message


def order_created_hotel_notification_template(hotel_name, order_id, guest_name, room_number, vendor_name, order_total, **kwargs):
    """Notify hotel when order is created"""
    subject = f"🛎️ New Order #{order_id} - Room {room_number}"
    message = f"""New Order Alert! 🛎️

Order Details:
📋 Order ID: #{order_id}
👤 Guest: {guest_name}
🏠 Room: {room_number}
🍽️ Vendor: {vendor_name}
💰 Total: ₹{order_total}

The order has been sent to the vendor for confirmation.

Monitor order status: dashboard.nestafar.com"""
    return subject, message


def order_created_vendor_notification_template(vendor_name, order_id, guest_name, room_number, items_summary, order_total, **kwargs):
    """Notify vendor when order is created"""
    subject = f"🆕 New Order #{order_id} - Room {room_number}"
    message = f"""New Order Received! 🛎️

Order Details:
📋 Order ID: #{order_id}
👤 Guest: {guest_name}
🏠 Room: {room_number}
🛍️ Items: {items_summary}
💰 Total: ₹{order_total}

⏰ Please accept or reject within 5 minutes to maintain your rating.

Manage order: vendor.nestafar.com"""
    return subject, message


def vendor_order_reminder_template(vendor_name, order_id, minutes_pending, **kwargs):
    """15-minute reminder to vendor for order status update"""
    subject = f"⏰ Order #{order_id} Status Update Required"
    message = f"""Reminder: {vendor_name} ⏰

Order #{order_id} has been pending for {minutes_pending} minutes.

Please update the order status:
✅ Accept & provide ETA
❌ Reject with reason
🍳 Mark as preparing
✅ Mark as completed

Update now: vendor.nestafar.com

Timely updates improve your rating! ⭐"""
    return subject, message


def order_accepted_guest_template(guest_name, order_id, vendor_name, estimated_time, **kwargs):
    """Notify guest when order is accepted"""
    subject = f"✅ Order #{order_id} Accepted!"
    message = f"""Great news {guest_name}! ✅

Your order has been accepted:

📋 Order ID: #{order_id}
🍽️ Vendor: {vendor_name}
⏰ Estimated Time: {estimated_time} minutes
🚀 Status: Accepted & Preparing

We'll notify you when it's ready for delivery!

Track order: guest.nestafar.com"""
    return subject, message


def order_preparing_guest_template(guest_name, order_id, vendor_name, estimated_time, **kwargs):
    """Notify guest when order is being prepared"""
    subject = f"🍳 Order #{order_id} Being Prepared"
    message = f"""Hi {guest_name}! 🍳

Your order is now being prepared:

📋 Order ID: #{order_id}
🍽️ Vendor: {vendor_name}
⏰ ETA: {estimated_time} minutes
👨‍🍳 Status: Being Prepared

Almost ready! We'll notify you when it's out for delivery.

Track order: guest.nestafar.com"""
    return subject, message


def order_completed_guest_template(guest_name, order_id, vendor_name, order_total, room_number, **kwargs):
    """Notify guest when order is completed"""
    subject = f"✅ Order #{order_id} Delivered!"
    message = f"""Hi {guest_name}! ✅

Your order has been successfully delivered to Room {room_number}:

📋 Order ID: #{order_id}
🍽️ Vendor: {vendor_name}
💰 Total: ₹{order_total}
📍 Delivered to: Room {room_number}

This amount has been added to your hotel bill.

Enjoy your meal! Rate your experience: guest.nestafar.com 🌟"""
    return subject, message


def checkout_bill_template(guest_name, property_name, checkout_date, total_amount, **kwargs):
    """Send bill after checkout"""
    subject = f"📄 Your Bill from {property_name}"
    message = f"""Thank you {guest_name}! 🙏

Your stay at {property_name} has ended.

📄 Final Bill Details:
📅 Checkout Date: {checkout_date}
💰 Total Amount: ₹{total_amount}

📎 Detailed bill PDF attached.

🌟 Please rate your experience and help us improve:
Review link: review.nestafar.com

Thank you for choosing Nestafar! 
We hope to host you again soon! 🏨✨"""
    return subject, message


def review_request_template(guest_name, property_name, **kwargs):
    """Request review after checkout"""
    subject = f"🌟 Rate Your Stay at {property_name}"
    message = f"""Hi {guest_name}! 🌟

How was your stay at {property_name}?

Your feedback helps us improve:
⭐ Rate your experience (1-5 stars)
💬 Share your thoughts
📝 Suggest improvements

Your review takes just 2 minutes and helps future guests!

Leave review: review.nestafar.com

Thank you for staying with Nestafar! 🏨✨"""
    return subject, message



def guest_arrived_welcome_template(guest_name, property_name, room_number, **kwargs):
    """Welcome message when guest arrives"""
    subject = f"🎉 Welcome to {property_name}!"
    message = f"""Welcome {guest_name}! 🎉

You've successfully checked into Room {room_number} at {property_name}.

📱 Access services instantly:
• Scan QR codes around the hotel
• Visit: guest.nestafar.com
• Order food, taxi, laundry & more

Need assistance? Contact the front desk anytime.

Enjoy your stay! 🏨✨"""
    return subject, message


def new_service_available_template(guest_name, service_name, service_type, property_name, **kwargs):
    """Notify guest about new service availability"""
    subject = f"🆕 New {service_type} Available!"
    message = f"""Great news {guest_name}! 🆕

A new {service_type} service is now available at {property_name}:

🍽️ {service_name}

Explore the new menu and place your order:
📱 Scan QR codes in your room
🌐 Visit: guest.nestafar.com

Order now and enjoy! 🛎️✨"""
    return subject, message


def order_confirmed_template(guest_name, service_type, order_id, order_items, total_amount, delivery_time, **kwargs):
    """Notify guest when order is confirmed"""
    subject = f"✅ Order #{order_id} Confirmed"
    message = f"""Hi {guest_name}! ✅

Your {service_type} order has been confirmed:

📋 Order ID: #{order_id}
🛍️ Items: {order_items}
💰 Total: ₹{total_amount}
⏰ Delivery Time: {delivery_time}

Your order is being prepared. We'll notify you when it's ready!

Track status: guest.nestafar.com"""
    return subject, message


def order_ready_template(guest_name, service_type, order_id, status, total_amount, instructions, **kwargs):
    """Notify guest when order is ready"""
    subject = f"🚀 Order #{order_id} Ready!"
    message = f"""Hi {guest_name}! 🚀

Your {service_type} order is ready:

📋 Order ID: #{order_id}
📦 Status: {status}
💰 Total: ₹{total_amount}

📝 Instructions: {instructions}

Enjoy your order! 🌟"""
    return subject, message


def vendor_new_order_template(vendor_name, order_id, guest_name, property_name, order_items, total_amount, **kwargs):
    """Notify vendor about new order"""
    subject = f"🆕 New Order #{order_id}"
    message = f"""New Order Alert! 🛎️

Hi {vendor_name},

📋 Order ID: #{order_id}
👤 Guest: {guest_name}
🏨 Property: {property_name}
🛍️ Items: {order_items}
💰 Total: ₹{total_amount}

⏰ Please accept/reject within 5 minutes.

Manage order: vendor.nestafar.com"""
    return subject, message


def service_hidden_notification_template(guest_name, service_name, property_name, **kwargs):
    """Notify guests when service is temporarily unavailable"""
    subject = f"⚠️ {service_name} Temporarily Unavailable"
    message = f"""Hi {guest_name}! ⚠️

{service_name} at {property_name} is temporarily unavailable.

Don't worry! Other services are still available:
📱 Check available options: guest.nestafar.com
🛎️ Contact front desk for alternatives

We'll notify you when {service_name} is back online!

Thank you for your understanding. 🙏"""
    return subject, message


def service_restored_notification_template(guest_name, service_name, property_name, **kwargs):
    """Notify guests when service is restored"""
    subject = f"✅ {service_name} Now Available!"
    message = f"""Good news {guest_name}! ✅

{service_name} at {property_name} is now available again!

📱 Place your order: guest.nestafar.com
🛎️ Enjoy our full range of services

Thank you for your patience! 🙏✨"""
    return subject, message


def weekly_report_template(partner_name, property_name, week_start, week_end, reservations, occupancy_rate, avg_orders, gmv, commission, recommendations, **kwargs):
    """Weekly business report for partners"""
    subject = f"📊 Weekly Report: {property_name} ({week_start} - {week_end})"
    
    # Format recommendations list
    if isinstance(recommendations, list):
        recommendations_text = "\n".join([f"• {rec}" for rec in recommendations]) if recommendations else "• Keep up the great work!"
    else:
        recommendations_text = str(recommendations) if recommendations else "• Keep up the great work!"
    
    message = f"""Weekly Business Report 📊

Property: {property_name}
Period: {week_start} to {week_end}

📈 Performance Metrics:
🏨 Reservations: {reservations}
🏠 Occupancy Rate: {occupancy_rate}%
🛎️ Avg Orders/Guest: {avg_orders}
💰 GMV: ₹{gmv:,.2f}
💰 Commission Earned: ₹{commission:,.2f}

🎯 Recommendations:
{recommendations_text}

📱 View detailed analytics: dashboard.nestafar.com

Keep growing with Nestafar! 🚀"""
    return subject, message
