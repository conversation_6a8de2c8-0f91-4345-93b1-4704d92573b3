from rest_framework import viewsets
from rest_framework.exceptions import ValidationError
from rest_framework.decorators import action
from nestafar.responses import (
    SuccessResponse, BadRequestResponse,
    NotFoundResponse
)
from django.db import transaction, models
import logging
from datetime import timedelta, datetime
from django.utils import timezone
from core.permissions import PropertyPermission
from stay.models import Property
from .models import RatePlan, RoomType, Calendar, HotelOTAIntegration, AvailabilityLog
from pms.tasks.aiosell_tasks import bulk_sync_calendar_entries_to_aiosell
from .serializers import RatePlanSerializer, RoomTypeSerializer
from django.db.models import Avg, Sum

logger = logging.getLogger(__name__)


class RoomTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only endpoint to list room types for the authenticated property.

    GET /pms/roomtypes/ -> List all room types (id, name, max_occupancy etc.)
    This helps the partner obtain the room_type UUID/ID for other PMS operations.
    """
    serializer_class = RoomTypeSerializer
    permission_classes = [PropertyPermission]

    def get_queryset(self):  # pragma: no cover - simple filter
        return RoomType.objects.filter(hotel=self.request.property).order_by('created_at')

    def get_serializer_context(self):
        ctx = super().get_serializer_context()
        if getattr(self.request, 'property', None) is not None:
            ctx['hotel'] = self.request.property
        return ctx

class RatePlanViewSet(viewsets.ModelViewSet):
    queryset = RatePlan.objects.all()
    serializer_class = RatePlanSerializer
    permission_classes = [PropertyPermission]

    MAX_CALENDAR_DAYS = 365  # inclusive maximum span that can be auto-generated (365 days = 1 year)

    def get_queryset(self):
        return RatePlan.objects.filter(room_type__hotel=self.request.property)

    def get_serializer_context(self):  # ensure 'hotel' is available for HotelContextMixin
        ctx = super().get_serializer_context()
        if getattr(self.request, 'property', None) is not None:
            ctx['hotel'] = self.request.property
        return ctx

    @transaction.atomic
    def perform_create(self, serializer):
        valid_from = serializer.validated_data.get('valid_from')
        valid_to = serializer.validated_data.get('valid_to')
        if valid_from is None or valid_to is None:
            raise ValidationError({'non_field_errors': 'valid_from and valid_to are required.'})
        # Fields are DateField -> compare directly; if ever DateTimeField, ensure both same awareness
        if isinstance(valid_from, datetime) and isinstance(valid_to, datetime):
            if (valid_from.tzinfo is None) != (valid_to.tzinfo is None):
                raise ValidationError({'non_field_errors': 'Both dates must be either timezone-aware or naive.'})
        if valid_to < valid_from:
            raise ValidationError({'valid_to': 'valid_to must be on or after valid_from.'})
        # Prevent past date creation
        today = timezone.localdate()
        if valid_from < today:
            raise ValidationError({'valid_from': 'valid_from cannot be in the past.'})
        if valid_to < today:
            raise ValidationError({'valid_to': 'valid_to cannot be in the past.'})
        day_span = (valid_to - valid_from).days + 1  # inclusive
        if day_span > self.MAX_CALENDAR_DAYS:
            raise ValidationError({
                'valid_to': f'Validity period spans {day_span} days which exceeds limit of {self.MAX_CALENDAR_DAYS} days.'
            })
        rate_plan = serializer.save()        
        room_type = rate_plan.room_type
        current = rate_plan.valid_from
        while current <= rate_plan.valid_to:
            # Ensure a calendar row exists for this specific (room_type, rate_plan, date)
            cal = (
                Calendar.objects.select_for_update()
                .filter(room_type=room_type, rate_plan=rate_plan, date=current)
                .first()
            )
            if cal is None:
                # Create fresh entry for this rate plan and date (do not overwrite other plans)
                Calendar.objects.create(
                    room_type=room_type,
                    date=current,
                    rate_plan=rate_plan,
                    daily_rate=rate_plan.base_rate,
                )
            else:
                # Update daily_rate if different
                if cal.daily_rate != rate_plan.base_rate:
                    cal.daily_rate = rate_plan.base_rate
                    cal.save(update_fields=['daily_rate'])
            # Advance to next day
            current += timedelta(days=1)

        # Trigger async inventory push for the new/updated date range (next 30 days only to limit load)
        hotel_id = str(room_type.hotel_id)
        transaction.on_commit(lambda: bulk_sync_calendar_entries_to_aiosell.delay(hotel_id=hotel_id, date_range_days=30))

    @transaction.atomic
    def perform_update(self, serializer):
        rate_plan = self.get_object()
        old_valid_from = rate_plan.valid_from
        old_valid_to = rate_plan.valid_to
        
        # Get new values from serializer
        new_valid_from = serializer.validated_data.get('valid_from', old_valid_from)
        new_valid_to = serializer.validated_data.get('valid_to', old_valid_to)
        
        # Validate new date range
        if new_valid_from is None or new_valid_to is None:
            raise ValidationError({'non_field_errors': 'valid_from and valid_to are required.'})
        if new_valid_to < new_valid_from:
            raise ValidationError({'valid_to': 'valid_to must be on or after valid_from.'})
        if isinstance(new_valid_from, datetime) and isinstance(new_valid_to, datetime):
            if (new_valid_from.tzinfo is None) != (new_valid_to.tzinfo is None):
                raise ValidationError({'non_field_errors': 'Both dates must be either timezone-aware or naive.'})
        # Prevent past dates on update
        today = timezone.localdate()
        if new_valid_from < today:
            raise ValidationError({'valid_from': 'valid_from cannot be in the past.'})
        if new_valid_to < today:
            raise ValidationError({'valid_to': 'valid_to cannot be in the past.'})
        
        # Check span limit
        day_span = (new_valid_to - new_valid_from).days + 1  # inclusive
        if day_span > self.MAX_CALENDAR_DAYS:
            raise ValidationError({
                'valid_to': f'Validity period spans {day_span} days which exceeds limit of {self.MAX_CALENDAR_DAYS} days.'
            })
        
        # Save the updated rate plan
        updated_rate_plan = serializer.save()
        room_type = updated_rate_plan.room_type
        
        # Handle calendar adjustments only if dates changed
        if old_valid_from != new_valid_from or old_valid_to != new_valid_to:
            # Remove calendar entries that are outside the new date range
            Calendar.objects.filter(
                room_type=room_type,
                rate_plan=rate_plan,
                date__lt=new_valid_from
            ).delete()
            
            Calendar.objects.filter(
                room_type=room_type,
                rate_plan=rate_plan,
                date__gt=new_valid_to
            ).delete()
            
            # Add new calendar entries for extended date range
            current = new_valid_from
            while current <= new_valid_to:
                # Ensure a calendar entry exists for this specific (room_type, rate_plan, date)
                cal = Calendar.objects.select_for_update().filter(
                    room_type=room_type,
                    rate_plan=updated_rate_plan,
                    date=current
                ).first()

                if cal is None:
                    # Create fresh entry for new date for this rate plan (do not overwrite other plans)
                    Calendar.objects.create(
                        room_type=room_type,
                        date=current,
                        rate_plan=updated_rate_plan,
                        daily_rate=updated_rate_plan.base_rate,
                    )
                else:
                    # Update existing entry's daily rate if needed
                    if cal.daily_rate != updated_rate_plan.base_rate:
                        cal.daily_rate = updated_rate_plan.base_rate
                        cal.save(update_fields=['daily_rate'])

                current = current + timedelta(days=1)

        # Update base_rate for existing calendar entries if base_rate changed
        old_base_rate = rate_plan.base_rate
        new_base_rate = serializer.validated_data.get('base_rate', old_base_rate)
        if new_base_rate != old_base_rate:
            Calendar.objects.filter(
                room_type=room_type,
                rate_plan=updated_rate_plan
            ).update(daily_rate=updated_rate_plan.base_rate)

        # Trigger async inventory sync (limited date range)
        hotel_id = str(room_type.hotel_id)
        transaction.on_commit(lambda: bulk_sync_calendar_entries_to_aiosell.delay(hotel_id=hotel_id, date_range_days=30))
class AvailabilityViewSet(viewsets.ViewSet):
    permission_classes = [PropertyPermission]

    def list(self, request):
        # Aggregate availability by room type and date
        room_types = RoomType.objects.filter(hotel=request.property)
        entries = Calendar.objects.filter(
            room_type__in=room_types
        ).values(
            'room_type', 'date'
        ).annotate(
            total_available_rooms=Sum('available_rooms'),
            daily_rate=Avg('daily_rate')  # Use average if multiple rates exist
        ).order_by('date', 'room_type')

        results = [
            {
                'room_type': str(entry['room_type']),
                'date': entry['date'],
                'available_rooms': entry['total_available_rooms'] or 0,
                'daily_rate': entry['daily_rate'],
            }
            for entry in entries
        ]
        return SuccessResponse(data={'results': results})        

    @action(methods=['post'], detail=False)
    @transaction.atomic
    def distribute(self, request):
        # Distribute availability evenly across OTAs (placeholder)
        if not hasattr(request, 'property') or request.property is None:
            return NotFoundResponse(message='Property not found')
        property_obj: Property = request.property
        # Fetch active integrations once
        integrations = list(HotelOTAIntegration.objects.filter(hotel=property_obj, is_active=True))
        if not integrations:
            return SuccessResponse(message='No active OTA integrations to distribute availability.')

        today = timezone.now().date()
        # Aggregate availability per room_type for today (extend logic as needed for date ranges)
        aggregates = (
            Calendar.objects.filter(room_type__hotel=property_obj, date=today)
            .values('room_type')
            .annotate(total=models.Sum('available_rooms'))
        )

        to_create = []
        for agg in aggregates:
            total_available = agg['total'] or 0
            room_type_id = agg['room_type']

            # Distribute rooms proportionally (or based on OTA-specific allocation strategy)
            rooms_per_ota = total_available // len(integrations) if integrations else 0
            remainder    = total_available % len(integrations) if integrations else 0

            for integ in integrations:
                # Give one extra room to the first OTAs if there's a remainder
                allocated_rooms = rooms_per_ota + (1 if remainder > 0 else 0)
                remainder = max(0, remainder - 1)

                to_create.append(AvailabilityLog(
                    hotel=property_obj,
                    room_type_id=room_type_id,
                    ota_platform=integ.ota_platform,
                    date=today,
                    available_rooms=allocated_rooms,
                    sync_status='pending'
                ))

        if to_create:
            AvailabilityLog.objects.bulk_create(to_create)
        else:
            return SuccessResponse(message='No availability entries to distribute today.')
        return SuccessResponse(message=f"Availability distribution initiated for {property_obj.name}.")

class PolicyViewSet(viewsets.ViewSet):
    permission_classes = [PropertyPermission]

    def retrieve(self, request, pk=None):
        if not hasattr(request, 'property') or request.property is None:
            return NotFoundResponse(message='Property not found')
        # Source: Property.policies (default: empty dict)
        policies = getattr(request.property, 'policies', {}) or {}
        return SuccessResponse(data={'policies': policies})        

    def update(self, request, pk=None):
        if not hasattr(request, 'property') or request.property is None:
            return NotFoundResponse(message='Property not found')

        prop: Property = request.property
        policies = request.data.get('policies', {})

        # Validate policies is a dictionary
        if not isinstance(policies, dict):
            return BadRequestResponse(message='Policies must be a dictionary')

        prop.policies = policies
        prop.save(update_fields=['policies'])  # Save the updated policies to the database       
        return SuccessResponse(message='Policies updated successfully.')