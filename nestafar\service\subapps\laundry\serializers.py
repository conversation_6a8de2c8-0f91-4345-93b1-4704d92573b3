from .models import LaundryService, LaundryServiceItem, LaundryOrderItem, LaundryCart, LaundryCartItems, LaundryOrder
from rest_framework.serializers import ModelSerializer, SerializerMethodField, ValidationError

from ...models import ServicePartner


class LaundryServiceListSerializer(ModelSerializer):
    n_items = SerializerMethodField(required=False)

    def get_n_items(self, obj):
        return obj.service_items.count()

    class Meta:
        model = LaundryService
        fields = '__all__'


class LaundryServiceCreateSerializer(ModelSerializer):
    class Meta:
        model = LaundryService
        fields = '__all__'


class LaundryServiceItemListSerializer(ModelSerializer):
    class Meta:
        model = LaundryServiceItem
        fields = '__all__'


class LaundryServiceItemSerializer(ModelSerializer):
    def validate(self, attrs):
        addon = attrs.get('addon')
        if addon:
            for addon_item in addon.keys():
                try:
                    addon_price = float(addon[addon_item])
                    addon[addon_item] = addon_price
                except ValueError:
                    raise ValidationError("Invalid price for addon")
        else:
            attrs['addon'] = None
        return attrs

    class Meta:
        model = LaundryServiceItem
        fields = '__all__'


class LaundryServiceItemCardSerializer(LaundryServiceItemListSerializer):
    class Meta:
        model = LaundryServiceItem
        fields = ['name', 'description', 'price', 'addon']


class LaundryOrderItemCardSerializer(ModelSerializer):
    item = LaundryServiceItemCardSerializer()

    class Meta:
        model = LaundryOrderItem
        fields = ['item', 'quantity', 'add_ons']


class LaundryCartItemSerializer(ModelSerializer):
    class Meta:
        model = LaundryCartItems
        fields = ['item', 'quantity', 'add_ons']


class LaundryCartItemListSerializer(ModelSerializer):
    name = SerializerMethodField()

    def get_name(self, obj):
        return obj.item.name

    class Meta:
        model = LaundryCartItems
        fields = ['id', 'name', 'item', 'price', 'quantity', 'add_ons', 'ordered', 'updated_at', 'created_at']


class LaundryCartSerializer(ModelSerializer):
    cart_items = LaundryCartItemListSerializer(many=True)

    class Meta:
        model = LaundryCart
        fields = '__all__'


class ServicePartnerNameSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = ["id", "name", "type_of_service", "phone_number"]


class LaundryOrderSerializer(ModelSerializer):
    order_items = LaundryOrderItemCardSerializer(many=True)
    guest = SerializerMethodField(required=False)
    service_partner = ServicePartnerNameSerializer()

    def get_guest(self, obj):
        return {'id': obj.guest.id,
                'phone_no': obj.guest.user.phone.as_e164,
                'room_no': obj.guest.room.room_no,
                'name': obj.guest.user.name
                }

    class Meta:
        model = LaundryOrder
        fields = '__all__'
