name: Run Pytest

on:
  push:
    branches:
      - main
      
jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.x

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y python3-distutils

      - name: Upgrade pip and setuptools
        run: |
          pip install --upgrade pip setuptools

      - name: Install Python dependencies
        run: pip install -r requirements.txt
    
      - name: Install Redis
        run: sudo apt-get install -y redis-server

      - name: Start Redis
        run: redis-server &

      - name: Run Pytest
        run: pytest

  # deploy:
  #   runs-on: ubuntu-latest
  #   env:
  #     PROJECT_ID: <YOUR_PROJECT_ID>
  #     REGION: <YOUR_REGION>
  #     SERVICE_NAME: newbackend
  #     IMAGE_NAME: newbackend
  #     GCR_IMAGE: gcr.io/$PROJECT_ID/$IMAGE_NAME

  #   steps:
  #   - name: Checkout repository
  #     uses: actions/checkout@v2

  #   - name: Set up Google Cloud SDK
  #     uses: google-github-actions/setup-gcloud@master
  #     with:
  #       service_account_key: ${{ secrets.GCP_SA_KEY }}
  #       project_id: ${{ env.PROJECT_ID }}
  #       export_default_credentials: true

  #   - name: Build and Push Docker image to Google Container Registry
  #     run: |
  #       gcloud builds submit --tag ${{ env.GCR_IMAGE }} .

  #   - name: Deploy to Google Cloud Run
  #     run: |
  #       gcloud run deploy ${{ env.SERVICE_NAME }} \
  #         --image ${{ env.GCR_IMAGE }} \
  #         --platform managed \
  #         --region ${{ env.REGION }} \
  #         --allow-unauthenticated
