Scenario: Creating a food order
  Given I am an authenticated user with a food cart
  When I create a food order
  Then the response status code is 201
  And a new food order is created with the cart items

Scenario Outline: Getting food order details
  Given a food order exists with ID "<order_id>"
  When I retrieve the food order details
  Then the response status code is 200
  And the response data contains the food order details and associated items

  Examples:
    | order_id |
    | 1        |
