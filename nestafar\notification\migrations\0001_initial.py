# Generated by Django 4.2.7 on 2025-07-19 07:37

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('category', models.CharField(choices=[('USER_CHECKIN_INITIATED', 'USER_CHECKIN_INITIATED'), ('USER_CHECKIN', 'USER_CHECKIN'), ('USER_CHECKOUT', 'USER_CHECKOUT'), ('USER_ORDER_ACCEPTED', 'USER_ORDER_ACCEPTED'), ('USER_ORDER_CANCELLED', 'USER_ORDER_CANCELLED'), ('USER_ORDER_COMPLETED', 'USER_ORDER_COMPLETED'), ('PARTNER_ORDER_PLACED', 'PARTNER_ORDER_PLACED'), ('PRE_CHECKIN_CREATED', 'PRE_CHECKIN_CREATED'), ('PRECHECKIN_CONFIRMED', 'PRECHECKIN_CONFIRMED')], max_length=50)),
                ('channel', models.CharField(choices=[('MESSAGE', 'MESSAGE'), ('EMAIL', 'EMAIL'), ('WHATSAPP', 'WHATSAPP'), ('PUSH', 'PUSH')], max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user_type', models.PositiveSmallIntegerField(choices=[(1, 'User'), (2, 'Partner')])),
            ],
        ),
        migrations.CreateModel(
            name='UserNotificationProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_profile', to='core.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='PartnerNotificationProfile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('partner', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_profile', to='core.partnerprofile')),
            ],
        ),
        migrations.CreateModel(
            name='NotificationSubscription',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_active', models.BooleanField(default=True)),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='notification.notification')),
                ('partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.partnerprofile')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.userprofile')),
            ],
        ),
        migrations.CreateModel(
            name='NotificationLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_sent', models.BooleanField(default=False)),
                ('is_read', models.BooleanField(default=False)),
                ('error_msg', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('notification', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='notification.notification')),
                ('partner_notification_profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notification.partnernotificationprofile')),
                ('user_notification_profile', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='notification.usernotificationprofile')),
            ],
        ),
        migrations.CreateModel(
            name='FirebaseDeviceToken',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=255, unique=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='firebase_device_tokens', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
