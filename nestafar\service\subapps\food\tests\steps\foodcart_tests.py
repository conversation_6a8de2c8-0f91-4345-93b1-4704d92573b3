from pytest_bdd import scenario, given, then, when
from food.models import *
from food.tests import foodtest

@scenario("features/food_cart.feature")
def test_manage_food_cart(client, user):
    pass

@given("I am an authenticated user")
def authenticate(client, user):
    foodtest.authenticate_user(client, user)

@given("a food service item exists")
def create_food_service_item(service_factory=foodtest.create_food_service_item):
    item = service_factory()
    return item

@when("I add a food service item to the cart")
def add_food_item_to_cart(client, user, item):
    cart = FoodCart.objects.get(guest=user.guest)
    data = {"item": item.id, "quantity": 1}
    response = client.post(f"/api/food-carts/{cart.id}/items/", data=data, format="json")
    response.json()  # trigger data parsing

@then("the response status code is 201")
def check_add_item_status_code(response):
    assert response.status_code == 201

@then("And the response data contains the added cart item details")
def check_add_item_response(response):
    data = response.json()
    assert data["item"] == data["item"]["id"]
    # Add assertions for other expected cart item data (quantity, price)

@when("I remove a food service item from the cart")
def remove_food_item_from_cart(client, user, item):
    cart = FoodCart.objects.get(guest=user.guest)
    cart_item = FoodCartItems.objects.create(cart=cart, item=item, quantity=1)
    item_id = cart_item.id
    response = client.delete(f"/api/food-carts/{cart.id}/items/{item_id}/")

@then("And the removed item is no longer present in the cart")
def check_remove_item_from_cart(client, user):
    cart = FoodCart.objects.get(guest=user.guest)
    removed_items = FoodCartItems.objects.filter(cart=cart)
    assert len(removed_items) == 0

@when("I try to add an invalid item to the cart")
def add_invalid_item_to_cart(client, user):
    cart = FoodCart.objects.get(guest=user.guest)
    data = {"item": 9999, "quantity": 1}  # Invalid item ID
    response = client.post(f"/api/food-carts/{cart.id}/items/", data=data, format="json")

@then("And the response data contains error messages")
def check_invalid_item_error(response):
    data = response.json()
    assert "item" in data["errors"]
