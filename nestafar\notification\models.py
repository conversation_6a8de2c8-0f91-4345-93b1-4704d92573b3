from django.db import models
from core.models import UserProfile, PartnerProfile
import uuid
from django.core.exceptions import ValidationError
import enum
from phonenumber_field.modelfields import PhoneNumberField
from .templates.templates import (checkin_initiated_template, checkout_template, order_accepted_template,
                                  order_cancelled_template, order_completed_template, partner_order_placed_template,
                                  pre_checkin_confirmed_template, pre_checkin_created_template,
                                  order_placed_template, order_ongoing_template, order_rejected_template,
                                  partner_order_accepted_template, partner_order_ongoing_template,
                                  partner_order_completed_template, partner_order_cancelled_template,
                                  partner_order_rejected_template, precheckin_status_changed_template,
                                  daily_summary_guest_template, daily_summary_partner_template,
                                  welcome_message_template, checkout_reminder_template, checkout_successful_template,
                                  service_request_received_template, service_in_progress_template, service_completed_template,
                                  food_order_placed_template, food_order_confirmed_template, food_order_ready_template
                                )
from .templates.flow_templates import (signup_successful_template, onboarding_reminder_template, onboarding_completed_template,
                                      precheckin_reminder_template, precheckin_cancellation_warning_template, 
                                      room_allotment_template, guest_arrived_welcome_template, checkout_bill_template,
                                      review_request_template, new_service_available_template, dinner_reminder_template,
                                      vendor_order_reminder_template, order_confirmed_template, order_ready_template,
                                      vendor_new_order_template, weekly_report_template, service_hidden_notification_template,
                                      service_restored_notification_template
                                     )
from core.models import User
class NotificationChannel(enum.Enum):
    MESSAGE = 'MESSAGE'
    EMAIL = 'EMAIL'
    WHATSAPP = 'WHATSAPP'
    PUSH = 'PUSH'

NotificationChannelHandler = {
    'PUSH': 'notification.channel.firebase.FirebaseChannel',
    'MESSAGE': 'notification.channel.message.MessageChannel',
    'WHATSAPP': 'notification.channel.whatsapp.WhatsAppChannel',
}

class NotificationCategory(enum.Enum):
    # Existing categories
    USER_CHECKIN_INITIATED = 'USER_CHECKIN_INITIATED'
    USER_CHECKIN = 'USER_CHECKIN'
    USER_CHECKOUT = 'USER_CHECKOUT'
    USER_ORDER_ACCEPTED = 'USER_ORDER_ACCEPTED'
    USER_ORDER_CANCELLED = 'USER_ORDER_CANCELLED'
    USER_ORDER_COMPLETED = 'USER_ORDER_COMPLETED'
    USER_ORDER_PLACED = 'USER_ORDER_PLACED'
    USER_ORDER_ONGOING = 'USER_ORDER_ONGOING'
    USER_ORDER_REJECTED = 'USER_ORDER_REJECTED'
    PARTNER_ORDER_PLACED = 'PARTNER_ORDER_PLACED'
    PARTNER_ORDER_ACCEPTED = 'PARTNER_ORDER_ACCEPTED'
    PARTNER_ORDER_CANCELLED = 'PARTNER_ORDER_CANCELLED'
    PARTNER_ORDER_COMPLETED = 'PARTNER_ORDER_COMPLETED'
    PARTNER_ORDER_ONGOING = 'PARTNER_ORDER_ONGOING'
    PARTNER_ORDER_REJECTED = 'PARTNER_ORDER_REJECTED'
    PRE_CHECKIN_CREATED = 'PRE_CHECKIN_CREATED'
    PRECHECKIN_CONFIRMED = 'PRECHECKIN_CONFIRMED'
    PRECHECKIN_STATUS_CHANGED = 'PRECHECKIN_STATUS_CHANGED'
    DAILY_SUMMARY_GUEST = 'DAILY_SUMMARY_GUEST'
    DAILY_SUMMARY_PARTNER = 'DAILY_SUMMARY_PARTNER'
    
    # New onboarding flow categories
    SIGNUP_SUCCESSFUL = 'SIGNUP_SUCCESSFUL'
    ONBOARDING_REMINDER = 'ONBOARDING_REMINDER'
    ONBOARDING_COMPLETED = 'ONBOARDING_COMPLETED'
    
    # New check-in flow categories
    PRECHECKIN_REMINDER = 'PRECHECKIN_REMINDER'
    PRECHECKIN_CANCELLATION_WARNING = 'PRECHECKIN_CANCELLATION_WARNING'
    ROOM_ALLOTMENT = 'ROOM_ALLOTMENT'
    GUEST_ARRIVED_WELCOME = 'GUEST_ARRIVED_WELCOME'
    CHECKOUT_BILL = 'CHECKOUT_BILL'
    REVIEW_REQUEST = 'REVIEW_REQUEST'
    
    # New service management flow categories
    NEW_SERVICE_AVAILABLE = 'NEW_SERVICE_AVAILABLE'
    DINNER_REMINDER = 'DINNER_REMINDER'
    VENDOR_ORDER_REMINDER = 'VENDOR_ORDER_REMINDER'
    ORDER_CONFIRMED = 'ORDER_CONFIRMED'
    ORDER_READY = 'ORDER_READY'
    VENDOR_NEW_ORDER = 'VENDOR_NEW_ORDER'
    
    # Additional service categories for comprehensive testing
    WELCOME_MESSAGE = 'WELCOME_MESSAGE'
    CHECKOUT_REMINDER = 'CHECKOUT_REMINDER' 
    CHECKOUT_SUCCESSFUL = 'CHECKOUT_SUCCESSFUL'
    SERVICE_REQUEST_RECEIVED = 'SERVICE_REQUEST_RECEIVED'
    SERVICE_IN_PROGRESS = 'SERVICE_IN_PROGRESS'
    SERVICE_COMPLETED = 'SERVICE_COMPLETED'
    FOOD_ORDER_PLACED = 'FOOD_ORDER_PLACED'
    FOOD_ORDER_CONFIRMED = 'FOOD_ORDER_CONFIRMED'
    FOOD_ORDER_READY = 'FOOD_ORDER_READY'
    SERVICE_HIDDEN_NOTIFICATION = 'SERVICE_HIDDEN_NOTIFICATION'
    SERVICE_RESTORED_NOTIFICATION = 'SERVICE_RESTORED_NOTIFICATION'
    
    # New weekly report categories
    WEEKLY_REPORT = 'WEEKLY_REPORT'

    @classmethod
    def from_name(cls, name):
        for category in cls:
            if category.name == name:
                return category
        return None


NotificationTemplates = {
    # Working templates (✅ ACTIVE in WhatsApp Business Manager)
    'USER_CHECKIN_INITIATED': (checkin_initiated_template, {'username':'', 'property_name':'', 'room_no':''}),
    'USER_ORDER_COMPLETED': (order_completed_template, {'username':'','order_id':''}),
    'USER_ORDER_PLACED': (order_placed_template, {'username':'','order_id':''}),
    'PRE_CHECKIN_CREATED': (pre_checkin_created_template, {'guest_name':'', 'property_owner_name':'', 'expected_date':'', 'room_number':'', 'precheckin_link':''}),
    
    # Templates needing WhatsApp Business Manager setup (❌ NOT CONFIGURED)
    'USER_CHECKOUT': (checkout_template, {'username':'', 'property_name':''}),
    'USER_ORDER_ACCEPTED': (order_accepted_template, {'username':'','order_id':'', 'vendor_name':'', 'estimated_time':'', 'total_amount':'', 'vendor_contact':''}),
    'USER_ORDER_CANCELLED': (order_cancelled_template, {'username':'','order_id':'', 'service_type':'', 'reason':'', 'refund_amount':'', 'additional_info':''}),
    'USER_ORDER_ONGOING': (order_ongoing_template, {'username':'','order_id':'', 'vendor_name':'', 'estimated_time':'', 'contact_number':''}),
    'USER_ORDER_REJECTED': (order_rejected_template, {'username':'','order_id':'', 'service_type':'', 'rejection_reason':'', 'refund_amount':''}),
    'PARTNER_ORDER_PLACED': (partner_order_placed_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':''}),
    'PARTNER_ORDER_ACCEPTED': (partner_order_accepted_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':''}),
    'PARTNER_ORDER_ONGOING': (partner_order_ongoing_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':'', 'estimated_completion':''}),
    'PARTNER_ORDER_COMPLETED': (partner_order_completed_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':''}),
    'PARTNER_ORDER_CANCELLED': (partner_order_cancelled_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':''}),
    'PARTNER_ORDER_REJECTED': (partner_order_rejected_template, {'partner_name':'', 'order_id':'', 'room_no':'', 'guest_name':'', 'rejection_reason':'', 'refund_amount':'', 'dashboard_link':''}),
    'PRECHECKIN_CONFIRMED': (pre_checkin_confirmed_template, {'guest_name':'', 'property_owner_name':'', 'expected_date':'', 'room_number':''}),
    'PRECHECKIN_STATUS_CHANGED': (precheckin_status_changed_template, {'guest_name':'', 'property_owner_name':'', 'status':'', 'room_number':''}),
    'DAILY_SUMMARY_GUEST': (daily_summary_guest_template, {'username':'', 'total_orders':'', 'total_spent':'', 'property_name':'', 'most_ordered_service':''}),
    'DAILY_SUMMARY_PARTNER': (daily_summary_partner_template, {'partner_name':'', 'total_orders':'', 'total_revenue':'', 'property_name':''}),
    
    # New flow templates (⚠️ NEED WHATSAPP BUSINESS MANAGER SETUP)
    # Onboarding Flow
    'SIGNUP_SUCCESSFUL': (signup_successful_template, {'partner_name':'', 'property_name':''}),
    'ONBOARDING_REMINDER': (onboarding_reminder_template, {'partner_name':'', 'property_name':'', 'missing_steps':'', 'completion_percentage':''}),
    'ONBOARDING_COMPLETED': (onboarding_completed_template, {'partner_name':'', 'property_name':''}),
    
    # Check-in Flow
    'PRECHECKIN_REMINDER': (precheckin_reminder_template, {'guest_name':'', 'property_name':'', 'checkin_date':'', 'hours_remaining':''}),
    'PRECHECKIN_CANCELLATION_WARNING': (precheckin_cancellation_warning_template, {'guest_name':'', 'property_name':'', 'hours_remaining':''}),
    'ROOM_ALLOTMENT': (room_allotment_template, {'guest_name':'', 'property_name':'', 'room_number':'', 'checkin_date':''}),
    'GUEST_ARRIVED_WELCOME': (guest_arrived_welcome_template, {'guest_name':'', 'property_name':'', 'room_number':''}),
    'CHECKOUT_BILL': (checkout_bill_template, {'guest_name':'', 'property_name':'', 'checkout_date':'', 'total_amount':''}),
    'REVIEW_REQUEST': (review_request_template, {'guest_name':'', 'property_name':''}),
    
    # Service Management Flow
    'NEW_SERVICE_AVAILABLE': (new_service_available_template, {'guest_name':'', 'service_name':'', 'service_type':'', 'property_name':''}),
    'DINNER_REMINDER': (dinner_reminder_template, {'guest_name':'', 'property_name':''}),
    'VENDOR_ORDER_REMINDER': (vendor_order_reminder_template, {'vendor_name':'', 'minutes_pending':'', 'order_id':'', 'total_amount':''}),
    'ORDER_CONFIRMED': (order_confirmed_template, {'guest_name':'', 'service_type':'', 'order_id':'', 'order_items':'', 'total_amount':'', 'delivery_time':''}),
    'ORDER_READY': (order_ready_template, {'guest_name':'', 'service_type':'', 'order_id':'', 'status':'', 'total_amount':'', 'instructions':''}),
    'VENDOR_NEW_ORDER': (vendor_new_order_template, {'vendor_name':'', 'order_id':'', 'guest_name':'', 'property_name':'', 'order_items':'', 'total_amount':''}),
    
    # Service notifications
    'SERVICE_HIDDEN_NOTIFICATION': (service_hidden_notification_template, {'guest_name':'', 'service_name':'', 'property_name':''}),
    'SERVICE_RESTORED_NOTIFICATION': (service_restored_notification_template, {'guest_name':'', 'service_name':'', 'property_name':''}),
    
    # Weekly Report
    'WEEKLY_REPORT': (weekly_report_template, {'partner_name':'', 'property_name':'', 'week_start':'', 'week_end':'', 'reservations':'', 'occupancy_rate':'', 'avg_orders':'', 'gmv':'', 'commission':'', 'recommendations':''}),
    
    # Additional templates for comprehensive testing
    'WELCOME_MESSAGE': (welcome_message_template, {'guest_name':'', 'property_name':'', 'room_number':'', 'wifi_password':''}),
    'CHECKOUT_REMINDER': (checkout_reminder_template, {'guest_name':'', 'property_name':'', 'checkout_time':'', 'extension_available':''}),
    'CHECKOUT_SUCCESSFUL': (checkout_successful_template, {'guest_name':'', 'property_name':'', 'total_amount':'', 'payment_method':''}),
    'SERVICE_REQUEST_RECEIVED': (service_request_received_template, {'guest_name':'', 'service_name':'', 'estimated_time':'', 'property_name':''}),
    'SERVICE_IN_PROGRESS': (service_in_progress_template, {'guest_name':'', 'service_name':'', 'eta':'', 'property_name':''}),
    'SERVICE_COMPLETED': (service_completed_template, {'guest_name':'', 'service_name':'', 'completion_time':'', 'property_name':''}),
    'FOOD_ORDER_PLACED': (food_order_placed_template, {'guest_name':'', 'property_name':'', 'order_items':'', 'total_amount':''}),
    'FOOD_ORDER_CONFIRMED': (food_order_confirmed_template, {'guest_name':'', 'property_name':'', 'order_items':'', 'delivery_time':''}),
    'FOOD_ORDER_READY': (food_order_ready_template, {'guest_name':'', 'property_name':'', 'order_items':'', 'total_amount':''}),
}
                                                        

class UserNotificationProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(UserProfile, on_delete=models.CASCADE, related_name='notification_profile')
    
    def clean(self):
        if self.cleaned_data['user'].is_partner:
            raise ValidationError('User is a partner, create a partner notification profile instead')
        
    def __str__(self) -> str:
        return self.user.user.name

class PartnerNotificationProfile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    partner = models.OneToOneField(PartnerProfile, on_delete=models.CASCADE, related_name='notification_profile')

    def clean(self):
        if not self.cleaned_data['partner'].user.is_partner:
            raise ValidationError('User is not a partner, create a user notification profile instead')
        
    def __str__(self) -> str:
        return self.partner.user.name

class Notification(models.Model):
    class UserTypeOptions(models.IntegerChoices):
        USER = 1
        PARTNER = 2

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    category = models.CharField(max_length=50, choices=[(tag.name, tag.value) for tag in NotificationCategory])
    channel = models.CharField(max_length=50, choices=[(tag.name, tag.value) for tag in NotificationChannel])
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    user_type = models.PositiveSmallIntegerField(choices=UserTypeOptions.choices)

    def __str__(self):
        return self.category + ' - ' + self.channel
    
class NotificationSubscription(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(UserProfile, on_delete=models.CASCADE, blank=True, null=True)
    partner = models.ForeignKey(PartnerProfile, on_delete=models.CASCADE, blank=True, null=True)
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='subscriptions')
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.user.user.name + ' - ' + self.notification.category + ' - ' + self.notification.channel
    
class NotificationLog(models.Model):
    """Model to track notification delivery and status"""
    
    # Message status choices for WhatsApp
    MESSAGE_STATUS_CHOICES = [
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed'),
        ('pending', 'Pending'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_notification_profile = models.ForeignKey(UserNotificationProfile, on_delete=models.CASCADE, blank=True, null=True)
    partner_notification_profile = models.ForeignKey(PartnerNotificationProfile, on_delete=models.CASCADE, blank=True, null=True)
    service_partner = models.ForeignKey('service.ServicePartner', on_delete=models.CASCADE, blank=True, null=True, help_text="For notifications sent to service partners")
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE)
    is_sent = models.BooleanField(default=False)
    is_read = models.BooleanField(default=False)
    error_msg = models.TextField(blank=True, null=True)
    
    # New fields for message tracking
    message_id = models.CharField(max_length=255, blank=True, null=True, help_text="External message ID from service provider")
    message_status = models.CharField(max_length=20, choices=MESSAGE_STATUS_CHOICES, default='pending')
    delivery_timestamp = models.DateTimeField(blank=True, null=True)
    read_timestamp = models.DateTimeField(blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        """Validate that exactly one recipient field is set"""
        from django.core.exceptions import ValidationError
        
        recipient_fields = [
            self.user_notification_profile,
            self.partner_notification_profile,
            self.service_partner
        ]
        
        # Count non-null recipient fields
        non_null_count = sum(1 for field in recipient_fields if field is not None)
        
        if non_null_count == 0:
            raise ValidationError(
                "Exactly one recipient must be specified: user_notification_profile, "
                "partner_notification_profile, or service_partner."
            )
        elif non_null_count > 1:
            raise ValidationError(
                "Only one recipient can be specified. Please set exactly one of: "
                "user_notification_profile, partner_notification_profile, or service_partner."
            )

    def __str__(self):
        return f"{self.notification.category} - {self.notification.channel} - {self.message_status}"
    
    class Meta:
        indexes = [
            models.Index(fields=['message_id']),
            models.Index(fields=['message_status']),
            models.Index(fields=['created_at']),
        ]
    

class FirebaseDeviceToken(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='firebase_device_tokens')
    token = models.CharField(max_length=255, unique=True)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.name + ' - ' + self.token


class WhatsAppContact(models.Model):
    """Model to store WhatsApp contact information for users"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='whatsapp_contacts')
    phone_number = PhoneNumberField(help_text="WhatsApp phone number")
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'phone_number']

    def __str__(self):
        return f"{self.user.name} - {self.phone_number}"