version: '3.8'

services:

  crdb:
    container_name: crdb
    hostname: crdb
    image: cockroachdb/cockroach:latest
    command: start-single-node --cluster-name=nestafar-cluster --insecure
    ports:
      - "26257:26257"
      - "8080:8080"
    environment:
      - DATABASE_NAME=backenddb
    volumes:
      - "${PWD}/cockroach-data/crdb:/cockroach/cockroach-data"

  crdb-init:
    platform: linux/amd64
    container_name: crdb-init
    hostname: crdb-init
    image: timveil/cockroachdb-remote-client:latest
    environment:
      - COCKROACH_HOST=crdb:26257
      - COCKROACH_INSECURE=true
      - DATABASE_NAME=backenddb
    depends_on:
      - crdb

  server:
      container_name: backend-server
      build:
        context: .
        dockerfile: Dockerfile
      stdin_open: true
      tty: true
      working_dir: /app
      ports:
        - "8000:8000"
      volumes:
        - .:/app
      environment:
        - DJANGO_ENV=test
      command: sh -c "cd nestafar && pytest||true && python3 manage.py makemigrations && python3 manage.py migrate &&  python3 manage.py collectstatic --noinput && gunicorn --bind 0.0.0.0:8000 nestafar.wsgi "
      depends_on:
        - crdb-init


  redis:
    container_name: redis
    image: redis:alpine
    ports:
      - "6379:6379"

  celery:
    container_name: celery
    restart: always
    build:
      context: .
    command:  sh -c "cd nestafar && celery -A nestafar worker -l info"
    volumes:
      - .:/app
    env_file:
      - .env
    depends_on:
      - crdb
      - redis
      - server

volumes:
  cockroach-data: