from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from stay.models import Property
from stay.serializers import PropertyDataVerificationSerializer
from nestafar.responses import SuccessResponse, BadRequestResponse, CreateResponse


class PropertyDataVerification(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, pk=None):
        _property = Property.objects.filter(id=pk).first()
        if not _property:
            return BadRequestResponse(data={'property': 'No property found for this user'})
        property = PropertyDataVerificationSerializer(_property)        
        return SuccessResponse(data=property.data)