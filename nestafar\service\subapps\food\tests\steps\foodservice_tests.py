from pytest_bdd import scenario, given, then, when
from food.models import FoodService
from food.tests import foodtest

@scenario("features/tests/food_service_test.feature")
def test_get_food_services(client):
    pass

@given("I am an authenticated user") 
def authenticate(client, user):  
    foodtest.authenticate_user(client, user)

@when("I get the list of food services")
def get_food_services(client):
    response = client.get("/api/food-services/")
    response.json()  # trigger data parsing

@then("the response status code is 200")
def check_status_code(response):
    assert response.status_code == 200

@then("And the response data contains a list of food services")
def check_response_data(response, service_factory=foodtest.create_food_service):
    data = response.json()
    assert isinstance(data, list)
    # Add assertions for filter scenarios (vegetarian only, name filter)
    # Use service_factory (or similar) to create test data if needed

@scenario("features/tests/food_service_test.feature")
def test_get_food_service_details(client):
    pass

@given("a food service exists with ID '<service_id>'")
def get_food_service(client, service_id):
    service = FoodService.objects.get(pk=service_id)
    return service

@when("I retrieve the food service details")
def retrieve_food_service(client, service):
    response = client.get(f"/api/food-services/{service.id}/")
    response.json()  # trigger data parsing

@then("And the response data contains the food service details")
def check_service_details(response, service):
    data = response.json()
    assert data["id"] == service.id
    # Add assertions for other service data fields (name, partner, etc.)
