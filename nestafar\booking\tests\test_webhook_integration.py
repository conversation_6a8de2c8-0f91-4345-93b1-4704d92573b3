"""
Integration tests for the Channel Manager webhook endpoints.
"""

import json
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, Mock
from booking.channel_managers.factory import ChannelManagerFactory


class WebhookIntegrationTests(TestCase):
    """Test the webhook endpoint integration with channel managers."""
    
    def setUp(self):
        self.client = Client()
    
    def test_aiosell_webhook_endpoint(self):
        """Test the AIOSell webhook endpoint routing."""
        # Sample AIOSell data from documentation
        aiosell_data = {
            "action": "book",
            "hotelCode": "SANDBOX-PMS",
            "channel": "Goingo",
            "bookingId": "*********",
            "cmBookingId": "AAABBBCCC",
            "bookedOn": "2022-12-08 15:25:35",
            "checkin": "2022-12-10",
            "checkout": "2022-12-12",
            "segment": "OTA",
            "specialRequests": "Airport Taxi Required",
            "pah": False,
            "amount": {
                "amountAfterTax": 1204.0,
                "amountBeforeTax": 1075.0,
                "tax": 129.0,
                "currency": "INR"
            },
            "guest": {
                "firstName": "Akshay",
                "lastName": "Kumar",
                "email": "<EMAIL>",
                "phone": "**********",
                "address": {
                    "line1": "51",
                    "city": "Bangalore",
                    "state": "Karnataka",
                    "country": "India",
                    "zipCode": "560035"
                }
            },
            "rooms": [
                {
                    "roomCode": "SUITE",
                    "rateplanCode": "SUITE-S-101",
                    "guestName": "Akshay Kumar",
                    "occupancy": {
                        "adults": 1,
                        "children": 0
                    },
                    "prices": [
                        {
                            "date": "2022-12-10",
                            "sellRate": 537.5
                        },
                        {
                            "date": "2022-12-11",
                            "sellRate": 537.5
                        }
                    ]
                }
            ]
        }
        
        # Mock the adapter's process_webhook method
        with patch.object(ChannelManagerFactory, 'create_adapter') as mock_factory:
            mock_adapter = Mock()
            mock_adapter.process_webhook.return_value = Mock(
                status_code=201,
                data={'success': True, 'message': 'Reservation Updated Successfully'}
            )
            mock_factory.return_value = mock_adapter
            
            # Test the webhook endpoint
            response = self.client.post(
                '/booking/webhook/aiosell/',
                data=json.dumps(aiosell_data),
                content_type='application/json'
            )
            
            # Verify the factory was called with correct channel name
            mock_factory.assert_called_once_with('aiosell')
            
            # Verify the adapter's process_webhook was called
            mock_adapter.process_webhook.assert_called_once()
    
    def test_unknown_channel_manager(self):
        """Test webhook endpoint with unknown channel manager."""
        response = self.client.post(
            '/booking/webhook/unknown_channel/',
            data=json.dumps({'action': 'book'}),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertIn('Unknown channel manager', response_data['message'])
    
    def test_empty_request_body(self):
        """Test webhook endpoint with empty request body."""
        response = self.client.post(
            '/booking/webhook/aiosell/',
            data='',
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertIn('Empty request body', response_data['message'])
    
    def test_invalid_json(self):
        """Test webhook endpoint with invalid JSON."""
        response = self.client.post(
            '/booking/webhook/aiosell/',
            data='invalid json',
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)
        response_data = response.json()
        self.assertFalse(response_data['success'])
        self.assertIn('Invalid JSON format', response_data['message'])
    
    def test_aiosell_cancel_webhook(self):
        """Test AIOSell cancellation webhook."""
        cancel_data = {
            "action": "cancel",
            "hotelCode": "SANDBOX-PMS",
            "channel": "Goingo",
            "bookingId": "*********"
        }
        
        # Mock the adapter's process_webhook method
        with patch.object(ChannelManagerFactory, 'create_adapter') as mock_factory:
            mock_adapter = Mock()
            mock_adapter.process_webhook.return_value = Mock(
                status_code=200,
                data={'success': True, 'message': 'Reservation Cancelled Successfully'}
            )
            mock_factory.return_value = mock_adapter
            
            # Test the webhook endpoint
            response = self.client.post(
                '/booking/webhook/aiosell/',
                data=json.dumps(cancel_data),
                content_type='application/json'
            )
            
            # Verify the factory was called with correct channel name
            mock_factory.assert_called_once_with('aiosell')
            
            # Verify the adapter's process_webhook was called
            mock_adapter.process_webhook.assert_called_once()
            
            # Verify the HTTP response
            self.assertEqual(response.status_code, 200)
            response_data = response.json()
            self.assertTrue(response_data['success'])
            self.assertEqual(response_data['message'], 'Reservation Cancelled Successfully')
    
    def test_webhook_exception_handling(self):
        """Test webhook endpoint exception handling."""
        # Mock the factory to raise an exception
        with patch.object(ChannelManagerFactory, 'create_adapter') as mock_factory:
            mock_factory.side_effect = Exception("Test exception")
            
            response = self.client.post(
                '/booking/webhook/aiosell/',
                data=json.dumps({'action': 'book'}),
                content_type='application/json'
            )
            
            self.assertEqual(response.status_code, 500)
            response_data = response.json()
            self.assertFalse(response_data['success'])
            self.assertIn('Internal server error', response_data['message'])
