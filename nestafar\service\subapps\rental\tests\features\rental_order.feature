Scenario: Creating a rental order
  Given I am an authenticated user with a rental cart containing items
  When I create a rental order
  Then the response status code is 201
  And a new rental order is created with the cart items details (including pickup/drop off dates, no of periods)

Scenario Outline: Retrieving rental order details
  Given a rental order exists with ID "<order_id>"
  When I retrieve the rental order details
  Then the response status code is 200
  And the response data contains the rental order details, including:
      * Guest information
      * Service information (period, type)
      * Ordered rental service items (details and quantities)

  Examples:
    | order_id |
    | 1        |
