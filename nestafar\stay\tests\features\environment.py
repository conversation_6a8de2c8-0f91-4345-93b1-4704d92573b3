from .helper import create_user
from rest_framework.test import APIClient
from django.urls import reverse
from behave.fixture import use_fixture_by_tag
from pathlib import Path
import json
from core.models import User


# def authenticate_client(context):
#     testusername = 'testuser'
#     testpassword = 'testpass'
#     testemail = '<EMAIL>'
#     create_user(testusername, testpassword,testemail)
#     set_active_user(context, testusername, testpassword)
#     context.scratchpad = {}


def set_active_user(context, username, password):
    client = APIClient()
    user = User.objects.get(username=username)
    login_response = client.post(
        reverse("login"), {"username": username, "password": password}
    )
    token = json.loads(login_response.content)["access_token"]
    client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
    context.auth_client = client
    context.auth_user = user


# fixture_registry = {
#     'fixture.auth_client': authenticate_client,
# }


def after_feature(context, feature):
    pass


def before_scenario(context, scenario):
    context.databases = "__all__"
    Path("__test_files").mkdir(parents=True, exist_ok=True)
    # authenticate_client(context)
    context.patchers = []


def after_scenario(context, scenario):
    for patcher in context.patchers:
        patcher.stop()


# def before_tag(context, tag):
#     if tag.startswith('fixture.'):
#         return use_fixture_by_tag(tag, context, fixture_registry)
