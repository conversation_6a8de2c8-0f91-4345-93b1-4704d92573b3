from django.dispatch import receiver
from django.db.models.signals import post_save, pre_save
from core.models import UserProfile, PartnerProfile
from .models import UserNotificationProfile, PartnerNotificationProfile
from notification.tasks import subscribe_user_to_notifications, send_notification, send_service_partner_notification
from .models import NotificationCategory
import logging
from celery import shared_task
from django.utils import timezone
from datetime import timedelta
from django.db.models import Sum
from service.models.service import ServicePartner

# Import concrete service models for signal connections
from service.subapps.transport.models import TransportService
from service.subapps.tourism.models import TourismService
from service.subapps.food.models import FoodService
from service.subapps.laundry.models import LaundryService
from service.subapps.shop.models import ShopService
from service.subapps.rental.models import RentalService

logger = logging.getLogger(__name__)


def get_guest_info(instance):
    """
    Helper method to extract guest name and room numbers from a PreCheckin instance.
    
    Args:
        instance: PreCheckin instance
        
    Returns:
        tuple: (guest_name, room_numbers_list)
    """
    # Get guest name from first guest
    guest_name = "Guest"
    if hasattr(instance, 'guests') and instance.guests.exists():
        guest_name = instance.guests.first().name
    
    # Get room numbers
    room_numbers = []
    if hasattr(instance, 'alloted_rooms') and instance.alloted_rooms.exists():
        room_numbers = [str(room.room.number) for room in instance.alloted_rooms.all()]
    
    return guest_name, room_numbers


@receiver(post_save, sender=UserProfile)
def create_user_notification_profile(sender, instance, created, **kwargs):
    if created:
        UserNotificationProfile.objects.create(user=instance)
        subscribe_user_to_notifications(instance)

@receiver(post_save, sender=PartnerProfile)
def create_partner_notification_profile(sender, instance, created, **kwargs):
    if created:
        PartnerNotificationProfile.objects.create(partner=instance)
        # Note: subscribe_user_to_notifications handles both UserProfile and PartnerProfile
        # It uses isinstance() internally to determine the correct subscription type
        subscribe_user_to_notifications(instance)
        
        # Trigger signup welcome message for the partner
        from notification.tasks.flow_tasks import send_signup_welcome_message
        # Get the primary property (first property) or create onboarding status if none exists
        from stay.models import Property
        
        # Try to get partner's properties
        properties = Property.objects.filter(staffs__in=[instance])
        if properties.exists():
            primary_property = properties.first()
            # Send welcome message and create onboarding status
            send_signup_welcome_message.delay(str(instance.id), str(primary_property.id))
        else:
            logger.info(f"No properties found for partner {instance.user.name}, welcome message will be sent when property is created")


@receiver(post_save, sender='stay.Property')
def property_created_handler(sender, instance, created, **kwargs):
    """Handle property creation - start onboarding process"""
    if created:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import send_signup_welcome_message
        
        # Get the property owner (first staff member)
        property_owner = instance.staffs.first()
        if property_owner:
            # Create onboarding status
            onboarding_status, created = OnboardingStatus.objects.get_or_create(
                partner=property_owner,
                property=instance
            )
            
            # Send signup welcome message
            send_signup_welcome_message.delay(str(property_owner.id), str(instance.id))
            
            logger.info(f"Created onboarding status for partner {property_owner.user.name} and property {instance.name}")


@receiver(post_save, sender='stay.Room')
def room_created_handler(sender, instance, created, **kwargs):
    """Handle room creation - update onboarding status"""
    if created:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import check_and_update_onboarding_status
        
        # Get the property owner
        property_owner = instance.property.staffs.first()
        if property_owner:
            # Update onboarding status
            try:
                onboarding = OnboardingStatus.objects.get(
                    partner=property_owner,
                    property=instance.property
                )
                onboarding.rooms_added = True
                onboarding.update_status()
                onboarding.save()
                
                # Check if onboarding is complete
                check_and_update_onboarding_status.delay(str(property_owner.id), str(instance.property.id))
                
                logger.info(f"Updated onboarding status for room creation: {instance.property.name}")
            except OnboardingStatus.DoesNotExist:
                logger.warning(f"No onboarding status found for property {instance.property.name}")


@receiver(post_save, sender='booking.ProfileImage')
def property_image_uploaded_handler(sender, instance, created, **kwargs):
    """Handle property image upload - update onboarding status"""
    if created:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import check_and_update_onboarding_status
        
        # Get property from profile
        property_obj = instance.profile.property
        property_owner = property_obj.staffs.first()
        
        if property_owner:
            try:
                onboarding = OnboardingStatus.objects.get(
                    partner=property_owner,
                    property=property_obj
                )
                onboarding.property_photos_uploaded = True
                onboarding.update_status()
                onboarding.save()
                
                # Check if onboarding is complete
                check_and_update_onboarding_status.delay(str(property_owner.id), str(property_obj.id))
                
                logger.info(f"Updated onboarding status for property image upload: {property_obj.name}")
            except OnboardingStatus.DoesNotExist:
                logger.warning(f"No onboarding status found for property {property_obj.name}")


def service_visibility_handler(sender, instance, created, **kwargs):
    """Handle service creation/visibility changes"""
    if created:
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import check_and_update_onboarding_status, notify_service_change
        
        # Get the service partner and property
        service_partner = instance.service_partner
        property_obj = service_partner.property
        
        # Update onboarding status when first service is added
        property_owner = property_obj.staffs.first()
        if property_owner:
            try:
                onboarding = OnboardingStatus.objects.get(
                    partner=property_owner,
                    property=property_obj
                )
                if not onboarding.services_added:
                    onboarding.services_added = True
                    onboarding.update_status()
                    onboarding.save()
                    
                    # Check if onboarding is complete
                    check_and_update_onboarding_status.delay(str(property_owner.id), str(property_obj.id))
                    
                    logger.info(f"Updated onboarding status for first service creation: {property_obj.name}")
            except OnboardingStatus.DoesNotExist:
                logger.warning(f"No onboarding status found for property {property_obj.name}")
        
        # Notify guests about new service
        notify_service_change.delay(str(service_partner.id), 'created', str(property_obj.id))


@receiver(pre_save, sender='booking.PreCheckin')
def capture_precheckin_original_status(sender, instance, **kwargs):
    """Capture the original status before save to detect changes"""
    if instance.pk:  # Only for existing instances (updates)
        try:
            original_instance = sender.objects.get(pk=instance.pk)
            instance._original_status = original_instance.status
        except sender.DoesNotExist:
            instance._original_status = None
    else:
        instance._original_status = None


@receiver(post_save, sender='booking.PreCheckin')
def precheckin_notification_handler(sender, instance, created, **kwargs):
    """Handle notifications for PreCheckin creation and status changes"""
    
    if created:
        # Notify property owner about new pre-checkin
        if hasattr(instance, 'property') and instance.property:
            property_owner = instance.property.staffs.first()
            if property_owner:
                # Get guest info using helper method
                guest_name, room_numbers = get_guest_info(instance)
                
                data = {
                    'guest_name': guest_name,
                    'property_owner_name': property_owner.user.name,
                    'expected_date': instance.expected_checkin.strftime('%Y-%m-%d'),
                    'room_number': ', '.join(room_numbers) if room_numbers else 'TBD'
                }
                
                send_notification.delay(
                    str(property_owner.user.id),
                    NotificationCategory.PRECHECKIN_CREATED.name,
                    data
                )
    else:
        # Check if status changed using captured original status
        if hasattr(instance, '_original_status') and instance._original_status is not None:
            if instance._original_status != instance.status:
                # Notify about status change
                property_owner = instance.property.staffs.first() if instance.property else None
                if property_owner:
                    # Get guest info using helper method
                    guest_name, room_numbers = get_guest_info(instance)
                    
                    data = {
                        'guest_name': guest_name,
                        'property_owner_name': property_owner.user.name,
                        'status': instance.get_status_display(),
                        'room_number': ', '.join(room_numbers) if room_numbers else 'TBD'
                    }
                    
                    send_notification.delay(
                        str(property_owner.user.id),
                        NotificationCategory.PRECHECKIN_STATUS_CHANGED.name,
                        data
                    )
                
                # If confirmed, also send confirmation notification
                if instance.status == 'confirmed':
                    if property_owner:
                        data = {
                            'guest_name': guest_name,
                            'property_owner_name': property_owner.user.name,
                            'expected_date': instance.expected_checkin.strftime('%Y-%m-%d'),
                            'room_number': ', '.join(room_numbers) if room_numbers else 'TBD'
                        }
                        
                        send_notification.delay(
                            str(property_owner.user.id),
                            NotificationCategory.PRECHECKIN_CONFIRMED.name,
                            data
                        )


@receiver(pre_save, sender='stay.Guest')
def capture_guest_original_state(sender, instance, **kwargs):
    """Capture the original checked_in and checked_out state before save to detect changes"""
    if instance.pk:  # Only for existing instances (updates)
        try:
            original_instance = sender.objects.get(pk=instance.pk)
            instance._original_checked_in = original_instance.checked_in
            instance._original_checked_out = original_instance.checked_out
        except sender.DoesNotExist:
            instance._original_checked_in = None
            instance._original_checked_out = None
    else:
        instance._original_checked_in = None
        instance._original_checked_out = None


@receiver(post_save, sender='stay.Guest')
def guest_notification_handler(sender, instance, created, **kwargs):
    """Handle notifications for guest check-in and checkout events"""
    
    # Skip processing for new guest creation
    if created:
        return
    
    # Handle check-in notification
    if (hasattr(instance, '_original_checked_in') and 
        instance._original_checked_in is not None and
        not instance._original_checked_in and instance.checked_in):
        
        # Guest just checked in - send notification
        checkin_data = {
            'username': instance.user.name,
            'property_name': instance.room.property.name if instance.room and instance.room.property else 'Property',
            'room_no': str(instance.room.room_no) if instance.room else 'N/A'
        }
        
        send_notification.delay(
            str(instance.user.id),
            NotificationCategory.USER_CHECKIN_INITIATED.name,
            checkin_data
        )
    
    # Handle checkout notification
    if (hasattr(instance, '_original_checked_out') and
        instance._original_checked_out is not None and
        not instance._original_checked_out and instance.checked_out):

        # Guest just checked out - process complete checkout flow
        from notification.tasks.flow_tasks import process_guest_checkout_flow
        process_guest_checkout_flow.delay(str(instance.id))


def send_order_notifications(order_instance, created=False):
    """Helper function to send order notifications"""
    
    if created:
        # Order was just created
        # Notify guest
        guest_data = {
            'username': order_instance.guest.user.name,
            'order_id': str(order_instance.id)
        }
        
        send_notification.delay(
            str(order_instance.guest.user.id),
            NotificationCategory.USER_ORDER_PLACED.name,
            guest_data
        )
        
        # Notify partner
        if order_instance.service_partner:
            partner_data = {
                'partner_name': order_instance.service_partner.partner.user.name,
                'order_id': str(order_instance.id),
                'room_no': str(order_instance.guest.room.number) if order_instance.guest.room else 'N/A',
                
                'room_no': str(order_instance.guest.room.number) if order_instance.guest and order_instance.guest.room else 'N/A',
            }
            
            send_notification.delay(
                str(order_instance.service_partner.partner.user.id),
                NotificationCategory.PARTNER_ORDER_PLACED.name,
                partner_data
            )
    else:
        # Order status changed
        status_to_user_category = {
            order_instance.OrderStatus.ACCEPTED: NotificationCategory.USER_ORDER_ACCEPTED.name,
            order_instance.OrderStatus.ONGOING: NotificationCategory.USER_ORDER_ONGOING.name,
            order_instance.OrderStatus.REJECTED: NotificationCategory.USER_ORDER_REJECTED.name,
            order_instance.OrderStatus.CANCELLED: NotificationCategory.USER_ORDER_CANCELLED.name,
            order_instance.OrderStatus.COMPLETED: NotificationCategory.USER_ORDER_COMPLETED.name,
        }
        
        status_to_partner_category = {
            order_instance.OrderStatus.ACCEPTED: NotificationCategory.PARTNER_ORDER_ACCEPTED.name,
            order_instance.OrderStatus.ONGOING: NotificationCategory.PARTNER_ORDER_ONGOING.name,
            order_instance.OrderStatus.REJECTED: NotificationCategory.PARTNER_ORDER_REJECTED.name,
            order_instance.OrderStatus.CANCELLED: NotificationCategory.PARTNER_ORDER_CANCELLED.name,
            order_instance.OrderStatus.COMPLETED: NotificationCategory.PARTNER_ORDER_COMPLETED.name,
        }
        
        # Notify guest
        if order_instance.status in status_to_user_category:
            guest_data = {
                'username': order_instance.guest.user.name,
                'order_id': str(order_instance.id)
            }
            
            send_notification.delay(
                str(order_instance.guest.user.id),
                status_to_user_category[order_instance.status],
                guest_data
            )
        
        # Notify partner
        if order_instance.service_partner and order_instance.status in status_to_partner_category:
            partner_data = {
                'partner_name': order_instance.service_partner.partner.user.name,
                'order_id': str(order_instance.id),
                'room_no': str(order_instance.guest.room.number) if order_instance.guest.room else 'N/A',
                'guest_name': order_instance.guest.user.name
            }
            
            send_notification.delay(
                str(order_instance.service_partner.partner.user.id),
                status_to_partner_category[order_instance.status],
                partner_data
            )

@shared_task
def send_daily_summary_notifications():
    """
    Celery task to send daily summary notifications to all active guests and partners
    Scheduled to run at the end of each day
    """
    from service.subapps.food.models import FoodOrder
    from service.subapps.laundry.models import LaundryOrder
    from service.subapps.rental.models import RentalOrder
    from service.subapps.transport.models import TransportOrder
    from service.subapps.shop.models import ShopOrder
    from service.subapps.tourism.models import TourismOrder
    from stay.models import Guest
    
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    
    # All order models
    order_models = [FoodOrder, LaundryOrder, RentalOrder, TransportOrder, ShopOrder, TourismOrder]
    
    try:
        logger.info(f"Starting daily summary notifications for {yesterday}")
        
        # Send summary to guests
        active_guests = Guest.objects.filter(
            checked_in=True,
            checked_out=False,
            check_in_date__date__lte=today
        )
        
        for guest in active_guests:
            try:
                # Calculate guest's daily stats across all order types
                total_orders = 0
                total_spent = 0
                
                for order_model in order_models:
                    orders = order_model.objects.filter(
                        guest=guest,
                        created_at__date=yesterday
                    )
                    total_orders += orders.count()
                    total_spent += orders.aggregate(total=Sum('total'))['total'] or 0
                
                # Only send if there's activity
                if total_orders > 0:
                    notification_data = {
                        'username': guest.user.name,
                        'total_orders': total_orders,
                        'total_spent': round(total_spent, 2),
                        'property_name': guest.room.property.name
                    }
                    
                    send_notification.delay(
                        str(guest.user.id),
                        NotificationCategory.DAILY_SUMMARY_GUEST.name,
                        notification_data
                    )
                    logger.info(f"Daily summary sent to guest {guest.user.name}")
                    
            except Exception as e:
                logger.error(f"Error sending daily summary to guest {guest.id}: {str(e)}")
        
        # Send summary to service partners
        service_partners = ServicePartner.objects.filter(
            is_visible=True
        )
        
        for partner in service_partners:
            try:
                # Calculate partner's daily stats
                total_orders = 0
                total_revenue = 0
                
                for order_model in order_models:
                    orders = order_model.objects.filter(
                        service_partner=partner,
                        created_at__date=yesterday,
                        status__in=[
                            order_model.OrderStatus.COMPLETED,
                            order_model.OrderStatus.ACCEPTED,
                            order_model.OrderStatus.ONGOING
                        ]
                    )
                    total_orders += orders.count()
                    # Calculate revenue using database aggregation
                    revenue = orders.aggregate(total=Sum('total'))['total'] or 0
                    total_revenue += revenue                
                # Only send if there's activity
                if total_orders > 0:
                    # Get property name from partner's property or default
                    property_name = 'All Properties'
                    if hasattr(partner, 'property') and partner.property:
                        property_name = partner.property.name
                    
                    notification_data = {
                        'partner_name': partner.name, 
                        'total_orders': total_orders,
                        'total_revenue': round(total_revenue, 2),
                        'property_name': property_name
                    }
                    send_service_partner_notification.delay(
                        str(partner.id),
                        NotificationCategory.DAILY_SUMMARY_PARTNER.name,
                        notification_data
                    )
                    logger.info(f"Daily summary sent to partner {partner.name}")

            except Exception as e:
                logger.error(f"Error sending daily summary to partner {partner.id}: {str(e)}")
        
        logger.info("Daily summary notifications completed successfully")
        
    except Exception as e:
        logger.error(f"Error in daily summary notifications: {str(e)}")
        raise

@receiver(post_save, sender=PartnerProfile)
def partner_signup_handler(sender, instance, created, **kwargs):
    """Handle partner signup and trigger welcome message"""
    if created:
        # Get the first property associated with this partner
        if hasattr(instance, 'properties') and instance.properties.exists():
            property_obj = instance.properties.first()
            # Trigger signup welcome message
            from notification.tasks.flow_tasks import send_signup_welcome_message
            send_signup_welcome_message.delay(str(instance.id), str(property_obj.id))


@receiver(pre_save, sender='notification.OnboardingStatus')
def onboarding_pre_save_handler(sender, instance, **kwargs):
    """Handle onboarding completion logic before save"""
    # Check if this is an update (not creation) and if onboarding just completed
    if instance.pk:  # Only for existing instances
        try:
            # Get the original instance to check for status changes
            original = sender.objects.get(pk=instance.pk)
            # If status is changing to completed or if it's complete but not marked as such
            if instance.is_complete() and original.status != sender.StatusChoices.COMPLETED:
                instance.status = sender.StatusChoices.COMPLETED
                if not instance.completed_at:
                    instance.completed_at = timezone.now()
                # Set a flag to trigger notification in post_save
                instance._just_completed = True
        except sender.DoesNotExist:
            pass


@receiver(post_save, sender='notification.OnboardingStatus')
def onboarding_status_handler(sender, instance, created, **kwargs):
    """Handle onboarding completion notifications"""
    # Only send notification if onboarding just completed (set in pre_save)
    if hasattr(instance, '_just_completed') and instance._just_completed:
        # Send completion notification
        from notification.tasks.flow_tasks import send_onboarding_completed_notification
        send_onboarding_completed_notification.delay(
            str(instance.partner.id),
            str(instance.property.id)
        )


@receiver(post_save, sender='booking.AllotedRoom')
def room_allotment_handler(sender, instance, created, **kwargs):
    """Handle room allotment notifications"""
    if created:
        # Get the guest from the pre-checkin
        if instance.precheckin and instance.precheckin.pre_checkin_guests.exists():
            primary_guest = instance.precheckin.pre_checkin_guests.filter(is_primary=True).first()
            if primary_guest:
                from notification.tasks.flow_tasks import send_room_allotment_notification
                send_room_allotment_notification.delay(
                    str(primary_guest.id),
                    str(instance.room.id)
                )


@receiver(post_save, sender='stay.Guest')
def guest_checkin_welcome_handler(sender, instance, **kwargs):
    """Send welcome message when guest actually arrives"""
    # Check if guest just arrived (checked_in status changed to True)
    if (hasattr(instance, '_original_checked_in') and
        instance._original_checked_in is not None and
        not instance._original_checked_in and instance.checked_in):

        # Send check-in successful notification
        checkin_data = {
            'guest_name': instance.user.name,
            'property_name': instance.room.property.name if instance.room and instance.room.property else 'Property',
            'room_details': f"Room {instance.room.room_no} - {instance.room.room_type}" if instance.room else 'Room',
            'checkout_date': instance.check_out_date.strftime('%Y-%m-%d') if instance.check_out_date else 'TBD'
        }

        send_notification.delay(
            str(instance.user.id),
            'CHECKIN_SUCCESSFUL',
            checkin_data
        )


def service_visibility_handler(sender, instance, created, **kwargs):
    """Handle service visibility changes"""
    if not created:  # Only for updates
        # Check if visibility changed
        if hasattr(instance, '_original_is_visible'):
            if instance._original_is_visible != instance.is_visible:
                action = 'restored' if instance.is_visible else 'hidden'
                from notification.tasks.flow_tasks import notify_service_change
                if hasattr(instance, 'service_partner') and hasattr(instance, 'property'):
                    notify_service_change.delay(
                        str(instance.service_partner.id),
                        action,
                        str(instance.property.id)         
                    )
                else:
                    logger.warning(f"Service instance {instance.id} missing required attributes")


# Connect to all concrete service models
for service_model in [TransportService, TourismService, FoodService, LaundryService, ShopService, RentalService]:
    post_save.connect(service_visibility_handler, sender=service_model)


def capture_service_visibility(sender, instance, **kwargs):
    """Capture original visibility before save"""
    if instance.pk:
        try:
            original = sender.objects.get(pk=instance.pk)
            instance._original_is_visible = original.is_visible
        except sender.DoesNotExist:
            instance._original_is_visible = None

# Connect to all concrete service models
for service_model in [TransportService, TourismService, FoodService, LaundryService, ShopService, RentalService]:
    pre_save.connect(capture_service_visibility, sender=service_model)


@receiver(post_save, sender='stay.Property')
def property_updated_handler(sender, instance, created, **kwargs):
    """Update onboarding status when property is updated"""
    if not created:  # Only for updates
        try:
            # Get partner profile
            partner = instance.owner if hasattr(instance, 'owner') else None
            if partner:
                from notification.tasks.flow_tasks import check_and_update_onboarding_status
                check_and_update_onboarding_status.delay(str(partner.id), str(instance.id))
        except Exception as e:
            logger.error(f"Error triggering onboarding status check: {str(e)}")


@receiver(post_save, sender='stay.Room')
def room_updated_handler(sender, instance, created, **kwargs):
    """Update onboarding status when rooms are added/updated"""
    try:
        # Get partner profile through property
        property_obj = instance.property
        partner = property_obj.owner if hasattr(property_obj, 'owner') else None
        if partner:
            from notification.tasks.flow_tasks import check_and_update_onboarding_status
            check_and_update_onboarding_status.delay(str(partner.id), str(property_obj.id))
    except Exception as e:
        logger.error(f"Error triggering onboarding status check: {str(e)}")


def service_updated_handler(sender, instance, created, **kwargs):
    """Update onboarding status when services are added/updated"""
    try:
        # Get partner profile through property
        property_obj = instance.property if hasattr(instance, 'property') else None
        if property_obj:
            partner = property_obj.owner if hasattr(property_obj, 'owner') else None
            if partner:
                from notification.tasks.flow_tasks import check_and_update_onboarding_status
                check_and_update_onboarding_status.delay(str(partner.id), str(property_obj.id))
    except Exception as e:
        logger.error(f"Error triggering onboarding status check: {str(e)}")

# Connect to all concrete service models
for service_model in [TransportService, TourismService, FoodService, LaundryService, ShopService, RentalService]:
    post_save.connect(service_updated_handler, sender=service_model)


@receiver(post_save, sender='booking.ProfileImage')
def property_image_updated_handler(sender, instance, created, **kwargs):
    """Update onboarding status when property images are added"""
    try:
        # Get partner profile through property
        profile = instance.profile
        property_obj = profile.property if hasattr(profile, 'property') else None
        if property_obj:
            partner = property_obj.owner if hasattr(property_obj, 'owner') else None
            if partner:
                from notification.tasks.flow_tasks import check_and_update_onboarding_status
                check_and_update_onboarding_status.delay(str(partner.id), str(property_obj.id))
    except Exception as e:
        logger.error(f"Error triggering onboarding status check: {str(e)}")
