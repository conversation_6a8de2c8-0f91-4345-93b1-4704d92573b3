from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import transaction
from core.models import User,PartnerProfile
from notification.models import WhatsAppContact
from notification.models.onboarding import OnboardingStatus
from notification.tasks.flow_tasks import *
from stay.models import Property, Room, Guest
from service.models.service import ServicePartner
import logging
from datetime import timedelta
from django.utils import timezone

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Test complete WhatsApp integration with all flows'

    def add_arguments(self, parser):
        """Add command line arguments"""
        parser.add_argument(
            '--phone', 
            type=str, 
            help='Phone number to send test messages to (format: +919876543210)',
            required=True
        )
        parser.add_argument(
            '--flow', 
            type=str, 
            default='all',
            choices=[
                'all', 'onboarding', 'checkin', 'service', 'orders', 
                'checkout', 'reports', 'reminders'
            ],
            help='Specific flow to test'
        )
        parser.add_argument(
            '--dry-run', 
            action='store_true',
            help='Show what would be sent without actually sending messages'
        )
        parser.add_argument(
            '--verbose', 
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        phone = options['phone']
        flow = options['flow']
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        self.stdout.write('🚀 Testing Complete WhatsApp Integration\n')
        
        if dry_run:
            self.stdout.write('⚠️  DRY RUN MODE - No messages will be sent\n')
        
        try:
            # Setup test user and data
            user, partner, property_obj, guest = self.setup_test_data(phone, verbose)
            
            # Test flows
            if flow == 'all':
                self.test_all_flows(user, partner, property_obj, guest, dry_run, verbose)
            else:
                self.test_specific_flow(flow, user, partner, property_obj, guest, dry_run, verbose)
                
            # Print summary
            self.print_summary()
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during testing: {str(e)}')
            )
            if verbose:
                import traceback
                self.stdout.write(traceback.format_exc())

    def setup_test_data(self, phone, verbose):
        """Setup test user and related data"""
        self.stdout.write('📋 Setting up test data...')
        
        with transaction.atomic():
            # Create test property first
            from geo.models import Location
            location, created = Location.objects.get_or_create(
                name='Test Location',
                defaults={
                    'latitude': 12.34,
                    'longitude': 56.78
                }
            )
            
            property_obj, created = Property.objects.get_or_create(
                name='Test Hotel Nestafar',
                defaults={
                    'description': 'Test property for WhatsApp integration testing',
                    'location': location,
                    'rooms': 10,
                    'avg_price': 100.0
                }
            )
            
            # Create or get test user
            user, created = User.objects.get_or_create(
                phone=phone,
                defaults={
                    'name': 'Test User',
                    'email': '<EMAIL>',
                    'is_partner': False
                }
            )
            
            if created and verbose:
                self.stdout.write(f'✅ Created test user: {user.name}')
            
            # Create WhatsApp contact
            whatsapp_contact, created = WhatsAppContact.objects.get_or_create(
                user=user,
                defaults={
                    'phone_number': phone,
                    'is_active': True
                }
            )
            
            # Create partner user for property owner
            partner_phone = phone.replace('+234', '+233')  # Different number for partner
            if partner_phone == phone:  # If replacement didn't work, add suffix
                partner_phone = phone + '1'
                
            partner_user, created = User.objects.get_or_create(
                phone=partner_phone,
                defaults={
                    'name': 'Test Partner',
                    'email': '<EMAIL>',
                    'is_partner': True
                }
            )
            
            # Create partner profile
            partner_profile, created = PartnerProfile.objects.get_or_create(
                user=partner_user,
                defaults={
                    'active_property': property_obj
                }
            )
            
            # Create WhatsApp contact for partner
            partner_whatsapp, created = WhatsAppContact.objects.get_or_create(
                user=partner_user,
                defaults={
                    'phone_number': partner_phone,
                    'is_active': True
                }
            )
            
            # Associate partner with property
            property_obj.staffs.add(partner_profile)
            
            # Create test room
            room, created = Room.objects.get_or_create(
                property=property_obj,
                room_no='101',
                defaults={
                    'rate': 2000.00,
                    'bed': 1,
                    'max_guests': 2
                }
            )
            
            # Create test guest
            guest, created = Guest.objects.get_or_create(
                user=user,
                room=room,
                defaults={
                    'check_in_date': timezone.now(),
                    'check_out_date': timezone.now() + timedelta(days=2),
                    'checked_in': False,
                    'checked_out': False
                }
            )
            
            if verbose:
                self.stdout.write(f'✅ Test data setup complete')
                self.stdout.write(f'   User: {user.name} ({user.phone})')
                self.stdout.write(f'   Partner: {partner_user.name} ({partner_user.phone})')
                self.stdout.write(f'   Property: {property_obj.name}')
                self.stdout.write(f'   Room: {room.room_no}')
        
        return user, partner_profile, property_obj, guest

    def test_all_flows(self, user, partner, property_obj, guest, dry_run, verbose):
        """Test all WhatsApp flows"""
        flows = ['onboarding', 'checkin', 'service', 'orders', 'checkout', 'reports', 'reminders']
        
        for flow in flows:
            self.stdout.write(f'\n🔄 Testing {flow.upper()} flow...')
            try:
                self.test_specific_flow(flow, user, partner, property_obj, guest, dry_run, verbose)
                self.stdout.write(f'✅ {flow.upper()} flow completed')
            except Exception as e:
                self.stdout.write(f'❌ {flow.upper()} flow failed: {str(e)}')
                if verbose:
                    import traceback
                    self.stdout.write(traceback.format_exc())

    def test_specific_flow(self, flow, user, partner, property_obj, guest, dry_run, verbose):
        """Test specific flow"""
        
        if flow == 'onboarding':
            self.test_onboarding_flow(partner, property_obj, dry_run, verbose)
        elif flow == 'checkin':
            self.test_checkin_flow(user, partner, property_obj, guest, dry_run, verbose)
        elif flow == 'service':
            self.test_service_flow(user, property_obj, guest, dry_run, verbose)
        elif flow == 'orders':
            self.test_orders_flow(user, guest, dry_run, verbose)
        elif flow == 'checkout':
            self.test_checkout_flow(user, guest, dry_run, verbose)
        elif flow == 'reports':
            self.test_reports_flow(partner, property_obj, dry_run, verbose)
        elif flow == 'reminders':
            self.test_reminders_flow(user, partner, property_obj, guest, dry_run, verbose)

    def test_onboarding_flow(self, partner, property_obj, dry_run, verbose):
        """Test onboarding flow"""
        
        # Create/get onboarding status
        onboarding, created = OnboardingStatus.objects.get_or_create(
            partner=partner,
            property=property_obj,
            defaults={'status': OnboardingStatus.StatusChoices.PENDING}
        )
        
        if not dry_run:
            # Test signup welcome
            self.stdout.write('  📧 Sending signup welcome message...')
            send_signup_welcome_message.delay(str(partner.id), str(property_obj.id))
            
            # Test onboarding reminder
            self.stdout.write('  🔔 Sending onboarding reminder...')
            from notification.tasks.send_tasks import send_notification
            send_notification.delay(
                str(partner.user.id),
                'ONBOARDING_REMINDER',
                {
                    'partner_name': partner.user.name,
                    'property_name': property_obj.name,
                    'missing_steps': ['Property photos', 'Room photos', 'Services setup'],
                    'completion_percentage': '40'
                }
            )
            
            # Test completion notification
            self.stdout.write('  🎉 Sending onboarding completion...')
            send_notification.delay(
                str(partner.user.id),
                'ONBOARDING_COMPLETED',
                {
                    'partner_name': partner.user.name,
                    'property_name': property_obj.name
                }
            )
        
        if verbose:
            self.stdout.write(f'    Onboarding status: {onboarding.status}')
            self.stdout.write(f'    Completion: {onboarding.calculate_completion_percentage():.1f}%')

    def test_checkin_flow(self, user, partner, property_obj, guest, dry_run, verbose):
        """Test check-in flow"""
        
        if not dry_run:
            # Test pre-checkin reminder
            self.stdout.write('  🏨 Sending pre-checkin reminder...')
            from notification.tasks.send_tasks import send_notification
            send_notification.delay(
                str(user.id),
                'PRECHECKIN_REMINDER',
                {
                    'guest_name': user.name,
                    'property_name': property_obj.name,
                    'checkin_date': (timezone.now() + timedelta(hours=12)).strftime('%Y-%m-%d %H:%M'),
                    'hours_remaining': '12'
                }
            )
            
            # Test room allotment
            self.stdout.write('  🚪 Sending room allotment notification...')
            send_notification.delay(
                str(user.id),
                'ROOM_ALLOTMENT',
                {
                    'guest_name': user.name,
                    'property_name': property_obj.name,
                    'room_number': guest.room.room_no,
                    'checkin_date': guest.check_in_date.strftime('%Y-%m-%d %H:%M')
                }
            )
            
            # Test guest arrival welcome
            self.stdout.write('  👋 Sending guest arrival welcome...')
            send_notification.delay(
                str(user.id),
                'GUEST_ARRIVED_WELCOME',
                {
                    'guest_name': user.name,
                    'property_name': property_obj.name,
                    'room_number': guest.room.room_no
                }
            )
        
        if verbose:
            self.stdout.write(f'    Guest: {user.name}')
            self.stdout.write(f'    Room: {guest.room.room_no}')
            self.stdout.write(f'    Check-in: {guest.check_in_date}')

    def test_service_flow(self, user, property_obj, guest, dry_run, verbose):
        """Test service management flow"""
        
        if not dry_run:
            # Test dinner reminder
            self.stdout.write('  🍽️ Sending dinner reminder...')
            from notification.tasks.send_tasks import send_notification
            send_notification.delay(
                str(user.id),
                'DINNER_REMINDER',
                {
                    'guest_name': user.name,
                    'property_name': property_obj.name
                }
            )
            
            # Test new service available
            self.stdout.write('  🆕 Sending new service notification...')
            send_notification.delay(
                str(user.id),
                'NEW_SERVICE_AVAILABLE',
                {
                    'guest_name': user.name,
                    'service_name': 'Pizza Corner',
                    'service_type': 'Food',
                    'property_name': property_obj.name
                }
            )
            
            # Test service hidden notification
            self.stdout.write('  ⚠️ Sending service hidden notification...')
            send_notification.delay(
                str(user.id),
                'SERVICE_HIDDEN_NOTIFICATION',
                {
                    'guest_name': user.name,
                    'service_name': 'Spa Services',
                    'property_name': property_obj.name
                }
            )

    def test_orders_flow(self, user, guest, dry_run, verbose):
        """Test orders flow"""
        
        if not dry_run:
            # Test order placed
            self.stdout.write('  🛍️ Sending order placed notification...')
            from notification.tasks.send_tasks import send_notification
            send_notification.delay(
                str(user.id),
                'USER_ORDER_PLACED',
                {
                    'username': user.name,
                    'order_id': 'TEST123'
                }
            )
            
            # Test order accepted
            self.stdout.write('  ✅ Sending order accepted notification...')
            send_notification.delay(
                str(user.id),
                'USER_ORDER_ACCEPTED',
                {
                    'username': user.name,
                    'order_id': 'TEST123',
                    'vendor_name': 'Pizza Corner',
                    'estimated_time': '30 minutes',
                    'total_amount': '450.00',
                    'vendor_contact': '+91-9876543210'
                }
            )
            
            # Test order completed
            self.stdout.write('  🎉 Sending order completed notification...')
            send_notification.delay(
                str(user.id),
                'USER_ORDER_COMPLETED',
                {
                    'username': user.name,
                    'order_id': 'TEST123'
                }
            )

    def test_checkout_flow(self, user, guest, dry_run, verbose):
        """Test checkout flow"""
        
        if not dry_run:
            # Test checkout bill
            self.stdout.write('  📄 Sending checkout bill...')
            from notification.tasks.send_tasks import send_notification
            send_notification.delay(
                str(user.id),
                'CHECKOUT_BILL',
                {
                    'guest_name': user.name,
                    'property_name': guest.room.property.name,
                    'checkout_date': timezone.now().strftime('%Y-%m-%d'),
                    'total_amount': '3,450.00'
                }
            )
            
            # Test review request
            self.stdout.write('  ⭐ Sending review request...')
            send_notification.delay(
                str(user.id),
                'REVIEW_REQUEST',
                {
                    'guest_name': user.name,
                    'property_name': guest.room.property.name
                }
            )

    def test_reports_flow(self, partner, property_obj, dry_run, verbose):
        """Test reports flow"""
        
        if not dry_run:
            # Test weekly report
            self.stdout.write('  📊 Sending weekly report...')
            from notification.tasks.send_tasks import send_notification
            
            recommendations = [
                "Great occupancy rate this week!",
                "Consider adding more food vendors",
                "Promote services to increase guest usage"
            ]
            
            send_notification.delay(
                str(partner.user.id),
                'WEEKLY_REPORT',
                {
                    'partner_name': partner.user.name,
                    'property_name': property_obj.name,
                    'week_start': '2025-01-06',
                    'week_end': '2025-01-12',
                    'reservations': '15',
                    'occupancy_rate': '75.5',
                    'avg_orders': '2.3',
                    'gmv': '45,230.00',
                    'commission': '4,523.00',
                    'recommendations': recommendations
                }
            )

    def test_reminders_flow(self, user, partner, property_obj, guest, dry_run, verbose):
        """Test various reminder flows"""
        
        if not dry_run:
            # Test vendor order reminder
            self.stdout.write('  ⏰ Sending vendor order reminder...')
            from notification.tasks.send_tasks import send_service_partner_notification
            from geo.models import Location
            
            # Create a test service partner with proper location
            location, created = Location.objects.get_or_create(
                name='Test Vendor Location',
                defaults={'latitude': 12.34, 'longitude': 56.78}
            )
            
            service_partner, created = ServicePartner.objects.get_or_create(
                name='Test Food Vendor',
                location=location,
                defaults={
                    'type_of_service': ServicePartner.PartnerTypes.FOOD,
                    'phone_number': partner.user.phone,
                    'is_visible': True
                }
            )
            
            # Create PropertyPartner relationship
            from stay.models import PropertyPartner
            property_partner, created = PropertyPartner.objects.get_or_create(
                property=property_obj,
                partner=service_partner,
                defaults={
                    'name': f'{service_partner.name} - {property_obj.name}',
                    'commission': 10.0
                }
            )
            
            send_service_partner_notification.delay(
                str(service_partner.id),
                'VENDOR_ORDER_REMINDER',
                {
                    'vendor_name': service_partner.name,
                    'order_id': 'TEST123',
                    'minutes_pending': '20',
                    'total_amount': '450.00'
                }
            )
            
            # Test pre-checkin cancellation warning
            self.stdout.write('  ⚠️ Sending pre-checkin cancellation warning...')
            from notification.tasks.send_tasks import send_notification
            send_notification.delay(
                str(user.id),
                'PRECHECKIN_CANCELLATION_WARNING',
                {
                    'guest_name': user.name,
                    'property_name': property_obj.name,
                    'hours_remaining': '2'
                }
            )

    def print_summary(self):
        """Print test summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write('📋 WHATSAPP INTEGRATION TEST SUMMARY')
        self.stdout.write('='*60)
        self.stdout.write('✅ All flows tested successfully')
        self.stdout.write('💡 Check your WhatsApp for test messages')
        self.stdout.write('📖 Review logs for detailed execution info')
        self.stdout.write('⚙️  Check WhatsApp Business Manager for template status')
        self.stdout.write('='*60)
