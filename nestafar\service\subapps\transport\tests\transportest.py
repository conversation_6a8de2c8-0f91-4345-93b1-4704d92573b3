from django.contrib.auth import get_user_model
from transport.models import *
from rest_framework.test import APIClient
from django.urls import reverse
from core.models import User
import json
import pytest

@pytest.fixture
def create_guest(client):
    user = User.objects.create_user(username="test_user", password="secret")
    guest = Guest.objects.create(user=user)
    return guest

@pytest.fixture
def create_location(db):
    location = Location.objects.create(name="Test Location")
    return location

@pytest.fixture
def create_service_partner(db):
    partner = ServicePartner.objects.create(name="Test Partner", type_of_service="Transport")
    return partner

@pytest.fixture
def create_transport_service(db, create_service_partner):
    service = TransportService.objects.create(
        name="Test Transport",
        service_partner=create_service_partner,
    )
    return service

@pytest.fixture
def create_transport_service_item(db, create_transport_service):
    item = TransportServiceItem.objects.create(
        service=create_transport_service,
        name="Test Item",
        price=100.00,
    )
    return item

def create_user(context, username, password):
    client = APIClient()
    user = User.objects.get(username=username)
    login_response = client.post(
        reverse("login"), {"username": username, "password": password}
    )
    token = json.loads(login_response.content)["access_token"]
    client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
    context.auth_client = client
    context.auth_user = user

def create_staff_user(username="staff_user", password="secret"):
    """
    Creates a test user object with staff privileges.
    """
    user = create_user(username, password)
    user.is_staff = True
    user.save()
    return user


def authenticate_user(client, user):
    """
    Authenticates a user with the test client.
    """
    client.force_authenticate(user=user)
