from django.urls import path, include
from rest_framework import routers
from core.views import *
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView, TokenVerifyView

router = routers.DefaultRouter()
router.register(r'roles', RoleViewSet)
router.register(r'user-role', UserRoleViewSet, basename='userrole')
router.register(r'reports', ReportsViewSet, basename='reports')

urlpatterns = [
    path("", include(router.urls)),
    path("user/", get_user_by_phone, name="user"),
    path("signup/", signup_view, name="signup"),
    path("dev/otp/", generate_otp_dev, name="generate_otp_dev"),
    path("otp/", generate_otp, name="generate_otp"),
    path("login/", login_view, name="login"),
    path("logout/", logout_view, name="logout"),
    path('delete-account/', DeleteAccountView.as_view(), name='delete-account'),
    path("test/", protected_view, name="test"),
    path("api/token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("api/token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("api/token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    path('user-profile/', UserProfileView.as_view(), name='user-profile'),
    path('partner-profile/', PartnerProfileView.as_view(), name='partner-profile'),
]
