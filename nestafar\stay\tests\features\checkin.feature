Feature: Checkin

    Scenario: Checkin Property
        Given Setup User
            | name | phone      | password | partner |
            | partner1 | 0812345678 | 123456   | True    |
            | guest1   | 0812345679 | 123456   | False   |
        Given Add max_rooms to partner1
            | max_rooms |
            | 5         |
        Given Set Auth User to partner1
        Given Setup Locations
            | name      | description  | address     | latitude | longitude | type  | timezone     |
            | manali    | manali       | old manali  | 1.0      | 1.0       | HT | Asia/Kolkata |
            | kullu     | kullu        | kullu       | 1.0      | 1.0       | HT | Asia/Kolkata |
            | kasol     | kasol        | kasol       | 1.0      | 1.0       | HT | Asia/Kolkata |
            | bhuntar   | bhuntar      | bhuntar     | 1.0      | 1.0       | HT | Asia/Kolkata |
        Given Setup Properties
            | name      | description  | location | type  | avg_price | po_address | rooms  | rating  | meal_cost  |  directions  |
            | property1 | property1    | manali   | hotel | 1000      | manali     | 100    | 4.5     | 100        |  manali      |
            # | property2 | property2    | kullu    | hotel | 1000      | kullu      | 100    | 4.5     | 100        |  kullu       |
        Then Validate Properties
            | name      | description  | location | type  | avg_price | po_address | rooms  | rating  | meal_cost  |  directions  |
            | property1 | property1    | manali   | hotel | 1000      | manali     | 100    | 4.5     | 100        |  manali      |
            # | property2 | property2    | kullu    | hotel | 1000      | kullu      | 100    | 4.5     | 100        |  kullu       |
        Given Setup Rooms
            | property  | room_no  | description  | bed | max_guests  | rate   | type_of_room  |
            | property1 | 101      | room1        | 1   | 2           | 1000   | 1             |
            | property1 | 102      | room2        | 1   | 2           | 1000   | 1             |
            | property1 | 103      | room3        | 1   | 2           | 1000   | 1             |
            | property1 | 104      | room4        | 1   | 2           | 1000   | 1             |
            | property1 | 105      | room5        | 1   | 2           | 1000   | 1             |
        Given Initiate Checkin
            | room_no   | guests                                             |
            | 101       | {"raju":"9874204200","shyam": "9874214210"}        |
        Given Set Auth User to raju
        Then Checkin
        Given Set Auth User to shyam
        Then Checkin
        Given Set Auth User to partner1
        Given Initiate Checkin
            | room_no   | guests                                             |
            | 102       | {"jay":"9874224220","veeru": "9874234230"}         |
        Given Set Auth User to jay
        Then Checkin
        # Given Set Auth User to raju
        Then Get User Profile
        Then Validate Checkin
            | room_no   | guests                                             |
            | 101       | {"raju":"9874204200","shyam": "9874214210"}        |
            | 102       | {"jay":"9874224220"}                               |
        Given Set Auth User to partner1
        Given Checkout
            | room_no   | guests                                             |
            | 101       | {"raju":"9874204200","shyam": "9874214210"}        |
            | 102       | {"jay":"9874224220"}                               |
        Given Initiate Checkin
            | room_no   | guests                                             |
            | 101       | {"tony":"9874244240","stark": "9874254250"}        |
        Given Set Auth User to tony
        Then Checkin
        Given Set Auth User to stark
        Then Checkin
        Then Validate Checkin
            | room_no   | guests                                             |
            | 101       | {"tony":"9874244240","stark": "9874254250"}        |