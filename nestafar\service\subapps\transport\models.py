from django.db import models
from service.models import *
from geo.models import Location


class TransportService(BaseService):
    class VehicleType(models.TextChoices):
        AUTO = 'Auto'
        HATCHBACK = 'Hatchback'
        SEDAN = 'Sedan'
        SUV = 'SUV'
        VAN = 'Van'
        BUS = 'Bus'

    waiting_charge_rate = models.FloatField(default=0)  # per hour
    night_service = models.BooleanField(default=False)
    outstation = models.BooleanField(default=False)
    vehicle_type = models.CharField(max_length=100, choices=VehicleType.choices, default=VehicleType.SEDAN)

    def __str__(self):
        return self.name


class TransportServiceItem(BaseServiceItem):
    service = models.ForeignKey(TransportService, on_delete=models.CASCADE, related_name='service_items')
    stop_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='transport_stops')

    def __str__(self):
        return self.name


class TransportCart(BaseCart):
    pickup_time = models.DateTimeField(blank=True, null=True)
    pickup_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='pickup_carts',
                                        null=True)
    drop_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='drop_carts',
                                      null=True)

    def __str__(self):
        return self.guest.user.name + "_" + self.guest.room.room_no + "_" + str(self.total)


class TransportCartItems(BaseCartItems):
    cart = models.ForeignKey(TransportCart, on_delete=models.CASCADE, related_name='cart_items')
    item = models.ForeignKey(TransportServiceItem, on_delete=models.CASCADE, related_name='cart_items')
    waiting_time = models.FloatField(default=0)
    waiting_charge = models.FloatField(default=0)
    stop_no = models.IntegerField(default=1)

    class Meta:
        verbose_name_plural = "Transport cart items"

    def __str__(self):
        return self.item.name + " " + str(self.quantity) + " " + str(self.price)

    def add_item(self, commission, charges):
        self.cart.reset_cart()
        self.waiting_charge = round(self.waiting_time * self.item.service.waiting_charge_rate)
        charges += self.waiting_charge
        return super().add_item(commission, charges)

    def add_quantity(self, quantity):
        # Disabled adding quantity for transport cart items
        return self


class TransportOrder(BaseOrder):
    cart = models.ForeignKey(TransportCart, on_delete=models.CASCADE, related_name='orders')
    service = models.ForeignKey(TransportService, on_delete=models.CASCADE, related_name='orders')
    pickup_time = models.DateTimeField(blank=True, null=True)
    pickup_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='pickup_orders',
                                        null=True)
    drop_location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='drop_orders',
                                      null=True)

    def __str__(self):
        return self.guest.user.name + "_" + self.service.name


class TransportOrderItem(BaseOrderItem):
    item = models.ForeignKey(TransportServiceItem, on_delete=models.PROTECT, related_name='order_items')
    order = models.ForeignKey(TransportOrder, on_delete=models.PROTECT, related_name='order_items')
    waiting_time = models.FloatField(default=0)
    waiting_charge = models.FloatField(default=0)
    stop_no = models.IntegerField(default=1)  # to be used in multi-stop journeys

    def add_item(self, cart_item):
        self.waiting_charge = cart_item.waiting_charge
        self.waiting_time = cart_item.waiting_time
        self.stop_no = cart_item.stop_no
        self.save(update_fields=['waiting_charge', 'waiting_time', 'stop_no'])
        self.order.charges += cart_item.waiting_charge
        self.order.save(update_fields=['charges'])
        return super().add_item(cart_item)
