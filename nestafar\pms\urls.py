from django.urls import path, include
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>
from .views import RatePlanViewSet, AvailabilityViewSet, PolicyViewSet, RoomTypeViewSet

router = DefaultRouter()
router.register(r'rateplan', RatePlanViewSet, basename='pms-rateplan')
router.register(r'roomtypes', RoomTypeViewSet, basename='pms-roomtypes')
router.register(r'availability', AvailabilityViewSet, basename='pms-availability')

urlpatterns = [
    path('', include(router.urls)),
    path(
        'policy/',
        PolicyViewSet.as_view({
            'get': 'retrieve',
            'put': 'update',
        }),
        name='pms-policy'
    ),
]

