from rest_framework.permissions import BasePermission, SAFE_METHODS

class PartnerPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.is_partner

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)

class PropertyPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and getattr(request, 'property', None) is not None

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)

class ServicePermission(BasePermission):
    def has_permission(self, request, view):
        service_type = view.kwargs.get('service_type', None)
        user = request.user

        if service_type == 'all':
            return True
        elif service_type and hasattr(user, 'partner_profile'):
            return getattr(user.partner_profile, f'has_{service_type}', False)
        elif service_type and hasattr(request, 'property'):
            return request.property.staffs.filter(**{f'has_{service_type}': True}).exists()
        else:
            return False
        
    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)

class RolePermission(BasePermission):
    def has_permission(self, request, view):
        user = request.user
        property_id = view.kwargs.get('pk', None)

        if user.is_authenticated:
            user_permissions = user.get_perms(property_id)
            required_permissions = getattr(view, 'permission_required', [])
            #to handle cases where property_id is null in UserRole
            if property_id is None or user_permissions is None:
                return False

            return all(perm in user_permissions for perm in required_permissions)
        
        return False

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)

class ReadOnlyViewPermission(BasePermission):
    """
    Permission class for read-only views.
    """
    def has_permission(self, request, view):
        return request.method in SAFE_METHODS

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)

class FullAccessViewPermission(BasePermission):
    """
    Permission class for views that require full access.
    """
    def has_permission(self, request, view):
        return True

    def has_object_permission(self, request, view, obj):
        return self.has_permission(request, view)
