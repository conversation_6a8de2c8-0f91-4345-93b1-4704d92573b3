from pytest_bdd import given, when, then, parsers
from notification.models import (Notification, NotificationCategory, NotificationChannel, FirebaseDeviceToken,
                                 NotificationLog)
from core.models import User
from django.db.models import Q

@given(parsers.parse('Setup Notifications'))
def setup_notifications(context):
        Notification.objects.create(
            category=NotificationCategory.USER_CHECKIN_INITIATED.name,
            channel=NotificationChannel.MESSAGE.name,
            description="User checkin initiated",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_CHECKIN.name,
            channel=NotificationChannel.PUSH.name,
            description="User checkin",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_CHECKOUT.name,
            channel=NotificationChannel.PUSH.name,
            description="User checkout",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_ORDER_ACCEPTED.name,
            channel=NotificationChannel.PUSH.name,
            description="User order accepted",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_ORDER_CANCELLED.name,
            channel=NotificationChannel.PUSH.name,
            description="User order cancelled",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.USER_ORDER_COMPLETED.name,
            channel=NotificationChannel.PUSH.name,
            description="User order completed",
            user_type=Notification.UserTypeOptions.USER
        )
        Notification.objects.create(
            category=NotificationCategory.PARTNER_ORDER_PLACED.name,
            channel=NotificationChannel.PUSH.name,
            description="Partner order placed",
            user_type=Notification.UserTypeOptions.PARTNER
        )

@given(parsers.parse('Setup Firebase Tokens\n{table}'))
def setup_firebase_tokens(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        FirebaseDeviceToken.objects.create(
            user=User.objects.get(name=row['user']),
            token=row['token']
        )

@then(parsers.parse('Validate Notifications\n{table}'))
def validate_notifications(context, table):
    context.populate_table_from_str(table)
    for row in context.table:
        user = User.objects.get(name=row['user'])
        notifications = NotificationLog.objects.filter(Q(user_notification_profile__user__user=user)| Q(partner_notification_profile__partner__user=user))
        assert len(notifications) != 0