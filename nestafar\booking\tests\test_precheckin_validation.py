from django.test import TestCase
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from django.core.exceptions import ValidationError
from booking.models import PreCheckin, Reservation
from stay.models import Property
from geo.models import Location

User = get_user_model()

class PreCheckinReservationPropertyValidationTests(TestCase):
    def setUp(self):
        # Minimal related objects
        self.location = Location.objects.create(
            name='Loc1', administrative_area=None, postal_code='000000', country='IN'
        )
        self.user = User.objects.create(name='User A', phone='9999999999')
        self.property1 = Property.objects.create(location=self.location, name='Prop1', rooms=10)
        self.property2 = Property.objects.create(location=self.location, name='Prop2', rooms=5)
        self.reservation = Reservation.objects.create(
            user=self.user,
            property=self.property1,
            check_in=timezone.now() + timedelta(days=1),
            check_out=timezone.now() + timedelta(days=2),
            guests=1,
            total=100,
        )

    def test_precheckin_valid_same_property(self):
        pc = PreCheckin.objects.create(
            property=self.property1,
            reservation=self.reservation,
            expected_checkin=timezone.now() + timedelta(days=1),
            total_amount=100,
        )
        self.assertEqual(pc.reservation.property, pc.property)

    def test_precheckin_invalid_cross_property(self):
        pc= PreCheckin.objects.create(
            property=self.property2,
            reservation=self.reservation,
            expected_checkin=timezone.now() + timedelta(days=1),
            total_amount=100,
        )
        with self.assertRaises(ValidationError):
            pc.full_clean()
