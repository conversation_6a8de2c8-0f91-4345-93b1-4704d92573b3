Scenario Outline: Listing food services
  When I get the list of food services
  Then the response status code is 200
  And the response data contains a list of food services

  Examples:
    | Name           | Description                                         |
    |----------------|----------------------------------------------------|
    | All services   | Get all services                                      |
    | Vegetarian only | Get only vegetarian services                         |
    | Filter by name | Get services with a specific name (partial match)     |

Scenario Outline: Retrieving food service details
  Given a food service exists with ID "<service_id>"
  When I retrieve the food service details
  Then the response status code is 200
  And the response data contains the food service details

  Examples:
    | service_id |
    | 1          |
