from django.contrib import admin
from ..models import UserProfile, PartnerProfile
from django import forms
from django.core.exceptions import ValidationError

class PartnerProfileForm(forms.ModelForm):
    class Meta:
        model = PartnerProfile
        fields = '__all__'

    def clean_active_property(self):
        active_property = self.cleaned_data.get('active_property')
        if not active_property:
            raise ValidationError('Active property is required.')
        return active_property

@admin.register(PartnerProfile)
class PartnerProfileAdmin(admin.ModelAdmin):
    form = PartnerProfileForm

admin.site.register(UserProfile)

