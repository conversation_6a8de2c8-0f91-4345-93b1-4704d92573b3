from pytest_bdd import given, when, then, parsers
from django.urls import reverse
import json
from stay.models import Room, Guest
from io import StringIO, BytesIO
import logging
from core.models import User

logger=logging.getLogger(__name__)
@given(parsers.parse('Initiate Checkin\n{table}'))
def initiate_checkin(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    for row in context.table:
        guests_param = json.loads(row['guests'])
        room = Room.objects.get(room_no=row['room_no'])
        guests=[]
        for k,v in guests_param.items():
            guests.append({"name":k,"phone":v})
        payload = {
            "room_id": room.id,
            "guests": guests,
        }
        data = payload
        response = auth_client.post(reverse('stay:initiate-checkin'), data=data,
                                    format='json')
        assert response.status_code == 200


# @then(parsers.parse('Checkin With QR Key'))
# def checkin_with_qr_key(context):
#     auth_client = context.auth_client
#     qr_key = context.scratchpad['qr_key']
#     payload = {
#         "qr_key": qr_key,
#         "consent": True
#     }
#     response = auth_client.post(reverse('stay:checkin'), data=payload, format='json')
#     assert response.status_code == 200

@then(parsers.parse('Complete Checkin\n{table}'))
def complete_checkin(context,table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    row = [row for row in context.table][0]
    qr_key = context.scratchpad['qr_key']
    payload = {
        "qr_key": qr_key,
    }
    response = auth_client.post(reverse('stay:complete-checkin'), data=payload)
    assert response.status_code == int(row['response_code'])


@then(parsers.parse('Get User Profile'))
def get_user_profile(context):
    auth_client = context.auth_client
    response = auth_client.get(reverse('core:user-profile'))
    assert response.status_code == 200
    context.scratchpad['user_profile'] = response.json()['data']


@then(parsers.parse('Validate Checkin\n{table}'))
def validate_checkin(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    for row in context.table:
        room = Room.objects.get(room_no=row['room_no'])
        guests = json.loads(row['guests'])
        assert room.checked_in == True
        guests_n = room.guest.filter(checked_in=True, checked_out=False).count()
        assert guests_n == len(guests.keys())
        for k,v in guests.items():
            assert room.guest.filter(user__name=k, checked_in=True, checked_out=False).exists()
            assert room.guest.filter(user__name=k, checked_in=True, checked_out=False).first().user.phone == v
            guest = Guest.objects.get(user__name=k)
            assert guest.checked_in == True


@given(parsers.parse('Add max_rooms to {user}\n{table}'))
def add_max_rooms_to_user(context, user, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    user = User.objects.get(name=user)
    for row in context.table:
        user.partner_profile.max_rooms = row['max_rooms']
        user.partner_profile.save()

@then(parsers.parse('Checkin'))
def checkin(context):
    auth_client = context.auth_client
    response = auth_client.post(reverse('stay:checkin'))
    assert response.status_code == 200


@given(parsers.parse('Checkout\n{table}'))
def checkout(context, table):
    context.populate_table_from_str(table)
    auth_client = context.auth_client
    for row in context.table:
        room = Room.objects.get(room_no=row['room_no'])
        payload = {
            "room_id": room.id,
        }
        response = auth_client.post(reverse('stay:checkout'), data=payload,
                                    format='json')
        assert response.status_code == 200
