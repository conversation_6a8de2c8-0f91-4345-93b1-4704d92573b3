from django.core.management.base import BaseCommand, CommandError
from service.subapps.food.models import FoodService, FoodServiceItem

class Command(BaseCommand):
    help = 'Copy service items from one food service to another using their food service IDs'

    def add_arguments(self, parser):
        parser.add_argument('source_service_id', type=str, help='The ID of the source FoodService')
        parser.add_argument('target_service_id', type=str, help='The ID of the target FoodService')

    def handle(self, *args, **options):
        source_service_id = options['source_service_id']
        target_service_id = options['target_service_id']

        try:
            source_service = FoodService.objects.get(id=source_service_id)
        except FoodService.DoesNotExist:
            raise CommandError(f'Source FoodService with ID {source_service_id} does not exist')

        try:
            target_service = FoodService.objects.get(id=target_service_id)
        except FoodService.DoesNotExist:
            raise CommandError(f'Target FoodService with ID {target_service_id} does not exist')

        service_items = source_service.service_items.all()
        for item in service_items:
            new_item = FoodServiceItem(
                service=target_service,
                name=item.name,
                description=item.description,
                price=item.price,
                image=item.image,
                addon=item.addon,
                vegetarian=item.vegetarian,
                category=item.category,
                is_special=item.is_special,
                is_active=item.is_active,
                rating=item.rating
            )
            new_item.save()

        self.stdout.write(self.style.SUCCESS(f'Successfully copied {service_items.count()} items from FoodService {source_service_id} to {target_service_id}'))
