from pytest_bdd import given, parsers
from pytest_bdd.parsers import <PERSON><PERSON><PERSON><PERSON>, get_parser
from pytest_bdd.steps import get_step_fixture_name
from pytest_bdd.types import GIVEN
from pytest_bdd.utils import get_caller_module_locals

from core.models import User


def _given(name, converters=None, target_fixture=None):
    dec = None
    original_name = name
    if isinstance(name, StepParser):
        dec = given(name, converters, target_fixture)
    else:
        name = name(name + "\n{table}" if "\n" not in name else name)
        dec = given(parsers.parse(name), converters, target_fixture)

    def decorator(func):
        new_fixture_step_name = get_step_fixture_name(get_parser(name).name, GIVEN)
        original_fixture_step_name = get_step_fixture_name(
            get_parser(original_name).name, GIVEN
        )
        decorated = dec(func)
        caller_locals_level2 = get_caller_module_locals(depth=2)
        caller_locals_level1 = get_caller_module_locals(depth=1)
        caller_locals_level1[new_fixture_step_name] = caller_locals_level1[
            original_fixture_step_name
        ]
        caller_locals_level2[original_fixture_step_name] = caller_locals_level1[
            original_fixture_step_name
        ]
        return decorated

    return decorator


def step():
    def wrapper(func):
        def inner(*args, **kwargs):
            return func(*args, **kwargs)

        return inner

    return wrapper


def create_user(name, password, phone, partner=False):
    user = User.objects.create_user(
        name=name, phone=phone, password=password, partner=partner
    )
    return user


def table_text(text):
    return None if text == "null" or text.strip() == "" else text
