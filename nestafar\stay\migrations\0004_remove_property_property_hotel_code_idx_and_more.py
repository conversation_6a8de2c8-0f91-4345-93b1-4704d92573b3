# Generated by Django 4.2.7 on 2025-08-14 13:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0003_property_channel_managers_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='property',
            name='property_hotel_code_idx',
        ),
        migrations.RemoveField(
            model_name='property',
            name='hotel_code',
        ),
        migrations.AddField(
            model_name='property',
            name='policies',
            field=models.JSONField(blank=True, default=dict, help_text='Hotel policies for OTA compliance'),
        ),
    ]
