from django.contrib.auth.middleware import get_user
from django.utils.functional import SimpleLazyObject
from rest_framework_simplejwt.authentication import JWTAuthentication

ExemptedPaths = [
    '/core/signup/',
    '/core/dev/otp/',
    '/core/otp/',
    '/core/login/',
    '/stay/checkin/'
]

ExemptedPathsWithRequests = {
    "/stay/property/": "POST",
}


class JWTAuthenticationMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # request.user = SimpleLazyObject(lambda: self.__class__.get_jwt_user(request))
        request.user = self.__class__.get_jwt_user(request)
        return self.get_response(request)

    @staticmethod
    def get_jwt_user(request):
        if request.path in ExemptedPaths:
            return None
        user = get_user(request)
        if user is None or user.is_authenticated:
            return user
        try:
            jwt_authentication = JWTAuthentication()
            user, token = jwt_authentication.authenticate(request)
        except Exception as e:
            print(e)
        return user

