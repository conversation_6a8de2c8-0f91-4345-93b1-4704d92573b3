# Generated by Django 4.2.7 on 2025-07-19 07:37

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service', '0001_initial'),
        ('stay', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FoodCart',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Ordered'), (2, 'Partially Accepted'), (3, 'Accepted'), (4, 'Ongoing'), (5, 'Partially Rejected'), (6, 'Rejected'), (7, 'Cancelled'), (8, 'Completed'), (9, 'Incomplete')], default=0)),
                ('subtotal', models.FloatField(default=0)),
                ('taxes', models.FloatField(default=0)),
                ('charges', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
                ('order_created_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FoodOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Accepted'), (2, 'Ongoing'), (3, 'Rejected'), (4, 'Cancelled'), (5, 'Completed')], default=0)),
                ('subtotal', models.FloatField(default=0)),
                ('commissions', models.FloatField(default=0)),
                ('taxes', models.FloatField(default=0)),
                ('charges', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
                ('rating', models.FloatField(default=0)),
                ('review', models.TextField(default='')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='food.foodcart')),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FoodService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('charges', models.FloatField(blank=True, null=True)),
                ('tax_rate', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('veg_only', models.BooleanField(default=False)),
                ('active_days', models.JSONField(default=list)),
                ('opening_time', models.TimeField(blank=True, default=None, null=True)),
                ('closing_time', models.TimeField(blank=True, default=None, null=True)),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FoodServiceItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(blank=True, null=True, upload_to='service_item_images')),
                ('addon', models.JSONField(blank=True, null=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('price', models.FloatField()),
                ('is_active', models.BooleanField(default=True)),
                ('rating', models.FloatField(blank=True, default=0, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('vegetarian', models.BooleanField(default=False)),
                ('category', models.CharField(default='GEN', max_length=100)),
                ('is_special', models.BooleanField(default=False)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_items', to='food.foodservice')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='FoodOrderItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.IntegerField(default=1)),
                ('add_ons', models.JSONField(blank=True, default=dict, null=True)),
                ('price', models.FloatField(default=0)),
                ('rating', models.FloatField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='food.foodserviceitem')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='food.foodorder')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='foodorder',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='food.foodservice'),
        ),
        migrations.AddField(
            model_name='foodorder',
            name='service_partner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='service.servicepartner'),
        ),
        migrations.CreateModel(
            name='FoodCartItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('quantity', models.IntegerField(default=1)),
                ('price', models.FloatField(default=0)),
                ('ordered', models.BooleanField(default=False)),
                ('add_ons', models.JSONField(blank=True, default=list, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='food.foodcart')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='food.foodserviceitem')),
            ],
            options={
                'verbose_name_plural': 'Food cart items',
            },
        ),
        migrations.AddIndex(
            model_name='foodorder',
            index=models.Index(fields=['guest', 'cart'], name='food_foodor_guest_i_a62068_idx'),
        ),
    ]
