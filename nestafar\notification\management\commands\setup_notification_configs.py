from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.utils import DatabaseError, IntegrityError
from notification.models import Notification, NotificationChannel

class Command(BaseCommand):
    help = 'Setup notification configurations for all categories with WhatsApp channel'

    def handle(self, *args, **options):
        """Create notification configurations for all categories"""
        
        # Define all notification configurations
        notification_configs = [
            # Existing user notifications
            ('USER_CHECKIN_INITIATED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User check-in initiated notification'),
            ('USER_CHECKOUT', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User checkout notification'),
            ('USER_ORDER_PLACED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User order placed notification'),
            ('USER_ORDER_ACCEPTED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User order accepted notification'),
            ('USER_ORDER_ONGOING', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User order ongoing notification'),
            ('USER_ORDER_COMPLETED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User order completed notification'),
            ('USER_ORDER_CANCELLED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User order cancelled notification'),
            ('USER_ORDER_REJECTED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'User order rejected notification'),
            ('DAILY_SUMMARY_GUEST', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Daily summary for guests'),
            
            # Existing partner notifications
            ('PARTNER_ORDER_PLACED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Partner order placed notification'),
            ('PARTNER_ORDER_ACCEPTED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Partner order accepted notification'),
            ('PARTNER_ORDER_ONGOING', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Partner order ongoing notification'),
            ('PARTNER_ORDER_COMPLETED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Partner order completed notification'),
            ('PARTNER_ORDER_CANCELLED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Partner order cancelled notification'),
            ('PARTNER_ORDER_REJECTED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Partner order rejected notification'),
            ('PRE_CHECKIN_CREATED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Pre-checkin created notification'),
            ('PRECHECKIN_CONFIRMED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Pre-checkin confirmed notification'),
            ('PRECHECKIN_STATUS_CHANGED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Pre-checkin status changed notification'),
            ('DAILY_SUMMARY_PARTNER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Daily summary for partners'),
            
            # New onboarding flow notifications
            ('SIGNUP_SUCCESSFUL', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Successful signup welcome message'),
            ('ONBOARDING_REMINDER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Onboarding completion reminder'),
            ('ONBOARDING_COMPLETED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Onboarding completion congratulation'),
            
            # New check-in flow notifications
            ('PRECHECKIN_REMINDER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Pre-checkin completion reminder'),
            ('PRECHECKIN_CANCELLATION_WARNING', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Pre-checkin cancellation warning'),
            ('ROOM_ALLOTMENT', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Room allotment notification'),
            ('GUEST_ARRIVED_WELCOME', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Guest arrival welcome message'),
            ('CHECKOUT_BILL', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Checkout bill notification'),
            ('REVIEW_REQUEST', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Review request after checkout'),
            
            # New service management flow notifications
            ('NEW_SERVICE_AVAILABLE', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'New service availability notification'),
            ('DINNER_REMINDER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Daily dinner reminder'),
            ('VENDOR_ORDER_REMINDER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Vendor order status reminder'),
            ('ORDER_CONFIRMED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Order confirmation notification'),
            ('ORDER_READY', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Order ready notification'),
            ('VENDOR_NEW_ORDER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'New order notification for vendor'),
            
            # Weekly report
            ('WEEKLY_REPORT', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Weekly business performance report'),
            ('SERVICE_HIDDEN_NOTIFICATION', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Service hidden notification'),
            ('SERVICE_RESTORED_NOTIFICATION', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Service restored notification'),
            
            # Additional templates for comprehensive testing
            ('WELCOME_MESSAGE', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Guest welcome message'),
            ('CHECKOUT_REMINDER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Checkout reminder notification'),
            ('CHECKOUT_SUCCESSFUL', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Checkout completion notification'),
            ('SERVICE_REQUEST_RECEIVED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Service request received confirmation'),
            ('SERVICE_IN_PROGRESS', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Service in progress notification'),
            ('SERVICE_COMPLETED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Service completion notification'),
            ('FOOD_ORDER_PLACED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Food order placed confirmation'),
            ('FOOD_ORDER_CONFIRMED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Food order confirmed by vendor'),
            ('FOOD_ORDER_READY', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Food order ready for pickup/delivery'),
            
            # Additional missing configurations  
            ('CHECKIN_SUCCESSFUL', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Check-in successful confirmation'),
            ('RATING_REQUEST', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER, 'Rating and review request'),
            ('DAILY_SUMMARY', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Daily business summary'),
            ('WEEKLY_SUMMARY', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Weekly business summary'),
            ('MONTHLY_SUMMARY', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER, 'Monthly business summary'),
        ]
        
        created_count = 0
        updated_count = 0
        
        # Use database transaction for atomicity
        with transaction.atomic():
            for category, channel, user_type, description in notification_configs:
                try:
                    notification, created = Notification.objects.get_or_create(
                        category=category,
                        channel=channel,
                        user_type=user_type,
                        defaults={
                            'description': description,
                            'is_active': True
                        }
                    )
                    
                    if created:
                        created_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(f'✅ Created: {category} -> {channel} ({user_type})')
                        )
                    else:
                        # Update existing if needed using the object we already have
                        if not notification.is_active:
                            notification.is_active = True
                            notification.save(update_fields=['is_active'])
                            updated_count += 1
                            self.stdout.write(
                                self.style.WARNING(f'🔄 Updated: {category} -> {channel} ({user_type})')
                            )
                        else:
                            self.stdout.write(
                                self.style.HTTP_INFO(f'⏭️  Exists: {category} -> {channel} ({user_type})')
                            )
                            
                except (DatabaseError, IntegrityError) as e:
                    self.stdout.write(
                        self.style.ERROR(f'❌ Database error for {category}: {e}')
                    )
                    # Re-raise to trigger transaction rollback for data integrity
                    raise
                except ValueError as e:
                    self.stdout.write(
                        self.style.ERROR(f'❌ Invalid value for {category}: {e}')
                    )
                    continue
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'❌ Unexpected error for {category}: {e}')
                    )
                    continue        
        self.stdout.write(
            self.style.SUCCESS(f'\n📊 Summary: {created_count} created, {updated_count} updated')
        )
        
        # Show current notification count
        total_notifications = Notification.objects.count()
        whatsapp_notifications = Notification.objects.filter(channel=NotificationChannel.WHATSAPP.name).count()
        self.stdout.write(
            self.style.SUCCESS(f'📱 Total WhatsApp notifications: {whatsapp_notifications}/{total_notifications}')
        )
        active_notifications = Notification.objects.filter(is_active=True).count()
        
        self.stdout.write(
            self.style.SUCCESS(f'📈 Total notifications: {total_notifications} ({active_notifications} active)')
        )