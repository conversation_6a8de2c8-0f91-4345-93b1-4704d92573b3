Scenario Outline: Creating a transport order from a transport cart
  Given I am an authenticated user with a transport cart containing items
  When I create a transport order
  Then the response status code is 201
  And a new transport order is created with the cart items details

Scenario Outline: Retrieving the details of a specific transport order
  Given a transport order exists with ID '<order_id>'
  When I retrieve the details of the transport order
  Then the response status code is 200
  And the response data contains the transport order details, including:
    * Guest information
    * Service information
    * Ordered transport service items

Scenario Outline: Updating a transport order (Optional)
  Given a transport order exists with ID '20202'
  When I update the transport order with "<field_to_update>" set to "12123" (Optional)
  Then the response status code is 200
  And the transport order is updated with the new value

  Examples:
    | field_to_update        | new_value          |
    | pickup_location        | "e9c27f3a-8b69-41f5-9623-b74e64b89b12" |
    | drop_location          | "bf18f716-6cfa-41c3-83ec-cb189d2c7497" |
    | pickup_time (optional)  | "2024-05-15T10:00:00" | (Optional, requires modifying order)
