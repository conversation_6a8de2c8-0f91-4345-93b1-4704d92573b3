"""
Channel Manager Webhook Views for handling reservation updates from multiple OTAs
"""
import logging
import re
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from booking.channel_managers.factory import ChannelManagerFactory

logger = logging.getLogger(__name__)

# Define allowed channel manager names (whitelist approach)
ALLOWED_CHANNEL_MANAGERS = {
    'aiosell',
    'booking_com',
    'expedia',
    'agoda',
    'airbnb'
}

# Pattern for valid channel names (alphanumeric and underscores only)
CHANNEL_NAME_PATTERN = re.compile(r'^[a-zA-Z0-9_]+$')


def validate_channel_name(channel_name: str) -> tuple[bool, str]:
    """
    Validate the channel_name parameter for security and format compliance.
    
    Args:
        channel_name: The channel manager name to validate
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check if channel_name is provided and is a string
    if not channel_name or not isinstance(channel_name, str):
        return False, "Channel name must be a non-empty string"
    
    # Check length constraints
    if len(channel_name) > 50:
        return False, "Channel name too long (max 50 characters)"
    
    # Check character pattern (only alphanumeric and underscores)
    if not CHANNEL_NAME_PATTERN.match(channel_name):
        return False, "Channel name contains invalid characters (only alphanumeric and underscores allowed)"
    
    # Check against whitelist of allowed channel managers
    if channel_name.lower() not in ALLOWED_CHANNEL_MANAGERS:
        return False, f"Channel manager '{channel_name}' is not supported"
    
    return True, ""


@api_view(['POST'])
@permission_classes([AllowAny])
def channel_manager_webhook(request, channel_name):
    """
    Generic webhook endpoint for channel managers to send reservation updates.
    Routes requests to the appropriate channel manager adapter.

    Args:
        request: Django HTTP request
        channel_name: Name of the channel manager (e.g., 'aiosell', 'booking_com')
    """
    try:
        # Validate channel_name parameter before processing
        is_valid, error_message = validate_channel_name(channel_name)
        if not is_valid:
            logger.warning(f"Invalid channel name received: {channel_name} - {error_message}")
            return Response({
                'success': False,
                'message': f'Invalid channel name: {error_message}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get the appropriate channel manager adapter
        adapter = ChannelManagerFactory.create_adapter(channel_name)

        if adapter is None:
            return Response({
                'success': False,
                'message': f'Unknown channel manager: {channel_name}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Process the webhook using the adapter
        return adapter.process_webhook(request)

    except Exception as e:
        logger.error(f"Channel manager webhook error ({channel_name}): {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)




