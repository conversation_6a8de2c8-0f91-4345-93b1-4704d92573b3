from service.models import BaseService, BaseServiceItem, BaseCart, BaseCartItems, BaseOrder, BaseOrderItem
from django.db import models


class ShopService(BaseService):
    def __str__(self):
        return self.name + " by " + self.partner.name


class ShopServiceItem(BaseServiceItem):
    service = models.ForeignKey(ShopService, on_delete=models.CASCADE, related_name='service_items')
    is_featured = models.BooleanField(default=False)
    category = models.CharField(max_length=100, default="GEN")

    def __str__(self):
        return self.name


class ShopCart(BaseCart):

    def __str__(self):
        return self.guest.user.name + " " + self.guest.room.property.name + " " + str(self.total)


class ShopCartItems(BaseCartItems):
    item = models.ForeignKey(ShopServiceItem, on_delete=models.CASCADE, related_name='cart_items')
    cart = models.ForeignKey(ShopCart, on_delete=models.CASCADE, related_name='cart_items')
    class Meta:
        verbose_name_plural = "Shop cart items"


class ShopOrder(BaseOrder):
    cart = models.ForeignKey(ShopCart, on_delete=models.CASCADE, related_name='orders')
    service = models.ForeignKey(ShopService, on_delete=models.CASCADE, related_name='orders')

    def __str__(self):
        return self.guest.user.name + "_" + self.service.name + "_"


class ShopOrderItem(BaseOrderItem):
    item = models.ForeignKey(ShopServiceItem, on_delete=models.CASCADE, related_name='order_items')
    order = models.ForeignKey(ShopOrder, on_delete=models.CASCADE, related_name='order_items')

    def __str__(self):
        return self.item.name + "_" + self.order.guest.user.name + "_" + self.order.service.name
