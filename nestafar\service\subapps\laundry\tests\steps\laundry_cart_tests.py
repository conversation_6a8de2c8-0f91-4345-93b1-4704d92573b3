from pytest_bdd import scenario, given, then, when
from models import LaundryCart, LaundryCartItems, LaundryServiceItem

@scenario("features/laundry_cart_test.feature")
def test_manage_laundry_cart(client, laundry_service_factory, user):
    pass

@given("I am an authenticated user")
def authenticate(client, user):
    # Simulate user authentication here 
    client.force_authenticate(user=user)

@given("a laundry cart exists for the user")
def create_laundry_cart(client, user):
    cart = LaundryCart.objects.create(guest=user.guest)
    return cart

@when("I add a laundry service item to the cart")
def add_laundry_item_to_cart(client, cart, laundry_service_factory):
    item = laundry_service_factory()
    data = {"item": item.id, "quantity": 1}
    response = client.post(f"/api/laundry-carts/{cart.id}/items/", data=data, format="json")
    response.json()  # trigger data parsing

@then("the response status code is 201")
def check_add_item_status_code(response):
    assert response.status_code == 201

@then("And the response data contains the added cart item details")
def check_add_item_response(response):
    data = response.json()
    assert data["item"] == data["item"]["id"]
    # Add assertions for other expected cart item data

@when("I remove a laundry service item from the cart")
def remove_laundry_item_from_cart(client, cart, laundry_service_factory):
    item = laundry_service_factory()
    LaundryCartItems.objects.create(cart=cart, item=item, quantity=1)
    item_id = item.id
    response = client.delete(f"/api/laundry-carts/{cart.id}/items/{item_id}/")

@then("the response status code is 200")
def check_remove_item_status_code(response):
    assert response.status_code == 200

@then("And the removed item is no longer present in the cart")
def check_remove_item_from_cart(client, cart):
    removed_items = LaundryCartItems.objects.filter(cart=cart)
    assert len(removed_items) == 0

@when("I try to add an invalid item to the cart")
def add_invalid_item_to_cart(client, cart):
    data = {"item": 9999, "quantity": 1}  # Invalid item ID
    response = client.post(f"/api/laundry-carts/{cart.id}/items/", data=data, format="json")

@then("And the response data contains error messages")
def check_invalid_item_error(response):
    data = response.json()
    assert "item" in data["errors"]
