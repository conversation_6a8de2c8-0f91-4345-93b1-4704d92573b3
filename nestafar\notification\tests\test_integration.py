import unittest
from unittest.mock import Mock, patch
from django.test import TestCase, override_settings
from core.models import User
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from stay.models import Guest, Room, Property, PropertyPartner
from booking.models import <PERSON><PERSON><PERSON>ckin, PreCheckinGuest
from notification.models.main import Notification, NotificationChannel, WhatsAppContact, NotificationCategory
from notification.tasks import send_notification
from service.models.service import ServicePartner
from service.subapps.food.models import FoodService, FoodCart, FoodOrder
from geo.models import Location


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
    CELERY_BROKER_URL='memory://',
    CELERY_RESULT_BACKEND='cache+memory://',
)
class NotificationIntegrationTestCase(TestCase):
    """
    Integration tests for the complete notification system including WhatsApp
    """
    
    def setUp(self):
        """Set up test data"""
        # Create location
        self.location = Location.objects.create(
            name="Test Location",
            latitude=12.9716,
            longitude=77.5946
        )
        
        # Create users
        self.guest_user = User.objects.create_user(
            phone='9876543210',
            name='Test Guest',
            password='testpass123'
        )
        
        self.partner_user = User.objects.create_user(
            phone='9876543211',
            name='Test Partner',
            password='testpass123',
            partner=True
        )
        
        self.property_owner = User.objects.create_user(
            phone='9876543212',
            name='Property Owner',
            password='testpass123',
            partner=True
        )
        
        # Create property
        self.property = Property.objects.create(
            name="Test Property",
            location=self.location,
            rooms=10,
            avg_price=100.0,
        )
        self.property.staffs.add(self.property_owner.partner_profile)
        
        # Create room
        self.room = Room.objects.create(
            property=self.property,
            room_no="101",
            rate=100.0,
            max_guests=2
        )
        
        # Create guest
        self.guest = Guest.objects.create(
            user=self.guest_user,
            room=self.room,
            checkin_key="test_key",
            checked_in=True
        )
        
        # Create service partner
        self.service_partner = ServicePartner.objects.create(
            location=self.location,
            name="FoodPartner1",
            type_of_service=ServicePartner.PartnerTypes.FOOD,
            description="Food partner description",
            phone_number='+919876543210',
        )
        
        # Create property partner to link with service partner
        self.property_partner = PropertyPartner.objects.create(
            property=self.property,
            partner=self.service_partner,
            commission=10.0,
            name="Food Partner",
            in_house=False
        )
        
        # Create food service
        self.food_service = FoodService.objects.create(
            partner=self.service_partner,
            name="Test Food Service",
            active_days=["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
            opening_time="9:00",
            closing_time="18:00",
            is_active=True
        )
        
        # Create WhatsApp contacts
        WhatsAppContact.objects.create(
            user=self.guest_user,
            phone_number='+919876543210',
            is_active=True,
            is_verified=True
        )
        
        WhatsAppContact.objects.create(
            user=self.partner_user,
            phone_number='+919876543211',
            is_active=True,
            is_verified=True
        )
        
        WhatsAppContact.objects.create(
            user=self.property_owner,
            phone_number='+919876543212',
            is_active=True,
            is_verified=True
        )
        
        # Create notifications
        self.create_test_notifications()
    
    def create_test_notifications(self):
        """Create test notification configurations"""
        notification_configs = [
            ('USER_ORDER_PLACED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('USER_ORDER_ACCEPTED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('USER_ORDER_COMPLETED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('PARTNER_ORDER_PLACED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
            ('PARTNER_ORDER_ACCEPTED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
            ('PRE_CHECKIN_CREATED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
            ('PRECHECKIN_STATUS_CHANGED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
            ('USER_CHECKIN_INITIATED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('USER_CHECKOUT', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('DAILY_SUMMARY_GUEST', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('DAILY_SUMMARY_PARTNER', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
        ]
        
        for category, channel, user_type in notification_configs:
            Notification.objects.create(
                category=category,
                channel=channel,
                description=f"Test {category} notification",
                user_type=user_type
            )
    
    @patch('notification.tasks.send_notification.delay')
    def test_pre_checkin_created_notification(self, mock_send_notification):
        """Test notification when pre-checkin is created"""
        
        # Create pre-checkin
        pre_checkin = PreCheckin.objects.create(
            property=self.property,
            expected_checkin=timezone.now() + timedelta(days=1),
            status='pending'
        )
        
        # Create pre-checkin guest
        PreCheckinGuest.objects.create(
            pre_checkin=pre_checkin,
            user=self.guest_user,
            is_primary=True
        )
        
        # Manually trigger the signal since we're using eager mode
        from notification.signals import precheckin_notification_handler
        precheckin_notification_handler(
            sender=PreCheckin,
            instance=pre_checkin,
            created=True
        )
        
        # Verify notification task was called
        mock_send_notification.assert_called()
    
    @patch('notification.tasks.send_notification.delay')
    def test_guest_checkin_notification(self, mock_send_notification):
        """Test notification when guest checks in"""
        
        # Create a new guest (not checked in)
        new_guest = Guest.objects.create(
            user=self.guest_user,
            room=self.room,
            checkin_key="new_key",
            checked_in=False
        )
        
        # Reset mock after creation
        mock_send_notification.reset_mock()
        
        # Create a mock original instance
        from unittest.mock import MagicMock
        original_guest = MagicMock()
        original_guest.checked_in = False
        
        # Mock the objects.get call to return our mock
        with patch.object(Guest.objects, 'get', return_value=original_guest):
            # Update to checked in and trigger signal manually
            new_guest.checked_in = True
            new_guest.save()
            
            # Manually trigger the signal since we're using eager mode
            from notification.signals import guest_checkin_notification_handler
            guest_checkin_notification_handler(
                sender=Guest,
                instance=new_guest,
                created=False
            )
        
        # Verify notification task was called
        mock_send_notification.assert_called()
    
    @patch('notification.tasks.send_notification.delay')
    @patch('notification.tasks.send_service_partner_notification.delay')
    def test_order_notifications(self, mock_send_partner_notification, mock_send_notification):
        """Test order creation and status change notifications"""
        
        # Create food cart
        cart = FoodCart.objects.create(
            guest=self.guest,
            status=FoodCart.CartStatus.PENDING
        )
        
        # Create food order
        order = FoodOrder.objects.create(
            guest=self.guest,
            service_partner=self.service_partner,
            cart=cart,
            service=self.food_service,
            status=FoodOrder.OrderStatus.PENDING,
            total=100.0
        )
        
        # Manually trigger the signal since we're using eager mode
        from service.signals import update_cart_status
        update_cart_status(
            sender=FoodOrder,
            instance=order,
            created=True
        )
        
        # Verify notification task was called for order creation (guest notification)
        self.assertTrue(mock_send_notification.called)
        # Verify service partner notification was also called
        self.assertTrue(mock_send_partner_notification.called)
        
        # Reset mocks
        mock_send_notification.reset_mock()
        mock_send_partner_notification.reset_mock()
        
        # Update order status
        order.status = FoodOrder.OrderStatus.ACCEPTED
        order.save()
        
        # Manually trigger the signal for status change
        update_cart_status(
            sender=FoodOrder,
            instance=order,
            created=False
        )
    
        # Verify both notifications are sent for status change
        self.assertTrue(mock_send_notification.called)
        self.assertTrue(mock_send_partner_notification.called) 

   
    @patch('notification.tasks.send_notification.delay')    
    def test_daily_summary_notifications(self, mock_send_notification):
        """Test daily summary notifications"""
        
        # Create some test orders from yesterday
        yesterday = timezone.now() - timedelta(days=1)
        
        # Create cart
        cart = FoodCart.objects.create(
            guest=self.guest,
            status=FoodCart.CartStatus.PENDING
        )
        
        # Create order with yesterday's date
        order = FoodOrder.objects.create(
            guest=self.guest,
            service_partner=self.service_partner,
            cart=cart,
            service=self.food_service,
            status=FoodOrder.OrderStatus.COMPLETED,
            total=100.0,
            created_at=yesterday
        )
        
        # Call the task
        from notification.signals import send_daily_summary_notifications
        send_daily_summary_notifications()
        
        # Verify notification was sent
        self.assertTrue(mock_send_notification.called)
        args = mock_send_notification.call_args_list
        
        # Should have at least one call for guest summary
        guest_summary_calls = [call for call in args if call[0][1] == 'DAILY_SUMMARY_GUEST']
        self.assertTrue(len(guest_summary_calls) > 0)
    
    @patch('notification.tasks.send_notification.delay')
    def test_onboarding_flow(self, mock_send_notification):
        """Test complete onboarding flow"""
        from notification.models.onboarding import OnboardingStatus
        from notification.tasks.flow_tasks import (
            send_signup_welcome_message, 
            check_onboarding_reminders,
            check_and_update_onboarding_status
        )
        
        # Create onboarding status
        onboarding = OnboardingStatus.objects.create(
            partner=self.partner_profile,
            property=self.property
        )
        
        # Test signup welcome
        send_signup_welcome_message(str(self.partner_profile.id), str(self.property.id))
        
        # Verify signup welcome notification was sent
        mock_send_notification.assert_called()
        args = mock_send_notification.call_args_list[-1]
        self.assertEqual(args[0][1], 'SIGNUP_SUCCESSFUL')
        
        # Test reminder check
        check_onboarding_reminders()
        
        # Should send reminder for incomplete onboarding
        reminder_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'ONBOARDING_REMINDER']
        self.assertTrue(len(reminder_calls) > 0)
        
        # Complete onboarding steps
        onboarding.property_details_added = True
        onboarding.property_photos_uploaded = True
        onboarding.rooms_added = True
        onboarding.room_photos_uploaded = True
        onboarding.services_added = True
        onboarding.save()
        
        # Test completion check
        check_and_update_onboarding_status(str(self.partner_profile.id), str(self.property.id))
        
        # Should send completion notification
        completion_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'ONBOARDING_COMPLETED']
        self.assertTrue(len(completion_calls) > 0)
    
    @patch('notification.tasks.send_notification.delay')
    def test_precheckin_flow(self, mock_send_notification):
        """Test pre-checkin flow with reminders and warnings"""
        from notification.tasks.flow_tasks import check_precheckin_reminders
        
        # Create pre-checkin for tomorrow
        tomorrow = timezone.now() + timedelta(days=1)
        
        # Create a pending pre-checkin
        self.precheckin.status = 'pending'
        self.precheckin.expected_checkin = tomorrow
        self.precheckin.save()
        
        # Test reminder check
        check_precheckin_reminders()
        
        # Should send reminder notification
        reminder_calls = [call for call in mock_send_notification.call_args_list 
                         if call[0][1] in ['PRECHECKIN_REMINDER', 'PRECHECKIN_CANCELLATION_WARNING']]
        self.assertTrue(len(reminder_calls) > 0)
    
    @patch('notification.tasks.send_notification.delay')
    def test_service_management_flow(self, mock_send_notification):
        """Test service management notifications"""
        from notification.tasks.flow_tasks import (
            send_dinner_reminders,
            check_vendor_order_reminders,
            notify_service_change
        )
        
        # Test dinner reminders
        send_dinner_reminders()
        
        dinner_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'DINNER_REMINDER']
        self.assertTrue(len(dinner_calls) > 0)
        
        # Test vendor order reminders
        # Create a pending order from 20 minutes ago
        old_time = timezone.now() - timedelta(minutes=20)
        
        cart = FoodCart.objects.create(
            guest=self.guest,
            status=FoodCart.CartStatus.PENDING
        )
        
        order = FoodOrder.objects.create(
            guest=self.guest,
            service_partner=self.service_partner,
            cart=cart,
            service=self.food_service,
            status=FoodOrder.OrderStatus.PENDING,
            total=100.0,
            created_at=old_time
        )
        
        check_vendor_order_reminders()
        
        # Should send vendor reminder
        vendor_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'VENDOR_ORDER_REMINDER']
        self.assertTrue(len(vendor_calls) > 0)
        
        # Test service visibility change notifications
        notify_service_change(str(self.service_partner.id), 'hidden', str(self.property.id))
        
        service_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'SERVICE_HIDDEN_NOTIFICATION']
        self.assertTrue(len(service_calls) > 0)
    
    @patch('notification.tasks.send_notification.delay')
    def test_weekly_report_flow(self, mock_send_notification):
        """Test weekly report generation"""
        from notification.tasks.flow_tasks import generate_weekly_reports
        
        # Create some historical data
        last_week = timezone.now() - timedelta(days=7)
        
        # Create booking/reservation data
        # (This would need to be adapted based on your actual models)
        
        # Generate reports
        generate_weekly_reports()
        
        # Should send weekly report
        report_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'WEEKLY_REPORT']
        self.assertTrue(len(report_calls) > 0)
    
    @patch('notification.tasks.send_notification.delay')
    def test_guest_checkout_flow(self, mock_send_notification):
        """Test complete guest checkout flow"""
        from notification.tasks.flow_tasks import process_guest_checkout_flow
        
        # Set guest as checked out
        self.guest.checked_out = True
        self.guest.check_out_date = timezone.now()
        self.guest.save()
        
        # Process checkout flow
        process_guest_checkout_flow(str(self.guest.id))
        
        # Should send checkout bill notification
        bill_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'CHECKOUT_BILL']
        self.assertTrue(len(bill_calls) > 0)
        
        # Review request should be scheduled (we can't easily test the delayed task here)
    
    @patch('notification.tasks.send_notification.delay')
    def test_room_allotment_and_welcome(self, mock_send_notification):
        """Test room allotment and guest welcome flow"""
        from notification.tasks.flow_tasks import (
            send_room_allotment_notification,
            send_guest_arrived_welcome
        )
        
        # Test room allotment
        send_room_allotment_notification(str(self.guest.id), str(self.room.id))
        
        allotment_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'ROOM_ALLOTMENT']
        self.assertTrue(len(allotment_calls) > 0)
        
        # Test guest arrival welcome
        send_guest_arrived_welcome(str(self.guest.id))
        
        welcome_calls = [call for call in mock_send_notification.call_args_list if call[0][1] == 'GUEST_ARRIVED_WELCOME']
        self.assertTrue(len(welcome_calls) > 0)
        
        # Reset mock to ignore creation notifications
        mock_send_notification.reset_mock()
        
        # Run daily summary task
        from notification.signals import send_daily_summary_notifications
        send_daily_summary_notifications()
        
        # Verify notification task was called for daily summary
        self.assertTrue(mock_send_notification.called)
    
    def test_whatsapp_phone_number_formatting(self):
        """Test phone number formatting for WhatsApp"""
        from notification.channel.whatsapp import WhatsAppChannel
        
        with patch('django.conf.settings.WHATSAPP_ACCESS_TOKEN', 'test_token'), \
             patch('django.conf.settings.WHATSAPP_PHONE_NUMBER_ID', '123456789'):
            
            channel = WhatsAppChannel()
            
            # Test various phone number formats
            test_cases = [
                ('9876543210', '919876543210'),  # 10 digit
                ('919876543210', '919876543210'),  # Already formatted
                ('+91-9876543210', '919876543210'),  # With special chars
                ('+91 9876 543 210', '919876543210'),  # With spaces
            ]
            
            for input_num, expected in test_cases:
                result = channel.format_phone_number(input_num)
                self.assertEqual(result, expected)
    
    def test_notification_template_validation(self):
        """Test that all notification templates are properly configured"""
        from notification.models.main import NotificationTemplates
        
        required_templates = [
            'USER_CHECKIN_INITIATED',
            'USER_CHECKOUT',
            'USER_ORDER_PLACED',
            'USER_ORDER_ACCEPTED',
            'USER_ORDER_COMPLETED',
            'PARTNER_ORDER_PLACED',
            'PARTNER_ORDER_ACCEPTED',
            'PRE_CHECKIN_CREATED',
            'PRECHECKIN_STATUS_CHANGED',
            'DAILY_SUMMARY_GUEST',
            'DAILY_SUMMARY_PARTNER',
            # New WhatsApp templates
            'SIGNUP_SUCCESSFUL',
            'ONBOARDING_REMINDER',
            'PRECHECKIN_REMINDER',
            'PRECHECKIN_CANCELLATION_WARNING',
            'CHECKIN_SUCCESSFUL',
            'CHECKOUT_BILL',
            'REVIEW_REQUEST',
            'ORDER_CONFIRMED',
            'ORDER_READY',
            'VENDOR_NEW_ORDER',
            'VENDOR_ORDER_REMINDER',
            'DINNER_REMINDER',
            'WEEKLY_REPORT',
            'SERVICE_HIDDEN_NOTIFICATION',
            'SERVICE_RESTORED_NOTIFICATION',
        ]
        
        for template_name in required_templates:
            self.assertIn(template_name, NotificationTemplates)
            template_data = NotificationTemplates[template_name]
            self.assertIsInstance(template_data, tuple)
            self.assertEqual(len(template_data), 2)  # Function and parameters
            self.assertTrue(callable(template_data[0]))  # Template function
            self.assertIsInstance(template_data[1], dict)  # Parameters dict
    
    @patch('notification.tasks.send_notification.delay')
    def test_notification_error_handling(self, mock_send_notification):
        """Test notification error handling"""
        
        # Import the function at the beginning of the test
        from notification.tasks import send_notification
        
        # Test with invalid category
        with self.assertRaises(ValueError):
            send_notification('invalid_user_id', 'INVALID_CATEGORY', {})
        
        # Test with missing required data
        with self.assertRaises(Exception):
            send_notification(str(self.guest_user.id), 'USER_ORDER_PLACED', {})  # Missing order_id
        
        # Verify error handling doesn't break the system
        self.assertTrue(True)  # Test passes if no uncaught exceptions
        """Test error handling in notification system"""
        
        # Configure the mock to return a result
        mock_send_notification.return_value = None
        
        # Try to send notification with proper mocking
        from notification.tasks import send_notification
        
        with patch('notification.tasks.send_notification') as mock_task:
            mock_task.return_value = (True, 'Notification sent successfully')
            
            result = mock_task(
                str(self.guest_user.id),
                'USER_ORDER_PLACED',
                {'username': 'Test', 'order_id': 'ORDER123'}
            )
            
            # Should handle error gracefully
            self.assertTrue(isinstance(result, tuple))
            self.assertEqual(len(result), 2)


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
    CELERY_BROKER_URL='memory://',
    CELERY_RESULT_BACKEND='cache+memory://',
)
class WhatsAppChannelPerformanceTestCase(TestCase):
    """Test performance aspects of WhatsApp channel"""
    
    def setUp(self):
        """Set up test data"""
        self.users = []
        for i in range(10):
            user = User.objects.create_user(
                phone=f'98765432{i:02d}',
                name=f'User {i}',
                password='testpass123'
            )
            self.users.append(user)
            
            WhatsAppContact.objects.create(
                user=user,
                phone_number=f'+9198765432{i:02d}',
                is_active=True,
                is_verified=True
            )
    
    @patch('notification.tasks.send_notification.delay')
    def test_bulk_notification_performance(self, mock_send_notification):
        """Test performance with bulk notifications"""
        
        import time
        start_time = time.time()
        
        # Send notifications to all users
        for user in self.users:
            # Trigger the task through the actual code path
            from notification.tasks import send_notification
            send_notification.delay(
                str(user.id),
                'USER_ORDER_PLACED',
                {'username': user.name, 'order_id': 'ORDER123'}
            )
        
        end_time = time.time()
        
        # Should complete within reasonable time
        self.assertLess(end_time - start_time, 5.0)  # 5 seconds for 10 notifications
        
        # Verify all calls were made
        self.assertEqual(mock_send_notification.call_count, 10)

@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
    CELERY_BROKER_URL='memory://',
    CELERY_RESULT_BACKEND='cache+memory://',
    WHATSAPP_ACCESS_TOKEN='test_token',
    WHATSAPP_PHONE_NUMBER_ID='123456789',
)
class WhatsAppChannelTestCase(TestCase):
    """Test the WhatsApp channel specifically"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone='9876543210',
            name='Test User',
            password='testpass123'
        )
        
        # Create WhatsApp contact
        self.whatsapp_contact = WhatsAppContact.objects.create(
            user=self.user,
            phone_number='+919876543210',
            is_active=True,
            is_verified=True
        )
    
    def test_whatsapp_channel_initialization(self):
        """Test WhatsApp channel initialization"""
        from notification.channel.whatsapp import WhatsAppChannel
        
        channel = WhatsAppChannel()
        self.assertEqual(channel.access_token, 'test_token')
        self.assertEqual(channel.phone_number_id, '123456789')
        self.assertEqual(channel.api_version, 'v22.0')
        self.assertEqual(channel.base_url, 'https://graph.facebook.com/v22.0')
    
    def test_get_user_whatsapp_number(self):
        """Test getting WhatsApp number for user"""
        from notification.channel.whatsapp import WhatsAppChannel
        
        channel = WhatsAppChannel()
        number = channel.get_user_whatsapp_number(self.user.id)
        self.assertEqual(number, '+919876543210')
    
    def test_format_phone_number(self):
        """Test phone number formatting"""
        from notification.channel.whatsapp import WhatsAppChannel
        
        channel = WhatsAppChannel()
        
        test_cases = [
            ('9876543210', '919876543210'),
            ('919876543210', '919876543210'),
            ('+91-9876543210', '919876543210'),
            ('+91 9876 543 210', '919876543210'),
        ]
        
        for input_num, expected in test_cases:
            result = channel.format_phone_number(input_num)
            self.assertEqual(result, expected)
    
    def test_create_message(self):
        """Test message creation"""
        from notification.channel.whatsapp import WhatsAppChannel
        
        channel = WhatsAppChannel()
        result = channel.create_message(self.user.id, 'Test Title', 'Test Body')
        
        self.assertTrue(result)
        self.assertIn('messaging_product', channel.message_payload)
        self.assertEqual(channel.message_payload['messaging_product'], 'whatsapp')
        self.assertEqual(channel.message_payload['to'], '919876543210')
        self.assertEqual(channel.message_payload['type'], 'text')
        self.assertEqual(channel.message_payload['text']['body'], '*Test Title*\n\nTest Body')
    
    @patch('notification.channel.whatsapp.requests.post')
    def test_send_message_success(self, mock_post):
        """Test successful message sending"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'messages': [{'id': 'test_message_id'}]}
        mock_post.return_value = mock_response
        
        from notification.channel.whatsapp import WhatsAppChannel
        
        channel = WhatsAppChannel()
        success, message, message_id = channel.send_message(
            self.user.id, 
            'Test Body',
            title='Test Title'
        )
        
        self.assertTrue(success)
        self.assertEqual(message_id, 'test_message_id')
        mock_post.assert_called_once()
    
    @patch('notification.channel.whatsapp.requests.post')
    def test_send_message_error(self, mock_post):
        """Test message sending with error"""
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.text = 'Bad Request'
        mock_post.return_value = mock_response
        
        from notification.channel.whatsapp import WhatsAppChannel
        
        channel = WhatsAppChannel()
        success, error_msg, message_id = channel.send_message(
            self.user.id, 
            'Test Body',
            title='Test Title'
        )
        
        self.assertFalse(success)
        self.assertIn('WhatsApp API error', error_msg)
        self.assertIsNone(message_id)

@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
    CELERY_BROKER_URL='memory://',
    CELERY_RESULT_BACKEND='cache+memory://',
)
class NotificationSignalTestCase(TestCase):
    """Test notification signals"""
    
    def setUp(self):
        """Set up test data"""
        # Create location
        self.location = Location.objects.create(
            name="Test Location",
            latitude=12.9716,
            longitude=77.5946
        )
        
        # Create users
        self.guest_user = User.objects.create_user(
            phone='9876543210',
            name='Test Guest',
            password='testpass123'
        )
        
        self.partner_user = User.objects.create_user(
            phone='9876543211',
            name='Test Partner',
            password='testpass123',
            partner=True
        )
        
        # Create property
        self.property = Property.objects.create(
            name="Test Property",
            location=self.location,
            rooms=10,
            avg_price=100.0,
        )
        self.property.staffs.add(self.partner_user.partner_profile)
        
        # Create room
        self.room = Room.objects.create(
            property=self.property,
            room_no="101",
            rate=100.0,
            max_guests=2
        )
        
        # Create notifications
        notification_configs = [
            ('PRE_CHECKIN_CREATED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
            ('PRECHECKIN_STATUS_CHANGED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.PARTNER),
            ('USER_CHECKIN_INITIATED', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
            ('USER_CHECKOUT', NotificationChannel.WHATSAPP.name, Notification.UserTypeOptions.USER),
        ]
        
        for category, channel, user_type in notification_configs:
            Notification.objects.create(
                category=category,
                channel=channel,
                description=f"Test {category} notification",
                user_type=user_type
            )
    
    @patch('notification.tasks.send_notification.delay')
    def test_pre_checkin_status_change_signal(self, mock_send_notification):
        """Test pre-checkin status change signal"""
        
        # Create pre-checkin
        pre_checkin = PreCheckin.objects.create(
            property=self.property,
            expected_checkin=timezone.now() + timedelta(days=1),
            status='pending'
        )
        
        # Reset mock after creation
        mock_send_notification.reset_mock()
        
        # Create a mock original instance
        from unittest.mock import MagicMock
        original_precheckin = MagicMock()
        original_precheckin.status = 'pending'
        
        # Mock the objects.get call to return our mock
        with patch.object(PreCheckin.objects, 'get', return_value=original_precheckin):
            # Change status
            pre_checkin.status = 'confirmed'
            pre_checkin.save()
            
            # Manually trigger the signal
            from notification.signals import precheckin_notification_handler
            precheckin_notification_handler(
                sender=PreCheckin,
                instance=pre_checkin,
                created=False
            )
        
        # Verify notification was sent
        mock_send_notification.assert_called()
    
    @patch('notification.tasks.send_notification.delay')
    def test_guest_checkout_signal(self, mock_send_notification):
        """Test guest checkout signal"""
        
        # Create guest
        guest = Guest.objects.create(
            user=self.guest_user,
            room=self.room,
            checkin_key="test_key",
            checked_in=True,
            checked_out=False
        )
        
        # Reset mock after creation
        mock_send_notification.reset_mock()
        
        # Create a mock original instance
        from unittest.mock import MagicMock
        original_guest = MagicMock()
        original_guest.checked_out = False
        
        # Mock the objects.get call to return our mock
        with patch.object(Guest.objects, 'get', return_value=original_guest):
            # Simulate checkout
            from notification.signals import guest_checkout_notification_handler
            guest.checked_out = True
            guest.save()
            
            # Manually trigger signal
            guest_checkout_notification_handler(
                sender=Guest,
                instance=guest,
                created=False
            )
        
        # Verify notification was sent
        mock_send_notification.assert_called()


@override_settings(
    CELERY_TASK_ALWAYS_EAGER=True,
    CELERY_TASK_EAGER_PROPAGATES=True,
    CELERY_BROKER_URL='memory://',
    CELERY_RESULT_BACKEND='cache+memory://',
)
class NotificationTaskTestCase(TestCase):
    """Test notification tasks"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            phone='9876543210',
            name='Test User',
            password='testpass123'
        )
        
        # Create WhatsApp contact
        WhatsAppContact.objects.create(
            user=self.user,
            phone_number='+919876543210',
            is_active=True,
            is_verified=True
        )
        
        # Create notification
        Notification.objects.create(
            category='USER_ORDER_PLACED',
            channel=NotificationChannel.WHATSAPP.name,
            description="Test notification",
            user_type=Notification.UserTypeOptions.USER
        )
    
    def test_validate_data_for_notification(self):
        """Test notification data validation"""
        from notification.tasks import validate_data_for_notification
        from notification.models import NotificationCategory
        
        # Test valid data
        category = NotificationCategory.USER_ORDER_PLACED
        data = {'username': 'Test User', 'order_id': 'ORDER123'}
        
        validated, result = validate_data_for_notification(category, data)
        
        self.assertTrue(validated)
        self.assertIsInstance(result, tuple)
        self.assertEqual(len(result), 2)  # title and body
    
    def test_validate_data_for_notification_missing_field(self):
        """Test notification data validation with missing field"""
        from notification.tasks import validate_data_for_notification
        from notification.models import NotificationCategory
        
        # Test missing data
        category = NotificationCategory.USER_ORDER_PLACED
        data = {'username': 'Test User'}  # Missing order_id
        
        validated, error = validate_data_for_notification(category, data)
        
        self.assertFalse(validated)
        self.assertIn('Missing order_id', error)
    
    def test_load_handler_from_channel_name(self):
        """Test loading notification handler"""
        from notification.tasks import load_handler_from_channel_name
        
        handler = load_handler_from_channel_name('WHATSAPP')
        
        self.assertIsNotNone(handler)
        self.assertEqual(handler.__name__, 'WhatsAppChannel')
    
    @patch('notification.channel.whatsapp.requests.post')
    def test_send_notification_task(self, mock_post):
        """Test the send_notification task"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'messages': [{'id': 'test_message_id'}]}
        mock_post.return_value = mock_response
        
        with patch('django.conf.settings.WHATSAPP_ACCESS_TOKEN', 'test_token'), \
             patch('django.conf.settings.WHATSAPP_PHONE_NUMBER_ID', '123456789'):
            
            result = send_notification(
                str(self.user.id),
                'USER_ORDER_PLACED',
                {'username': 'Test User', 'order_id': 'ORDER123'}
            )
            
            self.assertTrue(result[0])
            self.assertEqual(result[1], 'Notification in Queue')


if __name__ == '__main__':
    unittest.main()
