Scenario Outline: Managing rental cart
  Given I am an authenticated user
  When I add a rental service item to the cart
  Then the response status code is 201
  And the response data contains the added cart item details with pickup & drop off dates, no of periods

  When I remove a rental service item from the cart
  Then the response status code is 200
  And the removed item is no longer present in the cart

  When I try to add an invalid item or quantity to the cart
  Then the response status code is 400
  And the response data contains error messages

  Examples:
    | Action        | Quantity | Description                                      |
    |----------------|---------|----------------------------------------------------|
    | Add item       | 1        | Add a valid rental service item to the cart        |
    | Remove item    | 1        | Remove an existing item from the cart              |
    | Add invalid    | 9999     | Try adding a non-existent service item          |
    | Add invalid    
