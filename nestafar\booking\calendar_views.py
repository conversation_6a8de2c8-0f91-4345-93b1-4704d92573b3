from rest_framework.decorators import api_view, permission_classes
from nestafar.responses import (
    SuccessResponse, CreateResponse, BadRequestResponse,
    NotFoundResponse
)
from rest_framework import status
from datetime import datetime
from decimal import Decimal, InvalidOperation
from core.permissions import PropertyPermission
from pms.models import Calendar, RoomType

@api_view(['GET', 'PATCH'])
@permission_classes([PropertyPermission])
def calendar_endpoint(request):
    if request.method == 'GET':
        start_date = request.query_params.get('start')
        end_date   = request.query_params.get('end')
        if not start_date or not end_date:
            return BadRequestResponse(
                message='start and end required YYYY-MM-DD'
            )
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date   = datetime.strptime(end_date,   '%Y-%m-%d').date()
        except ValueError:
            return BadRequestResponse(
                message='Invalid date format'
            )
        if end_date < start_date:
            return BadRequestResponse(
                message = 'end must be on or after start'
            )
        # Optional: protect against excessively large ranges
        inclusive_days = (end_date - start_date).days + 1
        if inclusive_days > 365:
            return BadRequestResponse(
                message='Requested range too large (max 365 days inclusive)'
            )
        qs = (Calendar.objects
              .filter(room_type__hotel=request.property, date__range=(start_date, end_date))
              .values('room_type_id', 'date', 'available_rooms', 'daily_rate')
              .order_by('date', 'room_type_id'))
        results = [
            {
                'room_type_id': str(row['room_type_id']),
                'date': row['date'].isoformat(),
                'available_rooms': row['available_rooms'],
                'daily_rate': (str(row['daily_rate']) if row['daily_rate'] is not None else None)
            }
            for row in qs
        ]
        return SuccessResponse(data={'results': results})
    elif request.method == 'PATCH':
        # Update (or create) daily rate for a specific day
        room_type_id = request.data.get('room_type_id')
        date_str = request.data.get('date')
        raw_rate = request.data.get('daily_rate')

        if room_type_id in (None, '') or date_str in (None, '') or raw_rate in (None, ''):
            return BadRequestResponse(message='room_type_id, date, daily_rate required')

        # Parse date
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return BadRequestResponse(message='Invalid date format')

        # Validate / normalize daily_rate
        try:
            rate_decimal = Decimal(str(raw_rate))
        except (InvalidOperation, TypeError):
            return BadRequestResponse(message='daily_rate must be a valid number')
        if rate_decimal < 0:
            return BadRequestResponse(message='daily_rate must be >= 0')
        # Normalize to 2 decimal places
        try:
            rate_decimal = rate_decimal.quantize(Decimal('0.01'))
        except (InvalidOperation):  # pragma: no cover - defensive
            return BadRequestResponse(message='Failed to normalize daily_rate')

        # Fetch room type
        # Validate room_type_id type and existence within the current property
        try:
            rt_id = int(room_type_id)
        except (TypeError, ValueError):
            return BadRequestResponse(message='room_type_id must be an integer')

        try:
            rt = RoomType.objects.get(id=rt_id, hotel=request.property)
        except RoomType.DoesNotExist:
            return NotFoundResponse(message='RoomType not found')

        cal_obj, created = Calendar.objects.update_or_create(
            room_type=rt,
            date=date,
            defaults={'daily_rate': rate_decimal}
        )

        payload = {
            'success': True,
            'created': created,
            'calendar': {
                'room_type_id': str(cal_obj.room_type_id),
                'date': cal_obj.date.isoformat(),
                'available_rooms': cal_obj.available_rooms,
                'daily_rate': str(cal_obj.daily_rate),  # return as string to preserve precision
            }
        }
        return CreateResponse(payload) if created else SuccessResponse(payload)
    else:  
        return BadRequestResponse(message='Method not allowed')