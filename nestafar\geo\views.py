from rest_framework.viewsets import ReadOnlyModelViewSet, ModelViewSet
from django_filters import rest_framework as filters
from rest_framework.filters import OrderingFilter, SearchFilter
from rest_framework.permissions import IsAuthenticated
from .serializers import *
from .utils import calculate_distance
from rest_framework.decorators import action
from .services import GoogleMapsService
from rest_framework.response import Response
from django.core.cache import cache
from django.db.models import Q

class CityFilter(filters.FilterSet):
    class Meta:
        model = City
        fields = ['state']


class CityModelViewSet(ReadOnlyModelViewSet):
    queryset = City.objects.all()
    serializer_class = CitySerializer
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = CityFilter


class StateModelViewSet(ReadOnlyModelViewSet):
    queryset = State.objects.all()
    serializer_class = StateSerializer
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_fields = ['country']
    search_fields = ['name']
    ordering_fields = ['name']


class AdministrationAreaFilter(filters.FilterSet):
    class Meta:
        model = AdministrationArea
        fields = '__all__'


class AdministrationAreaViewSet(ReadOnlyModelViewSet):
    queryset = AdministrationArea.objects.all()
    serializer_class = AdministrationAreaSerializer
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = AdministrationAreaFilter
    search_fields = ['pincode']
    ordering_fields = ['pincode']

class LocationViewSet(ModelViewSet):
    queryset = Location.objects.all()
    permission_classes = [IsAuthenticated]
    filterset_fields = ['location_type', 'is_verified']
    search_fields = ['name', 'address']
    ordering_fields = ['created_at', 'name']

    def get_serializer_class(self):
        if self.action == 'create':
            return LocationCreateOrManualSerializer
        return LocationSerializer
    
    @action(detail=False, methods=['get'])
    def nearby(self, request):
        """Find nearby locations within a specified radius"""
        lat = float(request.query_params.get('latitude', 0))
        lon = float(request.query_params.get('longitude', 0))
        radius = float(request.query_params.get('radius', 5))  # km
        
        locations = []
        for location in Location.objects.all():
            if location.latitude and location.longitude:
                distance = calculate_distance(
                    lat, lon,
                    float(location.latitude),
                    float(location.longitude)
                )
                if distance <= radius:
                    locations.append({
                        'location': LocationSerializer(location).data,
                        'distance': round(distance, 2)
                    })
        
        return Response(locations, status=200)

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Search for locations."""
        query = request.query_params.get('query', '').strip().lower()
        if not query:
            return Response({"error": "Query parameter is required"}, status=400)

        # Step 1: Check cache
        cache_key = f"location_search_{query}"
        cached_result = cache.get(cache_key)
        if cached_result:
            return Response(cached_result, status=200)

        # Step 2: Check database
        locations = Location.objects.filter(Q(name__icontains=query) | Q(address__icontains=query))
        if locations.exists():
            serialized_data = LocationSerializer(locations, many=True).data
            cache.set(cache_key, serialized_data, timeout=60 * 60 * 24)  # Cache for 24 hours
            return Response(serialized_data, status=200)

        # Step 3: Fetch from Google Maps
        maps_service = GoogleMapsService()
        results = maps_service.search_place(query)
        if not results:
            return Response([], status=200)

        # Step 4: Cache and return Google Maps response
        google_results = results.get('results', [])
        cache.set(cache_key, google_results, timeout=60 * 60 * 24)  # Cache for 24 hours
        return Response(google_results, status=200)
    
class LocationMetadataViewSet(ModelViewSet):
    queryset = LocationMetadata.objects.all()
    serializer_class = LocationMetadataSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['location']
