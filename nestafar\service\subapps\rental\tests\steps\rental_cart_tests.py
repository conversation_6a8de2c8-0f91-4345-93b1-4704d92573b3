from pytest_bdd import scenario, given, then, when
from rental.models import RentalCart, RentalCartItems, RentalServiceItem
from rental.tests import rentaltests
import datetime

@scenario("features/rental_cart.feature")
def test_manage_rental_cart(client, user):
    pass

@given("I am an authenticated user")
def authenticate(client, user):
    rentaltests.authenticate_user(client, user)

@given("a rental service item exists")
def create_rental_service_item(service_factory=rentaltests.create_rental_service_item):
    item = service_factory()
    return item

@when("I add a rental service item to the cart")
def add_rental_item_to_cart(client, user, item):
    # Get user's cart or create a new one
    cart, _ = RentalCart.objects.get_or_create(guest=user.guest, defaults={"status": "OPEN"})
    data = {"item": item.id, "quantity": 1, "pickup_date": "2024-05-12", "drop_date": "2024-05-15"}
    response = client.post(f"/api/rental-carts/{cart.id}/items/", data=data, format="json")
    response.json()  # trigger data parsing

@then("Then the response status code is 201")
def check_add_item_status_code(response):
    assert response.status_code == 201

@then("And the response data contains the added cart item details with pickup & drop off dates, no of periods")
def check_add_item_response(response, rental_service_item):
    # Ensure that response is captured and valid
    assert response.status_code == 201  # Ensure response is valid
    data = response.json()

    # Ensure the expected fields are in the response
    assert "item" in data
    assert "pickup_date" in data
    assert "drop_date" in data
    assert "no_of_periods" in data

    # Fetching pickup and drop-off dates from the response
    pickup_date_str = data["pickup_date"]
    drop_date_str = data["drop_date"]

    # Converting date strings to datetime objects
    pickup_date = datetime.datetime.strptime(pickup_date_str, "%Y-%m-%d")
    drop_date = datetime.datetime.strptime(drop_date_str, "%Y-%m-%d")

    # Calculating the number of periods
    delta = drop_date - pickup_date
    expected_no_of_periods = delta.days  # For daily rentals, adjust if using other periods

    # Validating the calculated number of periods against the expected value
    assert data["no_of_periods"] == expected_no_of_periods

@when("I remove a rental service item from the cart")
def remove_rental_item_from_cart(client, user, item):
    # Get user's cart
    cart = RentalCart.objects.get(guest=user.guest)
    cart_item = RentalCartItems.objects.create(cart=cart, item=item, quantity=1)
    item_id = cart_item.id
    response = client.delete(f"/api/rental-carts/{cart.id}/items/{item_id}/")

@then("And the removed item is no longer present in the cart")
def check_remove_item_from_cart(client, user):
    cart = RentalCart.objects.get(guest=user.guest)
    removed_items = RentalCartItems.objects.filter(cart=cart)
    assert len(removed_items) == 0

@when("I try to add an invalid item or quantity to the cart")
def add_invalid_item_to_cart(client, user):
    # Get user's cart or create a new one
    cart, _ = RentalCart.objects.get_or_create(guest=user.guest, defaults={"status": "OPEN"})
    # Invalid item ID
    data = {"item": 9999, "quantity": 1, "pickup_date": "2024-05-12", "drop_date": "2024-05-15"}
    response = client.post(f"/api/rental-carts/{cart.id}/items/", data=data, format="json")
    response.json()  # trigger data parsing

@then("Then the response status code is 400")
def check_invalid_item_status_code(response):
    assert response.status_code == 400

@then("And the response data contains error messages")
def check_invalid_item_error(response):
    data = response.json()
    assert "errors" in data
    assert "item" in data["errors"]
    assert "does not exist" in data["errors"]["item"][0]
        # Assert specific error messages based on API implementation
