from django.core.management import BaseCommand
from geo.models import Location
from core.models import User, PartnerProfile, UserProfile
from stay.models import Property, PropertyMetaData, PropertyReview, PropertyPartner, Room, Guest
from service.models import ServicePartner
from service.subapps.food.models import FoodService, FoodServiceItem
from service.subapps.rental.models import RentalService, RentalServiceItem
from service.subapps.transport.models import TransportService, TransportServiceItem
from service.subapps.laundry.models import LaundryService, LaundryServiceItem
import logging



class Command(BaseCommand):
    def handle(self, *args, **kwargs):
        logger = logging.getLogger(__name__)
        partnerUsers, guestUsers = [], []
        try:
            partnerUser1 = User.objects.create_user(phone="9876543210", name="PartnerUser1", partner=True)
            partnerUser2 = User.objects.create_user(phone="9876543211", name="PartnerUser2", partner=True)
        except:
            logger.info("Partner user already exists")
            partnerUser1 = User.objects.get(phone="9876543210")
            partnerUser2 = User.objects.get(phone="9876543211")
        partnerUser1.set_password("password")
        partnerUser2.set_password("password")
        partnerUsers.append(partnerUser1)
        partnerUsers.append(partnerUser2)
        for partnerUser in partnerUsers:
            try:
                partnerUser = partnerUser.save()
            except:
                logger.info("Partner user already exists")
        try:
            guest_user1 = User.objects.create_user(phone="9876543212", name="GuestUser1", partner=False)
            guest_user2 = User.objects.create_user(phone="9876543213", name="GuestUser2", partner=False)
        except:
            logger.info("Guest user already exists")
            guest_user1 = User.objects.get(phone="9876543212")
            guest_user2 = User.objects.get(phone="9876543213")
        guest_user1.set_password("password")
        guest_user2.set_password("password")

        guestUsers.append(guest_user1)
        guestUsers.append(guest_user2)
        for guestUser in guestUsers:
            try:
                guestUser = guestUser.save()
            except:
                logger.info("Guest user already exists")
        PropertyPartner.objects.all().delete()        

        Guest.objects.all().delete()
        Room.objects.all().delete()
        Property.objects.all().delete()
        ServicePartner.objects.all().delete()
        FoodServiceItem.objects.all().delete()
        FoodService.objects.all().delete()
        RentalServiceItem.objects.all().delete()
        RentalService.objects.all().delete()
        TransportServiceItem.objects.all().delete()
        TransportService.objects.all().delete()
        LaundryServiceItem.objects.all().delete()
        LaundryService.objects.all().delete()
        Location.objects.all().delete()


            
        locations = []    
        location1 = Location.objects.create(name="Location1", description="Location1 description", address="Location1 address",
                                            type="HS", area=100, latitude=12.34, longitude=56.78, timezone="IST")
        location2 = Location.objects.create(name="Location2", description="Location2 description", address="Location2 address",
                                            type="HT", area=200, latitude=12.34, longitude=56.78, timezone="IST")
        
        locations.append(location1)
        locations.append(location2)
        for location in locations:
            location.save()

        properties = []
        property1 = Property.objects.create(
            location=location1,
            name="Property1",
            avg_price=1000,
            po_address="Property1 address",
            photo="Property1 photo",
            type=Property.Type.BUDGET,
            rooms=10,
            rating=5,
            description="Property1 description",
            meal_cost=200,
            directions="Property1 directions",
        )
        property1.staffs.add(partnerUser1.partner_profile)

        property2 = Property.objects.create(
            location=location2,
            name="Property2",
            avg_price=2000,
            po_address="Property2 address",
            photo="Property2 photo",
            type=Property.Type.FAMILY,
            rooms=20,
            rating=4,
            description="Property2 description",
            meal_cost=400,
            directions="Property2 directions",
        )
        property2.staffs.add(partnerUser2.partner_profile)

        properties.append(property1)
        properties.append(property2)
        for property in properties:
            property.save()

        for p in properties:
            for i in range(1,5):
                r = Room(
                    room_no='10'+str(i),
                    property=p,
                    type_of_room='Single',
                    description='Single Room',
                    rate=1000,
                    bed='King',
                    max_guests=2,
                )
                r.save()

        servicePartners = []
        foodservice_partner1 = ServicePartner.objects.create(
            location=location1,
            name="FoodPartner1",
            type_of_service=ServicePartner.PartnerTypes.FOOD,
            description="Food partner description",
            
        )
        foodservice_partner2 = ServicePartner.objects.create(
            location=location2,
            name="FoodPartner2",
            type_of_service=ServicePartner.PartnerTypes.FOOD,
            description="Food partner description",
        )
        servicePartner2 = ServicePartner.objects.create(
            location=location2,
            name="TransportPartner2",
            type_of_service=ServicePartner.PartnerTypes.TRANSPORT,
            description="Transport Partner description",
        )
        servicePartner3 = ServicePartner.objects.create(
            location=location2,
            name="LaundryPartner",
            type_of_service=ServicePartner.PartnerTypes.LAUNDRY,
            description="Laundry Partner description",
        )
        servicePartners.append(servicePartner2)
        servicePartners.append(servicePartner3)

        property_partner1 = PropertyPartner.objects.create(
            property=property1,
            partner=foodservice_partner1,
            commission=10,
            name="FoodPartner1",
        )
        property_partner2 = PropertyPartner.objects.create(
            property=property2,
            partner=foodservice_partner2,
            commission=20,
            name="FoodPartner2",
        )


        propertyPartners = []
        propertyPartner1 = PropertyPartner.objects.create(
            property=property1,
            partner=servicePartner2,
            commission=10,
            name="PropertyPartner1",
        )
        propertyPartner2 = PropertyPartner.objects.create(
            property=property2,
            partner=servicePartner2,
            commission=20,
            name="PropertyPartner2",
        )
        propertyPartners.append(propertyPartner1)
        propertyPartners.append(propertyPartner2)
        for propertyPartner in propertyPartners:
            propertyPartner.save()
        
        foodservices = []
        fs1 = FoodService.objects.create(
            partner=foodservice_partner1, charges=100, tax_rate=10, name="Ala Carte",
            veg_only=True
        )
        fs2 = FoodService.objects.create(
            partner=foodservice_partner2, charges=200, tax_rate=20, name="Buffet",
            veg_only=True
        )
        foodservices.append(fs1)
        foodservices.append(fs2)
        for foodservice in foodservices:
            foodservice.save()

        foodserviceItems = []
        fsi11 = FoodServiceItem.objects.create(service=fs1, name='Item1', description='Item1 description', price=100, rating=4)
        fsi12 = FoodServiceItem.objects.create(service=fs1, name='Item2', description='Item2 description', price=100, rating=4)
        fsi13 = FoodServiceItem.objects.create(service=fs1, name='Item3', description='Item3 description', price=100, rating=4)
        
        fsi24 = FoodServiceItem.objects.create(service=fs2, name='Item4', description='Item4 description', price=0, rating=5)
        fsi25 = FoodServiceItem.objects.create(service=fs2, name='Item5', description='Item5 description', price=0, rating=5)
        fsi26 = FoodServiceItem.objects.create(service=fs2, name='Item6', description='Item6 description', price=0, rating=5)
        
        foodserviceItems = [fsi11, fsi12, fsi13, fsi24, fsi25, fsi26]
        for foodserviceItem in foodserviceItems:
            foodserviceItem.save()

        rentalservices = []
        rs1 = RentalService.objects.create(
            partner=servicePartner2,
            charges=100,
            tax_rate=10,
            name="Bike rental",
            period=1,
            min_period=1,
            max_period=10,
        )
        rs2 = RentalService.objects.create(
            partner=servicePartner2,
            charges=200,
            tax_rate=20,
            name="Car rental",
            period=1,
            min_period=1,
            max_period=10,
        )
        rentalservices.append(rs1)
        rentalservices.append(rs2)
        for rentalservice in rentalservices:
            rentalservice.save()

        rentalserviceItems = []

        rsi11 = RentalServiceItem.objects.create(service=rs1, name='Royal Enfield', description='dhuk dhuk', price=100, rating=4)
        rsi12 = RentalServiceItem.objects.create(service=rs1, name='Activa', description='fuk fuk', price=100, rating=4)
        rsi13 = RentalServiceItem.objects.create(service=rs1, name='Bicycle', description='chuk chuk', price=100, rating=4)
        
        rsi24 = RentalServiceItem.objects.create(service=rs2, name='Swift', description='zoom zoom', price=0, rating=5)
        rsi25 = RentalServiceItem.objects.create(service=rs2, name='Wagon R', description='zoom zoom', price=0, rating=5)
        rsi26 = RentalServiceItem.objects.create(service=rs2, name='Alto', description='zoom zoom', price=0, rating=5)
        
        rentalserviceItems = [rsi11, rsi12, rsi13, rsi24, rsi25, rsi26]
        for rentalserviceItem in rentalserviceItems:
            rentalserviceItem.save()

        servicePartner2 = ServicePartner.objects.get(
            type_of_service=ServicePartner.PartnerTypes.TRANSPORT
        )
        servicePartner2 = ServicePartner.objects.get(
            type_of_service=ServicePartner.PartnerTypes.TRANSPORT
        )
        location1 = Location.objects.get(name="Location1")
        location2 = Location.objects.get(name="Location2")
        transportservices = []
        ts1 = TransportService.objects.create(
            partner=servicePartner2,
            charges=100,
            tax_rate=10,
            name="Airport Pickup/Drop",
            surcharge=10,
            night_service=True,
            outstation=False,
        )
        ts2 = TransportService.objects.create(
            partner=servicePartner2,
            charges=200,
            tax_rate=20,
            name="Interstate",
            surcharge=20,
            night_service=False,
            outstation=True,
        )
        transportservices.append(ts1)
        transportservices.append(ts2)
        for transportservice in transportservices:
            transportservice.save()

        tsi1 = TransportServiceItem.objects.create(service=ts1, name='Swift Dzire', description='Airport Pickup', price=100, rating=4,
                                                coverage_distance=100)
        tsi1.service_areas.add(location1)
        tsi1.save()
        tsi2 = TransportServiceItem.objects.create(service=ts1, name='Innova', description='Airport Pickup', price=100, rating=4,
                                                    coverage_distance=100)
        tsi2.service_areas.add(location1)
        tsi2.save()
        tsi3 = TransportServiceItem.objects.create(service=ts1, name='Alto', description='Airport Pickup', price=100, rating=4,
                                                    coverage_distance=100)
        
        tsi4 = TransportServiceItem.objects.create(service=ts2, name='Swift Dzire', description='Interstate', price=0, rating=5,
                                                    coverage_distance=200)
        tsi5 = TransportServiceItem.objects.create(service=ts2, name='Innova', description='Interstate', price=0, rating=5,
                                                    coverage_distance=200)
        tsi6 = TransportServiceItem.objects.create(service=ts2, name='Alto', description='Interstate', price=0, rating=5,
                                                    coverage_distance=200)
        tsi4.service_areas.add(location2)
        tsi4.save()
        tsi5.service_areas.add(location2)
        tsi5.save()
        tsi6.service_areas.add(location2)
        tsi6.save()
        
        transportServiceItems = [tsi1, tsi2, tsi3, tsi4, tsi5, tsi6]
        for transportServiceItem in transportServiceItems:
            transportServiceItem.save()

        laundryservices = []
        ls1 = LaundryService.objects.create(
            partner=servicePartner2, charges=100, tax_rate=10, name="In house Laundry"
        )
        ls2 = LaundryService.objects.create(
            partner=servicePartner2, charges=100, tax_rate=10, name="Ironing"
        )
        laundryservices.append(ls1)
        laundryservices.append(ls2)
        for laundryservice in laundryservices:
            laundryservice.save()
            
        lsi1 = LaundryServiceItem.objects.create(service=ls1, name='Shirt', description='Shirt', price=100, rating=4)
        lsi2 = LaundryServiceItem.objects.create(service=ls1, name='Pant', description='Pant', price=200, rating=4)
        lsi3 = LaundryServiceItem.objects.create(service=ls1, name='Towel', description='Towel', price=300, rating=4)
        
        lsi4 = LaundryServiceItem.objects.create(service=ls2, name='Shirt', description='Shirt', price=10, rating=5)
        lsi5 = LaundryServiceItem.objects.create(service=ls2, name='Pant', description='Pant', price=20, rating=5)
        lsi6 = LaundryServiceItem.objects.create(service=ls2, name='Towel', description='Towel', price=30, rating=5)
        
        laundryserviceItems = [lsi1, lsi2, lsi3, lsi4, lsi5, lsi6]
        for laundryserviceItem in laundryserviceItems:
            laundryserviceItem.save()
