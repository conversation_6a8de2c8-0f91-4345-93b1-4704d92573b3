# Generated by Django 4.2.7 on 2025-08-01 16:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('service', '0002_servicepartner_is_visible'),
        ('stay', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('core', '0002_initial'),
        ('notification', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GuestReview',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('review_text', models.TextField(blank=True)),
                ('service_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('cleanliness_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('location_rating', models.IntegerField(blank=True, choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)], null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='OnboardingStatus',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('property_details_added', models.BooleanField(default=False)),
                ('property_photos_uploaded', models.BooleanField(default=False)),
                ('rooms_added', models.BooleanField(default=False)),
                ('room_photos_uploaded', models.BooleanField(default=False)),
                ('services_added', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('incomplete', 'Incomplete')], default='pending', max_length=20)),
                ('total_gmv', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_commission', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('last_reminder_sent', models.DateTimeField(blank=True, null=True)),
                ('reminder_count', models.IntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Onboarding Status',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='WeeklyReport',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('week_start', models.DateField()),
                ('week_end', models.DateField()),
                ('total_reservations', models.IntegerField(default=0)),
                ('occupancy_rate', models.FloatField(default=0.0)),
                ('average_orders_per_guest', models.FloatField(default=0.0)),
                ('total_gmv', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_commission', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('recommendations', models.JSONField(default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='WhatsAppContact',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(help_text='WhatsApp phone number', max_length=128, region=None)),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='delivery_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='message_id',
            field=models.CharField(blank=True, help_text='External message ID from service provider', max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='message_status',
            field=models.CharField(choices=[('sent', 'Sent'), ('delivered', 'Delivered'), ('read', 'Read'), ('failed', 'Failed'), ('pending', 'Pending')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='read_timestamp',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='service_partner',
            field=models.ForeignKey(blank=True, help_text='For notifications sent to service partners', null=True, on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner'),
        ),
        migrations.AddField(
            model_name='notificationlog',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='notification',
            name='category',
            field=models.CharField(choices=[('USER_CHECKIN_INITIATED', 'USER_CHECKIN_INITIATED'), ('USER_CHECKIN', 'USER_CHECKIN'), ('USER_CHECKOUT', 'USER_CHECKOUT'), ('USER_ORDER_ACCEPTED', 'USER_ORDER_ACCEPTED'), ('USER_ORDER_CANCELLED', 'USER_ORDER_CANCELLED'), ('USER_ORDER_COMPLETED', 'USER_ORDER_COMPLETED'), ('USER_ORDER_PLACED', 'USER_ORDER_PLACED'), ('USER_ORDER_ONGOING', 'USER_ORDER_ONGOING'), ('USER_ORDER_REJECTED', 'USER_ORDER_REJECTED'), ('PARTNER_ORDER_PLACED', 'PARTNER_ORDER_PLACED'), ('PARTNER_ORDER_ACCEPTED', 'PARTNER_ORDER_ACCEPTED'), ('PARTNER_ORDER_CANCELLED', 'PARTNER_ORDER_CANCELLED'), ('PARTNER_ORDER_COMPLETED', 'PARTNER_ORDER_COMPLETED'), ('PARTNER_ORDER_ONGOING', 'PARTNER_ORDER_ONGOING'), ('PARTNER_ORDER_REJECTED', 'PARTNER_ORDER_REJECTED'), ('PRECHECKIN_CREATED', 'PRECHECKIN_CREATED'), ('PRECHECKIN_CONFIRMED', 'PRECHECKIN_CONFIRMED'), ('PRECHECKIN_STATUS_CHANGED', 'PRECHECKIN_STATUS_CHANGED'), ('DAILY_SUMMARY_GUEST', 'DAILY_SUMMARY_GUEST'), ('DAILY_SUMMARY_PARTNER', 'DAILY_SUMMARY_PARTNER'), ('SIGNUP_SUCCESSFUL', 'SIGNUP_SUCCESSFUL'), ('ONBOARDING_REMINDER', 'ONBOARDING_REMINDER'), ('ONBOARDING_COMPLETED', 'ONBOARDING_COMPLETED'), ('PRECHECKIN_REMINDER', 'PRECHECKIN_REMINDER'), ('PRECHECKIN_CANCELLATION_WARNING', 'PRECHECKIN_CANCELLATION_WARNING'), ('ROOM_ALLOTMENT', 'ROOM_ALLOTMENT'), ('GUEST_ARRIVED_WELCOME', 'GUEST_ARRIVED_WELCOME'), ('CHECKIN_SUCCESSFUL', 'CHECKIN_SUCCESSFUL'), ('CHECKOUT_BILL', 'CHECKOUT_BILL'), ('REVIEW_REQUEST', 'REVIEW_REQUEST'), ('ORDER_CONFIRMED', 'ORDER_CONFIRMED'), ('ORDER_READY', 'ORDER_READY'), ('VENDOR_NEW_ORDER', 'VENDOR_NEW_ORDER'), ('VENDOR_ORDER_REMINDER', 'VENDOR_ORDER_REMINDER'), ('NEW_SERVICE_AVAILABLE', 'NEW_SERVICE_AVAILABLE'), ('DINNER_REMINDER', 'DINNER_REMINDER'), ('WEEKLY_REPORT', 'WEEKLY_REPORT'), ('SERVICE_HIDDEN_NOTIFICATION', 'SERVICE_HIDDEN_NOTIFICATION'), ('SERVICE_RESTORED_NOTIFICATION', 'SERVICE_RESTORED_NOTIFICATION'), ('WELCOME_MESSAGE', 'WELCOME_MESSAGE'), ('CHECKOUT_REMINDER', 'CHECKOUT_REMINDER'), ('CHECKOUT_SUCCESSFUL', 'CHECKOUT_SUCCESSFUL'), ('SERVICE_REQUEST_RECEIVED', 'SERVICE_REQUEST_RECEIVED'), ('SERVICE_IN_PROGRESS', 'SERVICE_IN_PROGRESS'), ('SERVICE_COMPLETED', 'SERVICE_COMPLETED'), ('FOOD_ORDER_PLACED', 'FOOD_ORDER_PLACED'), ('FOOD_ORDER_CONFIRMED', 'FOOD_ORDER_CONFIRMED'), ('FOOD_ORDER_READY', 'FOOD_ORDER_READY'), ('RATING_REQUEST', 'RATING_REQUEST'), ('DAILY_SUMMARY', 'DAILY_SUMMARY'), ('WEEKLY_SUMMARY', 'WEEKLY_SUMMARY'), ('MONTHLY_SUMMARY', 'MONTHLY_SUMMARY')], max_length=50),
        ),
        migrations.AlterField(
            model_name='notification',
            name='id',
            field=models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AddIndex(
            model_name='notificationlog',
            index=models.Index(fields=['message_id'], name='notificatio_message_4dba6d_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationlog',
            index=models.Index(fields=['message_status'], name='notificatio_message_2d2f82_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationlog',
            index=models.Index(fields=['created_at'], name='notificatio_created_f441eb_idx'),
        ),
        migrations.AddField(
            model_name='whatsappcontact',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='whatsapp_contacts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='weeklyreport',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='weekly_reports', to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='weeklyreport',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='weekly_reports', to='stay.property'),
        ),
        migrations.AddField(
            model_name='onboardingstatus',
            name='partner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_statuses', to='core.partnerprofile'),
        ),
        migrations.AddField(
            model_name='onboardingstatus',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='onboarding_statuses', to='stay.property'),
        ),
        migrations.AddField(
            model_name='guestreview',
            name='guest',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='stay.guest'),
        ),
        migrations.AddField(
            model_name='guestreview',
            name='property',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='guest_reviews', to='stay.property'),
        ),
        migrations.AlterUniqueTogether(
            name='whatsappcontact',
            unique_together={('user', 'phone_number')},
        ),
        migrations.AlterUniqueTogether(
            name='weeklyreport',
            unique_together={('partner', 'property', 'week_start')},
        ),
        migrations.AlterUniqueTogether(
            name='onboardingstatus',
            unique_together={('partner', 'property')},
        ),
    ]
