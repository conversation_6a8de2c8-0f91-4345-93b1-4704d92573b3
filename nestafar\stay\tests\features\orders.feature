Feature: Orders

    Scenario: Place Orders
        Given Setup User
            | name | phone      | password | partner |
            | partner1 | 0812345678 | 123456   | True    |
            | guest1   | 0812345679 | 123456   | False   |
        Given Set Auth User to partner1
        Given Add max_rooms to partner1
            | max_rooms |
            | 5         |
        Given Setup Locations
            | name      | description  | address     | latitude | longitude | type  | timezone     |
            | manali    | manali       | old manali  | 1.0      | 1.0       | HT | Asia/Kolkata |
            | kullu     | kullu        | kullu       | 1.0      | 1.0       | HT | Asia/Kolkata |
            | kasol     | kasol        | kasol       | 1.0      | 1.0       | HT | Asia/Kolkata |
            | bhuntar   | bhuntar      | bhuntar     | 1.0      | 1.0       | HT | Asia/Kolkata |
        Given Setup Properties
            | name      | description  | location | type  | avg_price | po_address | rooms  | rating  | meal_cost  |  directions  |
            | property1 | property1    | manali   | hotel | 1000      | manali     | 100    | 4.5     | 100        |  manali      |
            # | property2 | property2    | kullu    | hotel | 1000      | kullu      | 100    | 4.5     | 100        |  kullu       |
        Then Validate Properties
            | name      | description  | location | type  | avg_price | po_address | rooms  | rating  | meal_cost  |  directions  |
            | property1 | property1    | manali   | hotel | 1000      | manali     | 100    | 4.5     | 100        |  manali      |
            # | property2 | property2    | kullu    | hotel | 1000      | kullu      | 100    | 4.5     | 100        |  kullu       |
        Given Setup Rooms
            | property  | room_no  | description  | bed | max_guests  | rate   | type_of_room  |
            | property1 | 101      | room1        | 1   | 2           | 1000   | 1             |
            | property1 | 102      | room2        | 1   | 2           | 1000   | 1             |
            | property1 | 103      | room3        | 1   | 2           | 1000   | 1             |
            | property1 | 104      | room4        | 1   | 2           | 1000   | 1             |
            | property1 | 105      | room5        | 1   | 2           | 1000   | 1             |
        Given Setup Service Partners
            | name           | location     | type | description    | commission  | delivery_charges | pickup_charges |
            | himalyan dhaba | manali       |  1   | himalyan dhaba | 12          |  50              | 50             |
            # | m laundry      | manali       |  2   | m laundry      | 13          |
            # | rentelo        | manali       |  4   | rentelo        | 14          |
            # | s cabs         | manali       |  3   | s cabs         | 15          |
        Given Setup Services
            | name          | partner       | charges  | tax_rate  | service_type  |
            | ala carte     | himalyan dhaba | 10      | 5         | foodservice   |
            # | dry cleaning  | m laundry      | 10      | 0         | laundryservice|
            # | wash & fold   | m laundry      | 10      | 0         | laundryservice|
            # | bike rental   | rentelo        | 10      | 0         | rentalservice |
            # | car rental    | rentelo        | 10      | 0         | rentalservice |
            # | city tour     | s cabs         | 10      | 0         | transportservice |
            # | hampta pass   | s cabs         | 10      | 0         | transportservice |
            # | rohtang pass  | s cabs         | 10      | 0         | transportservice |
            # | kullu manali  | s cabs         | 10      | 0         | transportservice |
            # | manali kasol  | s cabs         | 10      | 0         | transportservice |
        Given Setup Service Items
            | name          | service       | description   | addon         |price   | service_type  | vegetarian |
            | dal makhani   | ala carte     | dal makhani   | {"butter":20} | 100    | foodservice   | True       |
            | rajma         | ala carte     | rajma         | {}            | 120    | foodservice   | True       |
            | aloo gobhi    | ala carte     | aloo gobhi    | {}            | 80     | foodservice   | True       |
            | roti          | ala carte     | roti          | {}            | 10     | foodservice   | True       |
            | naan          | ala carte     | naan          | {}            | 20     | foodservice   | True       |
            | maggi         | ala carte     | maggi         | {"masala":20} | 50     | foodservice   | True       |
        # Given Setup Service Items
        #     | name          | service       | description   |  addon        |price   | service_type  |
        #     | tshirt/pant   | dry cleaning  | laundry       | {}            | 100    | laundryservice|
        #     | tshirt/pant   | wash & fold   | laundry       | {}            | 50     | laundryservice|
        # Given Setup Service Items
        #     | name          | service       | description   |  addon        |price   | service_type  |
        #     | bullet        | bike rental   | bullet        | {}            | 1000   | rentalservice |
        #     | activa        | bike rental   | activa        | {}            | 500    | rentalservice |
        #     | swift         | car rental    | swift         | {}            | 1000   | rentalservice |
        #     | innova        | car rental    | innova        | {}            | 2000   | rentalservice |
        # Given Setup Transport Service Items
        #     | name          | service       | description   |  addon        |price   | service_type     | service_areas  | coverage_distance   |
        #     | city tour     | city tour     | city tour     | {}            | 1000   | transportservice | manali         | 30                  |
        #     | hampta pass   | hampta pass   | hampta pass   | {}            | 1000   | transportservice | manali         | 30                  |
        #     | rohtang pass  | rohtang pass  | rohtang pass  | {}            | 1000   | transportservice | manali         | 30                  |
        #     | kullu manali  | kullu manali  | kullu manali  | {}            | 2000   | transportservice | manali,kullu   | 100                 |
        #     | manali kasol  | manali kasol  | manali kasol  | {}            | 2000   | transportservice | manali,kasol   | 100                 |
        Then Validate Service Items
            | name          | service       | description   |  addon        |price   | service_type     |
            | dal makhani   | ala carte     | dal makhani   | {"butter":20} | 100    | foodservice   |
            | rajma         | ala carte     | rajma         |             | 120    | foodservice   |           
            | aloo gobhi    | ala carte     | aloo gobhi    |             | 80     | foodservice   |           
            | roti          | ala carte     | roti          |             | 10     | foodservice   |           
            | naan          | ala carte     | naan          |             | 20     | foodservice   |           
            | maggi         | ala carte     | maggi         | {"masala":20} | 50     | foodservice   | 
        #     | tshirt/pant   | dry cleaning  | laundry       | {}            | 100    | laundryservice| 
        #     | tshirt/pant   | wash & fold   | laundry       | {}            | 50     | laundryservice| 
        #     | bullet        | bike rental   | bullet        | {}            | 1000   | rentalservice | 
        #     | activa        | bike rental   | activa        | {}            | 500    | rentalservice | 
        #     | swift         | car rental    | swift         | {}            | 1000   | rentalservice | 
        #     | innova        | car rental    | innova        | {}            | 2000   | rentalservice | 
        #     | city tour     | city tour     | city tour     | {}            | 1000   | transportservice |
        #     | hampta pass   | hampta pass   | hampta pass   | {}            | 1000   | transportservice |
        #     | rohtang pass  | rohtang pass  | rohtang pass  | {}            | 1000   | transportservice |
        Given Validate Catalog for property property1
            | name          | service       | description   |  addon        |price   | service_type     |
            | dal makhani   | ala carte     | dal makhani   | {"butter":20} | 100    | foodservice   |
            | rajma         | ala carte     | rajma         | {}            | 120    | foodservice   |           
            | aloo gobhi    | ala carte     | aloo gobhi    | {}            | 80     | foodservice   |           
            | roti          | ala carte     | roti          | {}            | 10     | foodservice   |           
            | naan          | ala carte     | naan          | {}            | 20     | foodservice   |           
            | maggi         | ala carte     | maggi         | {"masala":20} | 50     | foodservice   | 
            # | tshirt/pant   | dry cleaning  | laundry       | {}            | 100    | laundryservice| 
            # | tshirt/pant   | wash & fold   | laundry       | {}            | 50     | laundryservice| 
            # | bullet        | bike rental   | bullet        | {}            | 1000   | rentalservice | 
            # | activa        | bike rental   | activa        | {}            | 500    | rentalservice | 
            # | swift         | car rental    | swift         | {}            | 1000   | rentalservice | 
            # | innova        | car rental    | innova        | {}            | 2000   | rentalservice | 
            # | city tour     | city tour     | city tour     | {}            | 1000   | transportservice |
            # | hampta pass   | hampta pass   | hampta pass   | {}            | 1000   | transportservice |
            # | rohtang pass  | rohtang pass  | rohtang pass  | {}            | 1000   | transportservice |
            # | kullu manali  | kullu manali  | kullu manali  | {}            | 2000   | transportservice |
            # | manali kasol  | manali kasol  | manali kasol  | {}            | 2000   | transportservice |
        Given Initiate Checkin
            | room_no   | guests                                             |
            | 101       | {"raju":"9874204200","shyam": "9874214210"}        |
        Given Set Auth User to raju
        Then Checkin
        Given Set Auth User to shyam
        Then Checkin
        Given Set Auth User to partner1
        Given Set Auth User to raju
        Given Add Items to Food Cart
            | item          | add_ons        | quantity  |
            | dal makhani   | {"butter":20}  | 1         |
            | rajma         | {}             | 1         |
            | roti          | {}             | 2         |
            | maggi         | {"masala":20}  | 1         |
            | naan          | {}             | 2         |
        Given Get Food Cart
            | item          | add_ons        | quantity  |
            | dal makhani   | {"butter":20}  | 1         |
            | roti          | {}             | 2         |
            | maggi         | {"masala":20}  | 1         |
            | naan          | {}             | 2         |
            | rajma         | {}             | 1         |
        Given Place FoodOrder from Cart
        Then Validate FoodOrder
            | item          | add_ons        | quantity  |
            | dal makhani   | {"butter":20}  | 1         |
            | roti          | {}             | 2         |
            | maggi         | {"masala":20}  | 1         |
            | naan          | {}             | 2         |
            | rajma         | {}             | 1         |
        # Then Validate FoodCartItems
        Then Get FoodOrder
        Given Set Auth User to partner1
        Then Get FoodPartnerOrderView
        Then Accept FoodOrder
        Then Validate FoodOrder Status
        Then Validate FoodCart Status

        