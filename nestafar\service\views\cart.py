from rest_framework.filters import <PERSON><PERSON>ilter, OrderingFilter
from django_filters import rest_framework as filters
from django.db import transaction
from rest_framework.views import APIView
from core.permissions import *
from service.service_factory import *
from service.models import BaseCart
from stay.models import PropertyPartner
from nestafar.responses import SuccessR<PERSON>ponse, BadRequestResponse
from rest_framework.permissions import IsAuthenticated


class CartView(APIView):
    permission_classes = [IsAuthenticated, PropertyPermission, ServicePermission]
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]

    def get(self, request, service_type=None):
        try:
            partner_type = url_mappings.get(service_type)
            if hasattr(self.request, "property"):
                guest = self.request.user.guest.filter(checked_in=True, checked_out=False).first()
                cart = service_cart_model.get(partner_type).objects.filter(guest=guest,
                                                                           status=BaseCart.CartStatus.PENDING)
                if not cart.exists():
                    cart = service_cart_model.get(partner_type).objects.create(guest=guest)
                else:
                    cart = cart.last()
                serializer = service_cart_serializer.get(partner_type)(cart)
                return SuccessResponse(data=serializer.data, message="Cart retrieved successfully")
            else:
                return BadRequestResponse(message="Property not found")
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def post(self, request, service_type=None):
        try:
            partner_type = url_mappings.get(service_type)
            if hasattr(self.request, "property"):
                guest = self.request.user.guest.filter(checked_in=True, checked_out=False).last()
                cart = service_cart_model.get(partner_type).objects.filter(guest=guest,
                                                                           status=BaseCart.CartStatus.PENDING)
                if not cart.exists():
                    return BadRequestResponse(message="No active carts found. Get cart and add items to it")
                else:
                    cart = cart.last()
                serializer = service_cart_item_serializer.get(partner_type)(data=request.data, many=True)
                if serializer.is_valid():
                    for item in serializer.validated_data:
                        with transaction.atomic():
                            service = item['item'].service
                            property_partner = PropertyPartner.objects.filter(property=request.property,
                                                                              partner=service.partner).last()
                            search_params = {k: v for k, v in item.items() if k not in ['quantity']}
                            existing_cart_item = service_cart_item_model.get(partner_type).objects.filter(
                                cart=cart, **search_params).last()

                            if existing_cart_item:
                                existing_cart_item = existing_cart_item.add_quantity(item['quantity'])
                                cart = existing_cart_item.cart
                            else:
                                charges = service.charges * item['quantity']
                                if not service_cart_item_model.get(partner_type).objects.filter(
                                    cart=cart,
                                    item__service=service).exists():
                                    # add delivery charges only once for multiple items from the same service
                                    charges += property_partner.pickup_charges + property_partner.delivery_charges

                                cart_item = service_cart_item_model.get(partner_type)(cart=cart, **item)
                                cart_item.add_item(property_partner.commission, charges)
                    return SuccessResponse(data=service_cart_serializer.get(partner_type)(cart).data, 
                                           message="Item Added successfully")
                else:
                    return BadRequestResponse(message="Invalid item data", errors=serializer.errors)
            else:
                return BadRequestResponse(message="Property not found")
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def delete(self, request, service_type=None):
        try:
            partner_type = url_mappings.get(service_type)
            with transaction.atomic():
                item = service_cart_item_model.get(partner_type).objects.get(pk=request.data.get('item'))
                cart = item.cart
                property_partner = PropertyPartner.objects.filter(property=request.property,
                                                                  partner=item.item.service.partner).last()
                quantity = request.data.get('quantity')
                item.remove_quantity(quantity)
                if item.quantity == 0:
                    charges = property_partner.delivery_charges + property_partner.pickup_charges
                    new_charges = cart.charges - charges
                    cart.apply_charges(new_charges)
                    item.delete()
                serialized_data = service_cart_serializer.get(partner_type)(cart).data
                return SuccessResponse(message="Item quantity updated", data=serialized_data)
        except Exception as e:
            return BadRequestResponse(message=str(e))
