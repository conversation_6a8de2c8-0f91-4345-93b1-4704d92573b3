from rest_framework import serializers
from booking.models import PreCheckinGuest, Reservation, Profile, ProfileImage, PreCheckin, AllotedRoom, Payment
from django.utils import timezone
from django.utils.dateparse import parse_datetime
from stay.serializers import RoomSerializer, PropertySerializer

class ReservationSerializer(serializers.ModelSerializer):
    property = PropertySerializer(read_only=True)
    
    class Meta:
        model = Reservation
        fields = [
            'id', 'property', 'check_in', 'check_out', 'guests', 'total',
            'paid', 'requests', 'status', 'created_at', 'updated_at',
            'booking_details', 'external_booking_id', 'cm_booking_id',
            'channel', 'segment', 'pah', 'booked_on',
            'amount_before_tax', 'tax_amount', 'currency', 'room_details'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'external_booking_id',
            'cm_booking_id', 'booked_on', 'amount_before_tax',
            'tax_amount', 'currency'
        ]

    def validate(self, data):
        """Ensure check-out date is after check-in date."""
        if data['check_out'] <= data['check_in']:
            raise serializers.ValidationError("Check-out must be after check-in.")
        if data['guests'] <= 0:
            raise serializers.ValidationError("There must be at least one guest.")
        return data

class ProfileImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProfileImage
        fields = ['id', 'image', 'created_at', 'updated_at']

class ProfileSerializer(serializers.ModelSerializer):
    images = ProfileImageSerializer(many=True, required=False)
    property = PropertySerializer(read_only=True)

    class Meta:
        model = Profile
        fields = [
            'id', 'property', 'phone', 'email', 'description',
            'location', 'amenities', 'nearby', 'images', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def create(self, validated_data):
        images_data = self.context['request'].FILES.getlist('images')
        profile = Profile.objects.create(**validated_data)
        
        for image_data in images_data:
            ProfileImage.objects.create(profile=profile, image=image_data)
            
        return profile

    def update(self, instance, validated_data):
        images_data = self.context['request'].FILES.getlist('images')
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # If images were provided, replace the existing images with new ones
        if images_data:
            instance.images.all().delete()  # Delete existing images
            for image_data in images_data:
                ProfileImage.objects.create(profile=instance, image=image_data)
        
        return instance

class UserSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    phone = serializers.CharField()  # Changed from PhoneNumberField to CharField

class PreCheckinGuestSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    room = RoomSerializer(read_only=True)
    
    class Meta:
        model = PreCheckinGuest
        fields = [
            'id', 'pre_checkin', 'user', 'room', 'id_proof', 
            'is_verified', 'is_primary', 'age', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'is_verified']

class AllotedRoomSerializer(serializers.ModelSerializer):
    room = RoomSerializer()
    
    class Meta:
        model = AllotedRoom
        fields = ['id', 'pre_checkin', 'room', 'created_at']
        read_only_fields = ['id', 'created_at']

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = [
            'id', 'pre_checkin', 'amount', 'payment_method', 
            'transaction_id', 'status', 'payment_details', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']

class PreCheckinSerializer(serializers.ModelSerializer):
    guests = PreCheckinGuestSerializer(many=True, read_only=True, source='pre_checkin_guests')
    alloted_rooms = AllotedRoomSerializer(many=True, read_only=True)
    payments = PaymentSerializer(many=True, read_only=True)
    property = PropertySerializer(read_only=True)
    reservation = ReservationSerializer(read_only=True)
    reservation_id = serializers.PrimaryKeyRelatedField(
        source='reservation', queryset=Reservation.objects.none(), write_only=True, required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically scope reservation choices to the current property
        prop_id = None
        # 1. From existing instance
        instance = getattr(self, 'instance', None)
        if instance is not None and getattr(instance, 'property_id', None):
            prop_id = instance.property_id
        # 2. From context (e.g., view passes property_id)
        if prop_id is None:
            prop_id = self.context.get('property_id') if isinstance(self.context, dict) else None
        # 3. From initial_data 'property' field (could be int or nested dict)
        if prop_id is None:
            initial_data = getattr(self, 'initial_data', {}) or {}
            raw_prop = initial_data.get('property') if isinstance(initial_data, dict) else None
            try:
                if isinstance(raw_prop, dict):
                    raw_prop = raw_prop.get('id')
                if raw_prop not in (None, ''):
                    prop_id = int(raw_prop)
            except (ValueError, TypeError):
                prop_id = None  # leave None if parsing fails
        # Apply filtered queryset if we resolved a property id
        if prop_id is not None:
            self.fields['reservation_id'].queryset = Reservation.objects.filter(property_id=prop_id)

    def validate_reservation_id(self, value):
        """Validate reservation compatibility with pre-checkin data."""
        if value and hasattr(self, 'initial_data'):
            # Validate property matching with robust property ID extraction
            property_data = self.initial_data.get('property')
            if property_data:
                # Extract property ID from various formats
                if isinstance(property_data, dict):
                    property_id = property_data.get('id')
                elif isinstance(property_data, (str, int)):
                    property_id = property_data
                else:
                    property_id = None
                
                # Convert to same type for comparison
                if property_id is not None:
                    try:
                        # Convert both to string for comparison to handle mixed types
                        property_id_str = str(property_id)
                        reservation_property_id_str = str(value.property_id)
                        
                        if property_id_str != reservation_property_id_str:
                            raise serializers.ValidationError(
                                "Reservation must belong to the same property as the pre-checkin."
                            )
                    except (ValueError, AttributeError):
                        raise serializers.ValidationError(
                            "Invalid property data format."
                        )

            # Validate date compatibility with proper datetime parsing
            expected_checkin = self.initial_data.get('expected_checkin')
            if expected_checkin and value.check_in:
                # Parse expected_checkin if it's a string
                if isinstance(expected_checkin, str):
                    parsed_checkin = parse_datetime(expected_checkin)
                    if parsed_checkin is None:
                        raise serializers.ValidationError(
                            "Invalid expected_checkin datetime format."
                        )
                    expected_checkin = parsed_checkin
                
                # Check if reservation dates are datetime or date objects
                if hasattr(value.check_in, 'date'):
                    # Reservation fields are datetime objects
                    reservation_checkin = value.check_in
                    reservation_checkout = value.check_out
                    # Ensure both are timezone-aware or naive for comparison
                    if timezone.is_aware(expected_checkin) != timezone.is_aware(reservation_checkin):
                        if timezone.is_naive(expected_checkin):
                            expected_checkin = timezone.make_aware(expected_checkin)
                        else:
                            expected_checkin = timezone.make_naive(expected_checkin)
                else:
                    # Reservation fields are date objects, compare as dates
                    expected_checkin_date = expected_checkin.date() if hasattr(expected_checkin, 'date') else expected_checkin
                    reservation_checkin = value.check_in
                    reservation_checkout = value.check_out
                    expected_checkin = expected_checkin_date
                
                # Ensure expected checkin is within reservation period
                if expected_checkin < reservation_checkin or expected_checkin >= reservation_checkout:
                    raise serializers.ValidationError(
                        f"Expected checkin date must be within the reservation period ({reservation_checkin} to {reservation_checkout})."
                    )

            # Validate reservation status using model STATUS_CHOICES (exclude cancelled/completed if present)
            try:
                status_choices = getattr(Reservation, 'STATUS_CHOICES', [])
                disallowed = {'cancelled', 'completed'}
                valid_statuses = [code for code, _label in status_choices if code not in disallowed]
                # Fallback if STATUS_CHOICES empty
                if not valid_statuses:
                    valid_statuses = ['confirmed', 'pending']
            except Exception:
                valid_statuses = ['confirmed', 'pending']
            if value.status not in valid_statuses:
                raise serializers.ValidationError(
                    "Cannot create pre-checkin for cancelled or completed reservations."
                )
        return value

    class Meta:
        model = PreCheckin
        fields = [
            'id', 'property', 'reservation', 'reservation_id', 'guest_address', 'number_of_rooms',
            'expected_checkin', 'stay_duration', 'welcome_message',
            'total_amount', 'amount_paid', 'pending_balance',
            'payment_status', 'payment_id', 'status', 'is_completed',
            'special_requests', 'created_at', 'guests',
            'alloted_rooms', 'payments'
        ]
        read_only_fields = [
            'id', 'created_at', 'status', 'property', 'reservation', 'is_completed',
            'guests', 'alloted_rooms', 'payments',
        ]

    def validate_expected_checkin(self, value):
        """Validate expected checkin date."""
        if value < timezone.now():
            raise serializers.ValidationError(
                "Expected checkin date cannot be in the past"
            )
        return value

    def validate_stay_duration(self, value):
        """Validate stay duration."""
        if value <= 0:
            raise serializers.ValidationError(
                "Stay duration must be greater than 0"
            )
        return value

    def validate_number_of_rooms(self, value):
        """Validate number of rooms."""
        if value <= 0:
            raise serializers.ValidationError(
                "Number of rooms must be greater than 0"
            )
        return value

    def validate(self, data):
        """Validate the entire data set."""
        # Calculate pending balance
        total_amount = data.get('total_amount', 0)
        amount_paid = data.get('amount_paid', 0)
        
        pending_balance = float(total_amount) - float(amount_paid)
        if pending_balance < 0:
            raise serializers.ValidationError({
                "amount_paid": "Amount paid cannot exceed total amount"
            })

        # Auto-set payment status based on amounts
        if amount_paid == 0:
            data['payment_status'] = 'unpaid'
        elif amount_paid == total_amount:
            data['payment_status'] = 'completed'
        else:
            data['payment_status'] = 'partial'

        data['pending_balance'] = pending_balance

        return data

    def to_representation(self, instance):
        """Customize the output representation."""
        representation = super().to_representation(instance)
        
        # Add primary guest details if exists
        primary_guest = instance.pre_checkin_guests.filter(is_primary=True).first()
        if primary_guest:
            representation['primary_guest'] = {
                'id': str(primary_guest.id),
                'name': primary_guest.user.name,
                'phone': str(primary_guest.user.phone),
                'age': primary_guest.age,
                'is_verified': primary_guest.is_verified
            }
        
        return representation


