import copy
import uuid
from django.db import models
from geo.models import Location
from phonenumber_field.modelfields import PhoneNumberField


class ServicePartner(models.Model):
    class PartnerTypes(models.IntegerChoices):
        FOOD = 1
        LAUNDRY = 2
        TRANSPORT = 3
        RENTAL = 4
        OTHERS = 5
        SHOP = 6
        TOURISM = 7
        # add more here (in house food, in house laundry, etc)

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    type_of_service = models.PositiveSmallIntegerField(choices=PartnerTypes.choices, default=PartnerTypes.OTHERS)
    description = models.TextField(null=True, blank=True)
    phone_number = PhoneNumberField(null=True, blank=True)
    is_visible = models.BooleanField(default=True)

    # Partner preference -> Enum int wise
    @property
    def service(self):
        from service.subapps.food.models import FoodService
        from service.subapps.transport.models import TransportService
        from service.subapps.rental.models import RentalService
        from service.subapps.laundry.models import LaundryService
        from service.subapps.shop.models import ShopService
        from service.subapps.tourism.models import TourismService
        mapping = {
            ServicePartner.PartnerTypes.FOOD: FoodService,
            ServicePartner.PartnerTypes.TRANSPORT: TransportService,
            ServicePartner.PartnerTypes.LAUNDRY: LaundryService,
            ServicePartner.PartnerTypes.RENTAL: RentalService,
            ServicePartner.PartnerTypes.SHOP: ShopService,
            ServicePartner.PartnerTypes.TOURISM: TourismService
        }
        return mapping.get(self.type_of_service)

    def services(self):
        return self.service.objects.filter(partner=self)

    @property
    def service_item(self):
        from service.subapps.food.models import FoodServiceItem
        from service.subapps.transport.models import TransportServiceItem
        from service.subapps.rental.models import RentalServiceItem
        from service.subapps.laundry.models import LaundryServiceItem
        from service.subapps.shop.models import ShopServiceItem
        from service.subapps.tourism.models import TourismServiceItem

        mapping = {
            ServicePartner.PartnerTypes.FOOD: FoodServiceItem,
            ServicePartner.PartnerTypes.TRANSPORT: TransportServiceItem,
            ServicePartner.PartnerTypes.LAUNDRY: LaundryServiceItem,
            ServicePartner.PartnerTypes.RENTAL: RentalServiceItem,
            ServicePartner.PartnerTypes.SHOP: ShopServiceItem,
            ServicePartner.PartnerTypes.TOURISM: TourismServiceItem
        }
        return mapping.get(self.type_of_service)

    def service_items(self):
        return self.service_item.objects.filter(service__partner=self)

    def __str__(self):
        return self.name + " - " + str(self.type_of_service)


class BaseService(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    partner = models.ForeignKey(ServicePartner, on_delete=models.CASCADE)
    charges = models.FloatField(null=True, blank=True)
    tax_rate = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True


# addons format - {$add_on_name: $price }


class BaseServiceItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    service = models.ForeignKey(BaseService, on_delete=models.CASCADE, related_name='service_items')
    image = models.ImageField(upload_to='service_item_images', blank=True, null=True)
    addon = models.JSONField(null=True, blank=True)
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    price = models.FloatField()
    is_active = models.BooleanField(default=True)
    rating = models.FloatField(blank=True, null=True, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True

    def get_catalog_item(self, commission_rate=0):
        ob = copy.deepcopy(self)
        ob.price = round(ob.price * (1 + commission_rate / 100))
        if ob.addon:
            ob.addon = {k: round(v * (1 + commission_rate / 100))
                        for k, v in ob.addon.items()}
        return ob
