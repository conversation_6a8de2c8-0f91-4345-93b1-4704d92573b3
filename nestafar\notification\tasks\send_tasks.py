from notification.models import Notification, NotificationSubscription, NotificationCategory, NotificationTemplates, NotificationLog, NotificationChannelHandler, NotificationChannel
from core.models import PartnerProfile, User
from celery import shared_task
from django.utils import timezone
import importlib
import logging

logger = logging.getLogger(__name__)


from django.db import transaction

def subscribe_user_to_notifications(profile):
    with transaction.atomic():
        # Determine user type and foreign key field
        if isinstance(profile, PartnerProfile):
            user_type = Notification.UserTypeOptions.PARTNER
            filter_kwargs = {'partner': profile}
            subscription_kwargs = 'partner'
        else:
            user_type = Notification.UserTypeOptions.USER
            filter_kwargs = {'user': profile}
            subscription_kwargs = 'user'

        # Get relevant notifications
        notifications = Notification.objects.filter(user_type=user_type)

        # Get existing subscriptions
        existing_notification_ids = set(
            NotificationSubscription.objects.filter(**filter_kwargs)
            .values_list('notification_id', flat=True)
        )

        # Create missing subscriptions
        subscriptions_to_create = [
            NotificationSubscription(
                notification=notification,
                **{subscription_kwargs: profile}
            )
            for notification in notifications
            if notification.id not in existing_notification_ids
        ]

        if subscriptions_to_create:
            NotificationSubscription.objects.bulk_create(subscriptions_to_create)

def validate_data_for_notification(category, data):
    notification_template=NotificationTemplates.get(category.name)
    if not notification_template:
        return False, 'No template found for ' + category.name
    template, datav = notification_template
    for key in datav:
        if key not in data:
            return False, 'Missing ' + key + ' in data'
    return True, template(**data)

def load_handler_from_channel_name(channel_name: str):
    handler_string = NotificationChannelHandler.get(channel_name)
    if not handler_string:
        logger.error(f"No handler configured for channel: {channel_name}")
        return None

    try:
        mods = handler_string.split('.')
        if len(mods) < 2:
            logger.error(f"Invalid handler string format: {handler_string}")
            return None

        module = importlib.import_module('.'.join(mods[:-1]))
        handler = getattr(module, mods[-1])

        # Validate handler has required methods
        if not hasattr(handler, 'send_message'):
            logger.error(f"Handler {handler_string} missing required 'send_message' method")
            return None

        return handler
    except (ImportError, AttributeError) as e:
        logger.error(f"Failed to load handler {handler_string}: {e}")
        return None


def send_message_via_channel(handler, notification_channel, recipient_data, message_body, message_title, category=None, template_data=None):
    """
    Send message via specific channel with proper error handling
    Returns (success, response, message_id)
    """
    try:
        handler_instance = handler()
        
        # Handle different channel types
        if notification_channel == NotificationChannel.WHATSAPP.value:
            # For WhatsApp, use phone number directly
            if 'phone_number' in recipient_data:
                return handler_instance.send_message_to_phone(
                    str(recipient_data['phone_number']), 
                    message_body, 
                    title=message_title,
                    category=category,
                    **(template_data or {})
                )
            elif 'user_id' in recipient_data:
                # Fallback to user ID if phone not available
                result = handler_instance.send_message(
                    recipient_data['user_id'], 
                    message_body, 
                    title=message_title,
                    category=category,
                    **(template_data or {})
                )
                # Normalize result format
                if isinstance(result, tuple) and len(result) >= 2:
                    return result
                else:
                    return (bool(result), str(result), None)
            else:
                return (False, "No valid recipient data for WhatsApp", None)
                
        elif notification_channel in [NotificationChannel.PUSH.value, NotificationChannel.EMAIL.value, NotificationChannel.MESSAGE.value]:
            # For other channels, use user ID
            if 'user_id' in recipient_data:
                result = handler_instance.send_message(
                    recipient_data['user_id'], 
                    message_body, 
                    title=message_title
                )
                # Normalize result format
                if isinstance(result, tuple) and len(result) >= 2:
                    return result
                else:
                    return (bool(result), str(result), None)
            else:
                return (False, f"No user ID provided for {notification_channel}", None)
        else:
            return (False, f"Unsupported channel: {notification_channel}", None)
            
    except Exception as e:
        logger.error(f"Error sending via {notification_channel}: {str(e)}", exc_info=True)
        return (False, str(e), None)

@shared_task
def send_notification(user_id: str, event: str, data: dict):
    category = NotificationCategory.from_name(event)
    if not category:
        logger.error(f"Invalid event name: {event}")
        raise ValueError('Invalid event name ' + event)
    
    error_msg = None
    sent_successfully = False
    
    try:
        notification_types = Notification.objects.filter(
            category=category.name
        )
        
        if not notification_types.exists():
            logger.warning(f"No notification configurations found for category: {category.name}")
            return False, f"No notification configurations found for category: {category.name}"
        
        validated, template = validate_data_for_notification(category, data)
        if not validated:
            error_msg = "Invalid data for notification"
            logger.error(f"Data validation failed for {category.name}: {template}")
            return False, error_msg
            
        title, body = template
        logger.info(f"Processing notification for user {user_id}, category: {category.name}")
        logger.debug(f"Template data - Title: {title}, Body: {body[:100]}...")
        
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"User not found: {user_id}")
            return False, f"User not found: {user_id}"
        
        # Get notification profile
        try:
            if user.is_partner:
                notification_profile = user.partner_profile.notification_profile
            else:
                notification_profile = user.user_profile.notification_profile
        except AttributeError as e:
            logger.error(f"User {user_id} missing notification profile: {str(e)}")
            return False, f"User missing notification profile: {str(e)}"
        
        success_count = 0
        total_count = 0
        
        for notification in notification_types:
            total_count += 1
            logger.info(f"Attempting to send via {notification.channel} channel")
            
            handler = load_handler_from_channel_name(notification.channel)
            if handler is None:
                logger.warning(f"No handler found for channel: {notification.channel}")
                continue
            
            # Create notification log entry
            notification_log = NotificationLog.objects.create(
                notification=notification,
            )
            if user.is_partner:
                notification_log.partner_notification_profile = notification_profile
            else:
                notification_log.user_notification_profile = notification_profile
            
            try:
                logger.info(f"Sending message via {notification.channel} to user {user_id}")

                # Prepare recipient data
                recipient_data = {
                    'user_id': user.id,
                    'phone_number': getattr(user, 'phone', None)
                }

                # Use the helper function for consistency
                success, response, message_id = send_message_via_channel(
                    handler,
                    notification.channel,
                    recipient_data,
                    body,
                    title,
                    category=category.name,
                    template_data=data
                )               
                if success:
                    notification_log.is_sent = True
                    notification_log.message_status = 'sent'
                    success_count += 1
                    sent_successfully = True
                    
                    # Store message ID if available (for WhatsApp)
                    if message_id:
                        notification_log.message_id = message_id
                        logger.info(f"Message sent successfully via {notification.channel}, message_id: {message_id}")
                    else:
                        logger.info(f"Message sent successfully via {notification.channel}")
                else:
                    notification_log.is_sent = False
                    notification_log.message_status = 'failed'
                    error_msg = response or "Unknown error"
                    notification_log.error_msg = error_msg
                    logger.error(f"Failed to send via {notification.channel}: {error_msg}")
                    
            except Exception as e:
                error_msg = str(e)
                notification_log.is_sent = False
                notification_log.message_status = 'failed'
                notification_log.error_msg = error_msg
                logger.error(f"Exception sending via {notification.channel}: {error_msg}", exc_info=True)
            
            notification_log.save()
        
        if sent_successfully:
            logger.info(f"Notification sent successfully: {success_count}/{total_count} channels")
            return True, f'Notification sent successfully via {success_count}/{total_count} channels'
        else:
            logger.error(f"All notification channels failed for user {user_id}, category {category.name}")
            return False, error_msg or f"All {total_count} notification channels failed"
            
    except Exception as e:
        logger.error(f"Unexpected error in send_notification: {str(e)}", exc_info=True)
        return False, f"Unexpected error: {str(e)}"


@shared_task
def send_service_partner_notification(service_partner_id: str, event: str, data: dict):
    """
    Send notification to a ServicePartner using their phone number
    """
    from service.models.service import ServicePartner
    
    category = NotificationCategory.from_name(event)
    if not category:
        raise ValueError('Invalid event name ' + event)
    
    error_msg = None
    
    notification_types = Notification.objects.filter(
        category=category.name,
        user_type=Notification.UserTypeOptions.PARTNER
    )
    
    validated, template = validate_data_for_notification(category, data)
    if not validated:
        error_msg = "Invalid data for notification"
        return False, error_msg
    
    title, body = template
    
    try:
        service_partner = ServicePartner.objects.get(id=service_partner_id)
        
        # Check if service partner has phone number
        if not service_partner.phone_number:
            return False, 'ServicePartner has no phone number'
        
        success_count = 0
        total_count = 0
        sent_successfully = False
        
        for notification in notification_types:
            total_count += 1
            handler = load_handler_from_channel_name(notification.channel)
            if handler is None:
                continue
            
            # Create notification log entry
            notification_log = NotificationLog.objects.create(
                notification=notification,
                service_partner=service_partner
            )
            
            try:
                logger.info(f"Sending message via {notification.channel} to ServicePartner {service_partner_id}")
                
                # Prepare recipient data
                recipient_data = {
                    'phone_number': service_partner.phone_number,
                    'service_partner_id': service_partner_id
                }
                
                # Send message via appropriate channel
                success, response, message_id = send_message_via_channel(
                    handler, 
                    notification.channel, 
                    recipient_data, 
                    body, 
                    title
                )
                
                # Update notification log based on success
                if success:
                    notification_log.is_sent = True
                    notification_log.message_status = 'sent'
                    success_count += 1
                    sent_successfully = True
                    if message_id:
                        notification_log.message_id = message_id
                        logger.info(f"ServicePartner message sent successfully via {notification.channel}, message_id: {message_id}")
                    else:
                        logger.info(f"ServicePartner message sent successfully via {notification.channel}")
                else:
                    notification_log.is_sent = False
                    notification_log.message_status = 'failed'
                    error_msg = response or "Unknown error"
                    notification_log.error_msg = error_msg
                    logger.error(f"Failed to send to ServicePartner via {notification.channel}: {error_msg}")
                    
            except Exception as e:
                error_msg = str(e)
                notification_log.is_sent = False
                notification_log.message_status = 'failed'
                notification_log.error_msg = error_msg
                logger.error(f"Exception sending to ServicePartner via {notification.channel}: {error_msg}", exc_info=True)
            
            notification_log.save()
        
        if sent_successfully:
            logger.info(f"ServicePartner notification sent successfully: {success_count}/{total_count} channels")
            return True, f'Notification sent to ServicePartner via {success_count}/{total_count} channels'
        else:
            logger.error(f"All notification channels failed for ServicePartner {service_partner_id}")
            return False, error_msg or f"All {total_count} notification channels failed"
        
    except ServicePartner.DoesNotExist:
        return False, f'ServicePartner with id {service_partner_id} not found'
    except Exception as e:
        return False, str(e)


@shared_task
def update_message_status(message_id, status, timestamp=None):
    """
    Update message status based on webhook data
    """
    # Valid status values
    VALID_STATUSES = ['delivered', 'read', 'failed', 'sent', 'pending']
    
    if status not in VALID_STATUSES:
        logger.warning(f"Invalid status '{status}' for message_id: {message_id}")
        return

    try:
        # Find the notification log with this message ID
        notification_log = NotificationLog.objects.filter(message_id=message_id).first()
        
        if notification_log:
            # Update status
            notification_log.message_status = status
            
            # Update timestamps based on status
            if timestamp:
                try:
                    timestamp_dt = timezone.datetime.fromtimestamp(
                        int(timestamp), tz=timezone.get_current_timezone()
                    )
                    
                    if status == 'delivered':
                        notification_log.delivery_timestamp = timestamp_dt
                    elif status == 'read':
                        notification_log.read_timestamp = timestamp_dt
                        notification_log.is_read = True
                except (ValueError, TypeError) as e:
                    logger.error(f"Invalid timestamp format: {timestamp}, error: {e}")
            
            # Mark as sent if delivered or read
            if status in ['delivered', 'read']:
                notification_log.is_sent = True
            elif status == 'failed':
                notification_log.is_sent = False
                notification_log.error_msg = 'Message delivery failed'
            
            notification_log.save()
            logger.info(f"Updated message {message_id} status to {status}")
            
        else:
            logger.warning(f"NotificationLog not found for message_id: {message_id}")
            
    except Exception as e:
        logger.error(f"Error updating message status: {str(e)}")