# Generated by Django 4.2.7 on 2025-07-19 07:37

from django.db import migrations, models
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AllotedRoom',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('amount', models.FloatField()),
                ('payment_method', models.CharField(choices=[('credit_card', 'Credit Card'), ('debit_card', 'Debit Card'), ('paypal', 'Paypal'), ('cash', 'Cash'), ('bank_transfer', 'Bank Transfer')], max_length=20)),
                ('transaction_id', models.CharField(max_length=50)),
                ('status', models.CharField(choices=[('completed', 'Completed'), ('pending', 'Pending'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('payment_details', models.JSONField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PreCheckin',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('number_of_rooms', models.PositiveIntegerField(default=1)),
                ('stay_duration', models.PositiveIntegerField(default=1)),
                ('welcome_message', models.TextField(blank=True, null=True)),
                ('expected_checkin', models.DateTimeField()),
                ('total_amount', models.FloatField(default=0)),
                ('amount_paid', models.FloatField(default=0)),
                ('pending_balance', models.FloatField(default=0)),
                ('payment_status', models.CharField(choices=[('completed', 'Completed'), ('pending', 'Pending'), ('partial', 'Partial'), ('unpaid', 'Unpaid')], default='unpaid', max_length=20)),
                ('payment_id', models.CharField(blank=True, max_length=50, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('arrived', 'Arrived'), ('partial', 'Partial'), ('cancelled', 'Cancelled'), ('checked_in', 'Checked In'), ('checked_out', 'Checked Out')], default='pending', max_length=20)),
                ('guest_address', models.CharField(blank=True, max_length=255, null=True)),
                ('special_requests', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PreCheckinGuest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('is_primary', models.BooleanField(default=False)),
                ('id_proof', models.ImageField(blank=True, null=True, upload_to='precheckin/id_proofs/')),
                ('age', models.PositiveIntegerField(default=18)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_verified', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('phone', phonenumber_field.modelfields.PhoneNumberField(max_length=128, region=None, unique=True)),
                ('email', models.EmailField(max_length=254)),
                ('description', models.TextField()),
                ('amenities', models.JSONField(blank=True, null=True)),
                ('nearby', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ProfileImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(upload_to='booking_profiles/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('check_in', models.DateTimeField()),
                ('check_out', models.DateTimeField()),
                ('guests', models.IntegerField()),
                ('total', models.FloatField()),
                ('paid', models.FloatField(default=0)),
                ('requests', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('checked_in', 'Checked In'), ('checked_out', 'Checked Out'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('booking_details', models.JSONField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
