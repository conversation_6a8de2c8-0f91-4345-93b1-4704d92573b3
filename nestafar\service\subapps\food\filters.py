from django_filters import rest_framework as django_filters
from .models import *
from service.filters import BaseCartFilter


class FoodServiceItemFilter(django_filters.FilterSet):
    class Meta:
        model = FoodServiceItem
        fields = ['category', 'vegetarian', 'is_special',
                  'service', 'price', 'is_active', 'rating']


class FoodOrderFilter(django_filters.FilterSet):
    class Meta:
        model = FoodOrder
        fields = ['status', 'guest', 'cart']


class FoodCartFilter(BaseCartFilter):
    class Meta:
        model = FoodCart
        fields = ['status', 'guest']
