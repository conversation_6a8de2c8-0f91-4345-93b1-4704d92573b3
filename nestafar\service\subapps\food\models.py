from service.models import BaseService, BaseServiceItem, BaseCart, BaseCartItems, BaseOrder, BaseOrderItem
from django.db import models


class FoodService(BaseService):
    veg_only = models.BooleanField(default=False)
    active_days = models.JSONField(default=list)
    opening_time = models.TimeField(default=None, null=True, blank=True)
    closing_time = models.TimeField(default=None, null=True, blank=True)

    def __str__(self):
        return self.name + " by " + self.partner.name


class FoodServiceItem(BaseServiceItem):
    service = models.ForeignKey(FoodService, on_delete=models.CASCADE, related_name='service_items')
    vegetarian = models.BooleanField(default=False)
    category = models.Char<PERSON>ield(max_length=100, default="GEN")
    is_special = models.BooleanField(default=False)

    def __str__(self):
        return self.name


class FoodCart(BaseCart):

    def __str__(self):
        return self.guest.user.name + " " + self.guest.room.property.name + " " + str(self.total)


class FoodCartItems(BaseCartItems):
    item = models.ForeignKey(FoodServiceItem, on_delete=models.CASCADE, related_name='cart_items')
    cart = models.ForeignKey(FoodCart, on_delete=models.CASCADE, related_name='cart_items')
    class Meta:
        verbose_name_plural = "Food cart items"


class FoodOrder(BaseOrder):
    cart = models.ForeignKey(FoodCart, on_delete=models.CASCADE, related_name='orders')
    service = models.ForeignKey(FoodService, on_delete=models.CASCADE, related_name='orders')

    def __str__(self):
        return self.guest.user.name + "_" + self.service.name + "_"


class FoodOrderItem(BaseOrderItem):
    item = models.ForeignKey(FoodServiceItem, on_delete=models.CASCADE, related_name='order_items')
    order = models.ForeignKey(FoodOrder, on_delete=models.CASCADE, related_name='order_items')

    def __str__(self):
        return self.item.name + "_" + self.order.guest.user.name + "_" + self.order.service.name
