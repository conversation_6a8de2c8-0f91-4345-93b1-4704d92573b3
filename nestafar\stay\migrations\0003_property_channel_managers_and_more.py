# Generated by Django 4.2.7 on 2025-08-07 10:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0002_property_hotel_code_property_property_hotel_code_idx'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='channel_managers',
            field=models.JSONField(blank=True, default=dict, help_text="Channel manager integrations configuration. Format: {'aiosell': {'enabled': True, 'config': {...}}, ...}"),
        ),
        migrations.AddField(
            model_name='property',
            name='primary_channel_manager',
            field=models.Char<PERSON>ield(blank=True, help_text="Primary channel manager for this property (e.g., 'aiosell', 'booking_com')", max_length=50, null=True),
        ),
    ]
