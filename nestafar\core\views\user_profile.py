from rest_framework.views import APIView
from core.serializers import UserProfileSerializer, PartnerProfileSerializer, RoleSerializer, UserRoleSerializer
from nestafar.responses import SuccessResponse, BadRequestResponse
from rest_framework.permissions import IsAuthenticated
from core.permissions import PartnerPermission
from core.models import User, Role, UserRole
from django.shortcuts import get_object_or_404
from rest_framework import viewsets
from rest_framework_simplejwt.tokens import RefreshToken
from django.apps import apps
from rest_framework.views import APIView
from rest_framework.exceptions import ValidationError
from django.db.utils import DatabaseError
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny

@api_view(['GET'])
@permission_classes([PartnerPermission, IsAuthenticated])
def get_user_by_phone(request):
    phone = request.query_params.get('phone')
    
    if not phone:
        return BadRequestResponse(message="Phone number is required")
        
    try:
        user = User.objects.get(phone=phone)
        return SuccessResponse(data={'name': user.name}, message="User found successfully")
    except User.DoesNotExist:
        return BadRequestResponse(message="User not found")


class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        serializer = UserProfileSerializer(user)
        return SuccessResponse(data=serializer.data, message="User profile fetched successfully")

    def post(self, request):
        user = request.user
        if not user.is_partner:
            return BadRequestResponse(message="User is not a partner")
        user_id = request.data.get('user_id')
        image = request.FILES.get('image')

        user = get_object_or_404(User, id=user_id)
        profile = user.user_profile

        if image:
            profile.image.save(str(user.id) + '/' + image.name, image.file)
            profile.save()
            return SuccessResponse(data=UserProfileSerializer(user).data, message="User profile updated successfully")
        else:
            return BadRequestResponse(message="Image field is required")

class PartnerProfileView(APIView):
    permission_classes = [PartnerPermission]

    def get(self, request):
        user = request.user
        serializer = PartnerProfileSerializer(user.partner_profile)
        return SuccessResponse(data=serializer.data, message="Partner profile fetched successfully")

    def put(self, request):
        user = request.user
        phone = request.data.get('phone')
        name = request.data.get('name')
        email = request.data.get('email')
        
        active_property_id = request.data.get('active_property_id')

        user.phone = phone if phone else user.phone
        user.name = name if name else user.name
        user.email = email if email else user.email
        user.save()

        if user.is_partner:
            serializer = PartnerProfileSerializer(user.partner_profile, data=request.data, partial=True)
            if serializer.is_valid():
                serializer.save()

        if active_property_id:
            Property = apps.get_model('stay', 'Property')
            try:
                active_property = Property.objects.get(id=active_property_id)
                user.partner_profile.active_property = active_property
                user.partner_profile.save()
            except Property.DoesNotExist:
                return BadRequestResponse(message="Invalid property ID")

        return SuccessResponse(data={}, message="Partner profile updated successfully")

class RoleViewSet(viewsets.ModelViewSet):
    queryset = Role.objects.all()
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]
    permission_required = ['view_role', 'change_role', 'delete_role', 'add_role']

    def list(self, request, *args, **kwargs):
        roles = Role.objects.all()
        serializer = RoleSerializer(roles, many=True)
        return SuccessResponse(data=serializer.data, message="Roles fetched successfully")

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            return SuccessResponse(data=serializer.data, message="Role created successfully")
        except ValidationError as e:
            return BadRequestResponse(message="Validation failed", data=e.detail)
        except DatabaseError as e:
            return BadRequestResponse(message= f"Failed to create role {str(e)}")

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return SuccessResponse(data=serializer.data, message="Role updated successfully")
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return SuccessResponse(message="Role has been deleted")
        except Exception as e:
            return BadRequestResponse(message=str(e))

class UserRoleViewSet(viewsets.ModelViewSet):
    queryset = UserRole.objects.all()
    serializer_class = UserRoleSerializer
    permission_classes = [IsAuthenticated]
    permission_required = ['view_userrole', 'change_userrole', 'delete_userrole', 'add_userrole']

    def list(self, request, *args, **kwargs):
        user_roles = UserRole.objects.all()
        serializer = UserRoleSerializer(user_roles, many=True)
        return SuccessResponse(data=serializer.data, message="User roles fetched successfully")

    def create(self, request, *args, **kwargs):
        user_id = request.data.get('user_id')
        role_id = request.data.get('role_id')
        property_id = request.data.get('property_id')
        user = get_object_or_404(User, id=user_id)
        role = get_object_or_404(Role, id=role_id)

        try:
            # Check if UserRole already exists
            user_role, created = UserRole.objects.get_or_create(user=user, role=role, property_id=property_id)
            
            if created:
                message = "Role assigned successfully"
            else:
                message = "Role already assigned to user"
            
            return SuccessResponse(data=UserRoleSerializer(user_role).data, message=message)
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_update(serializer)
            return SuccessResponse(data=serializer.data, message="User role updated successfully")
        except Exception as e:
            return BadRequestResponse(message=str(e))

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            self.perform_destroy(instance)
            return SuccessResponse(message="User role has been deleted")
        except Exception as e:
            return BadRequestResponse(message=str(e))

class DeleteAccountView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request):
        user = request.user
        
        # Revoke JWT tokens
        try:
            refresh_token = request.data["refresh_token"]
            token = RefreshToken(refresh_token)
            token.blacklist()
        except Exception as e:
            return BadRequestResponse(message= "Invalid token", success= False)

        user.delete()
        return SuccessResponse(message= "User account deleted successfully", success = True)
