import os
from celery import Celery
from django.conf import settings
from celery.schedules import crontab
from celery.signals import setup_logging, task_failure, task_retry, worker_ready
import logging

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "nestafar.settings")

app = Celery("nestafar")

# Configure logging using Celery's recommended approach
@setup_logging.connect
def config_loggers(*args, **kwargs):
    """Let Django's LOGGING dict drive Celery worker logging.

    Celery would otherwise install its own handlers; we rely on Django's
    configuration (already loaded because we set DJANGO_SETTINGS_MODULE) so we
    only ensure the root logger has at least a basic config if Django LOGGING
    dict was not found (paranoia fallback).
    """
    from django.conf import settings
    import logging.config

    if hasattr(settings, 'LOGGING'):
        # settings.LOGGING already dictConfig'ed by Django on startup normally.
        # Calling again is harmless and ensures child processes pick it up.
        logging.config.dictConfig(settings.LOGGING)
    else:  # pragma: no cover - safety fallback
        logging.basicConfig(level=logging.INFO)

@worker_ready.connect
def worker_ready(**kwargs):
    """Log when worker is ready."""
    logger = logging.getLogger(__name__)
    logger.info('Celery worker ready')

@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, 
                        traceback=None, einfo=None, **kwargs):
    """Log task failures with detailed information."""
    logger = logging.getLogger(__name__)
    logger.error(
        f'Task {sender.name}[{task_id}] failed: {exception}',
        extra={
            'task_name': sender.name,
            'task_id': task_id,
            'exception': str(exception),
        }
    )

@task_retry.connect
def task_retry_handler(sender=None, task_id=None, reason=None, 
                      einfo=None, **kwargs):
    """Log task retries."""
    logger = logging.getLogger(__name__)
    logger.warning(
        f'Task {sender.name}[{task_id}] retry: {reason}',
        extra={
            'task_name': sender.name,
            'task_id': task_id,
            'reason': str(reason),
        }
    )


app.config_from_object('django.conf:settings', namespace="CELERY")

app.autodiscover_tasks(lambda: settings.INSTALLED_APPS)

app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

# Worker configuration with proper logging setup
app.conf.update(
    # Use Django's logging configuration instead of Celery's default
    worker_hijack_root_logger=False,
    worker_log_color=False,  # Disable color logging for better compatibility
    worker_disable_rate_limits=True,
    task_ignore_result=False,
    result_expires=3600,
    # Task execution settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    # Timezone configuration
    timezone='Asia/Kolkata',
    enable_utc=False,
    # Worker pool settings for better performance
    worker_pool_restarts=True,
    worker_max_tasks_per_child=1000,
)

#Periodic tasks
app.conf.beat_schedule = {
    'send-daily-summary': {
        'task': 'notification.signals.send_daily_summary_notifications',
        'schedule': crontab(hour=23, minute=30),  # Run at 11:30 PM every day
    },
    
    # Daily onboarding reminders at 10 AM
    'check-onboarding-reminders': {
        'task': 'notification.tasks.flow_tasks.check_onboarding_reminders',
        'schedule': crontab(hour=10, minute=0),
    },
    
    # Daily pre-checkin reminders at 9 AM
    'check-precheckin-reminders': {
        'task': 'notification.tasks.flow_tasks.check_precheckin_reminders',
        'schedule': crontab(hour=9, minute=0),
    },
    
    # Daily dinner reminders at 8 PM
    'send-dinner-reminders': {
        'task': 'notification.tasks.flow_tasks.send_dinner_reminders',
        'schedule': crontab(hour=20, minute=0),
    },
    
    # Vendor order reminders every 15 minutes
    'check-vendor-order-reminders': {
        'task': 'notification.tasks.flow_tasks.check_vendor_order_reminders',
        'schedule': crontab(minute='*/15'),
    },
    
    # Weekly reports every Monday at 10 AM
    'generate-weekly-reports': {
        'task': 'notification.tasks.flow_tasks.generate_weekly_reports',
        'schedule': crontab(hour=10, minute=0, day_of_week=1),
    },
}

@app.task(bind=True)
def debug_task(self):
    print(f"Request: {self.request!r}")