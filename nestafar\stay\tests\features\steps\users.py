from pytest_bdd import given, parsers, then, when
from ..helper import create_user
from distutils.util import strtobool
from core.models import User
from rest_framework.test import APIClient
from django.urls import reverse
import logging

logger = logging.getLogger(__name__)


@given(parsers.parse("Set Auth User to {name}"))
def setup_auth_user(context, name):
    user = User.objects.get(name=name)
    client = APIClient()
    generate_otp_response = client.post(
        reverse("core:generate_otp_dev"),
        {
            "phone": user.phone,
        },
    )
    logger.info(generate_otp_response.json())
    assert generate_otp_response.status_code == 200
    otp = "123456"
    login_response = client.post(
        reverse("core:login"), {"phone": user.phone, "otp": otp, "type": "token"}
    )
    assert login_response.status_code == 200
    token = login_response.json()["access"]
    client.credentials(HTTP_AUTHORIZATION="Bearer " + token)
    context.auth_client = client
    context.auth_user = user
