import logging
import re
from pathlib import Path
import pandas as pd
from django.core.management import BaseCommand, CommandError
from django.db import transaction
from geo.models import Country, State, City, AdministrationArea

logger = logging.getLogger(__name__)

class GeographicDataImporter:
    """<PERSON>les importing geographic data from CSV files."""
    
    def __init__(self, filepath: str):
        self.filepath = Path(filepath)
        self.batch_size = 5000
        
    def validate_file(self) -> pd.DataFrame:
        """Validates and reads the CSV file."""
        if not self.filepath.exists():
            raise CommandError(f"File not found: {self.filepath}")
            
        required_columns = {
            'Office Name', 'Pincode', 'District', 'StateName'
        }
        
        try:
            df = pd.read_csv(self.filepath)
            missing_cols = required_columns - set(df.columns)
            if missing_cols:
                raise CommandError(f"Missing required columns: {missing_cols}")
            
            return df
        except Exception as e:
            raise CommandError(f"Error reading CSV file: {e}")

    @staticmethod
    def clean_name(name: str) -> str:
        """Cleans and standardizes location names."""
        # Remove PO/SO/BO suffixes and extra whitespace
        cleaned = re.sub(r'\s+[A-Z].?O.?$', '', name)
        return cleaned.strip().title()

    @staticmethod
    def generate_state_code(state_name: str) -> str:
        """Generates a 2-letter state code."""
        words = state_name.split()
        if len(words) == 1:
            return words[0][:2].upper()
        return ''.join(word[0].upper() for word in words[:2])

    def setup_states(self, country: Country, df: pd.DataFrame) -> None:
        """Sets up states for the given country."""
        logger.info("Setting up states...")
        
        distinct_states = df['StateName'].drop_duplicates()
        for state_name in distinct_states:
            cleaned_name = self.clean_name(state_name)
            code = self.generate_state_code(cleaned_name)
            
            try:
                State.get_or_create_state(
                    country=country,
                    name=cleaned_name,
                    code=code
                )
                logger.debug(f"Processed state: {cleaned_name}")
            except Exception as e:
                logger.error(f"Error processing state {cleaned_name}: {e}")

    def import_administration_areas(self, df: pd.DataFrame) -> None:
        """Imports administration areas from the dataframe."""
        logger.info("Importing administration areas...")
        
        # Create a state name lookup for better performance
        state_lookup = {
            state.name: state 
            for state in State.objects.all()
        }

        total_rows = len(df)
        created_count = 0
        error_count = 0

        for start_idx in range(0, total_rows, self.batch_size):
            batch = df.iloc[start_idx:start_idx + self.batch_size]
            
            with transaction.atomic():
                for _, row in batch.iterrows():
                    try:
                        state_name = self.clean_name(row['StateName'])
                        state = state_lookup.get(state_name)
                        
                        if not state:
                            logger.error(f"State not found: {state_name}")
                            continue

                        city_name = self.clean_name(row['District'])
                        city, _ = City.objects.get_or_create(
                            name=city_name,
                            state=state
                        )

                        area_name = self.clean_name(row['Office Name'])
                        pincode = str(row['Pincode']).zfill(6)

                        # Use the model's get_or_create method that handles duplicates
                        AdministrationArea.get_or_create_area(
                            city=city,
                            pincode=pincode,
                            name=area_name
                        )
                        created_count += 1
                        
                        if created_count % 100 == 0:
                            logger.info(f"Processed {created_count}/{total_rows} areas...")
                            
                    except Exception as e:
                        error_count += 1
                        logger.error(f"Error processing row {row.to_dict()}: {e}")

        logger.info(f"Import completed. Created/Updated: {created_count}, Errors: {error_count}")

class Command(BaseCommand):
    help = 'Imports geographic data from a CSV file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            help='Path to the CSV file containing geographic data',
            default='Assets/geo/all_pincodes.csv'
        )

    def handle(self, *args, **options):
        try:
            filepath = options['file']
            importer = GeographicDataImporter(filepath)
            
            # Configure logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s'
            )
            
            # Start import process
            logger.info("Starting geographic data import...")
            df = importer.validate_file()
            
            # Setup country
            country, _ = Country.objects.get_or_create(
                name='India',
                code='IN',
                phone_code= '+91'
            )
            
            # Import data
            importer.setup_states(country, df)
            importer.import_administration_areas(df)
            
            logger.info("Import completed successfully!")
            
        except Exception as e:
            logger.error(f"Import failed: {e}")
            raise CommandError(f"Import failed: {e}")