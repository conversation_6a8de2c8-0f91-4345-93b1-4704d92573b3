import unittest
from unittest.mock import patch, MagicMock, call
from django.test import TestCase
from django.utils import timezone
from datetime import timed<PERSON><PERSON>, datetime
from decimal import Decimal

from core.models import User, PartnerProfile
from stay.models import Property, Room, Guest
from booking.models import <PERSON><PERSON><PERSON>ckin, Pre<PERSON><PERSON>ckinGuest
from service.subapps.food.models import FoodService, FoodOrder, FoodCart
from notification.models.onboarding import OnboardingStatus, WeeklyReport
from notification.models import WhatsAppContact
from notification.channel.whatsapp import WhatsAppChannel
from notification.tasks.flow_tasks import (
    check_onboarding_reminders,
    check_precheckin_reminders,
    send_dinner_reminders,
    check_vendor_order_reminders,
    generate_weekly_reports,
    send_signup_welcome_message,
    check_and_update_onboarding_status,
    process_guest_checkout_flow
)


class WhatsAppFlowTestCase(TestCase):
    """Comprehensive test suite for WhatsApp business flows"""

    def setUp(self):
        """Set up test data"""
        # Create test user and partner
        self.user = User.objects.create(
            name="Test Partner",
            email="<EMAIL>",
            phone="+919876543210"
        )
        
        self.partner = PartnerProfile.objects.create(
            user=self.user,
            name="Test Partner Profile"
        )
        
        # Create test property
        self.property = Property.objects.create(
            name="Test Hotel",
            address="Test Address",
            description="Test Description"
        )
        self.property.staffs.add(self.partner)
        
        # Create test room
        self.room = Room.objects.create(
            property=self.property,
            room_no="101",
            rate=2000.00
        )
        
        # Create test guest user
        self.guest_user = User.objects.create(
            name="Test Guest",
            email="<EMAIL>", 
            phone="+919876543211"
        )
        
        # Create test guest
        self.guest = Guest.objects.create(
            user=self.guest_user,
            room=self.room,
            checked_in=True,
            check_in_date=timezone.now(),
            checked_out=False
        )
        
        # Create WhatsApp contacts
        WhatsAppContact.objects.create(
            user=self.user,
            phone_number=self.user.phone,
            is_active=True,
            is_verified=True
        )
        
        WhatsAppContact.objects.create(
            user=self.guest_user,
            phone_number=self.guest_user.phone,
            is_active=True,
            is_verified=True
        )

    @patch('notification.tasks.send_notification.delay')
    def test_onboarding_flow_complete(self, mock_send_notification):
        """Test complete onboarding flow"""
        
        # Test signup welcome message
        send_signup_welcome_message(str(self.partner.id), str(self.property.id))
        
        # Verify onboarding status created
        onboarding = OnboardingStatus.objects.get(
            partner=self.partner,
            property=self.property
        )
        self.assertEqual(onboarding.status, OnboardingStatus.StatusChoices.PENDING)
        
        # Test onboarding reminder
        check_onboarding_reminders()
        
        # Verify reminder was sent
        mock_send_notification.assert_called()
        reminder_calls = [call for call in mock_send_notification.call_args_list 
                         if call[0][1] == 'ONBOARDING_REMINDER']
        self.assertTrue(len(reminder_calls) > 0)
        
        # Complete onboarding steps
        onboarding.property_details_added = True
        onboarding.property_photos_uploaded = True
        onboarding.rooms_added = True
        onboarding.room_photos_uploaded = True
        onboarding.services_added = True
        onboarding.save()
        
        # Test completion check
        check_and_update_onboarding_status(str(self.partner.id), str(self.property.id))
        
        # Verify completion
        onboarding.refresh_from_db()
        self.assertEqual(onboarding.status, OnboardingStatus.StatusChoices.COMPLETED)
        self.assertTrue(onboarding.is_complete())
        self.assertEqual(onboarding.calculate_completion_percentage(), 100.0)

    @patch('notification.tasks.send_notification.delay')
    def test_checkin_flow_complete(self, mock_send_notification):
        """Test complete check-in flow"""
        
        # Create pre-checkin
        expected_checkin = timezone.now() + timedelta(hours=12)
        precheckin = PreCheckin.objects.create(
            property=self.property,
            expected_checkin=expected_checkin,
            status='pending',
            number_of_rooms=1,
            stay_duration=2
        )
        
        # Create pre-checkin guest
        precheckin_guest = PreCheckinGuest.objects.create(
            pre_checkin=precheckin,
            user=self.guest_user,
            is_primary=True
        )
        
        # Test pre-checkin reminders
        check_precheckin_reminders()
        
        # Verify reminder was sent
        reminder_calls = [call for call in mock_send_notification.call_args_list 
                         if call[0][1] == 'PRECHECKIN_REMINDER']
        self.assertTrue(len(reminder_calls) > 0)
        
        # Test auto-cancellation for overdue pre-checkins
        overdue_precheckin = PreCheckin.objects.create(
            property=self.property,
            expected_checkin=timezone.now() - timedelta(hours=3),
            status='pending'
        )
        
        check_precheckin_reminders()
        
        # Verify auto-cancellation
        overdue_precheckin.refresh_from_db()
        self.assertEqual(overdue_precheckin.status, 'cancelled')

    @patch('notification.tasks.send_notification.delay')
    def test_service_management_flow(self, mock_send_notification):
        """Test service management flow"""
        
        # Test dinner reminders
        send_dinner_reminders()
        
        # Verify dinner reminder was sent
        dinner_calls = [call for call in mock_send_notification.call_args_list 
                       if call[0][1] == 'DINNER_REMINDER']
        self.assertTrue(len(dinner_calls) > 0)
        
        # Create food service and order for vendor reminders
        food_service = FoodService.objects.create(
            name="Test Restaurant",
            property=self.property,
            is_visible=True
        )
        
        # Create food cart and order
        cart = FoodCart.objects.create(
            guest=self.guest,
            status=FoodCart.CartStatus.PENDING
        )
        
        # Create order from 20 minutes ago
        old_time = timezone.now() - timedelta(minutes=20)
        order = FoodOrder.objects.create(
            guest=self.guest,
            service_partner=food_service,
            status=FoodOrder.OrderStatus.PENDING,
            total=500.00,
            commissions=50.00
        )
        order.created_at = old_time
        order.save()
        
        # Test vendor order reminders
        check_vendor_order_reminders()
        
        # Verify vendor reminder was sent
        vendor_calls = [call for call in mock_send_notification.call_args_list 
                       if call[0][1] == 'VENDOR_ORDER_REMINDER']
        self.assertTrue(len(vendor_calls) > 0)

    @patch('notification.tasks.send_notification.delay')
    def test_weekly_report_generation(self, mock_send_notification):
        """Test weekly report generation"""
        
        # Create some test data for the week
        week_start = timezone.now().date() - timedelta(days=7)
        week_end = week_start + timedelta(days=6)
        
        # Create completed order for GMV calculation
        order = FoodOrder.objects.create(
            guest=self.guest,
            status=FoodOrder.OrderStatus.COMPLETED,
            total=1000.00,
            commissions=100.00
        )
        
        # Generate weekly reports
        generate_weekly_reports()
        
        # Verify report was created
        report = WeeklyReport.objects.filter(
            partner=self.partner,
            property=self.property
        ).first()
        
        self.assertIsNotNone(report)
        self.assertIsNotNone(report.recommendations)
        
        # Verify notification was sent
        report_calls = [call for call in mock_send_notification.call_args_list 
                       if call[0][1] == 'WEEKLY_REPORT']
        self.assertTrue(len(report_calls) > 0)

    @patch('notification.tasks.send_notification.delay')
    def test_checkout_flow_complete(self, mock_send_notification):
        """Test complete checkout flow"""
        
        # Set checkout date
        self.guest.check_out_date = timezone.now()
        self.guest.checked_out = True
        self.guest.save()
        
        # Process checkout flow
        process_guest_checkout_flow(str(self.guest.id))
        
        # Verify checkout bill and review request were sent
        checkout_calls = [call for call in mock_send_notification.call_args_list 
                         if call[0][1] in ['CHECKOUT_BILL', 'REVIEW_REQUEST']]
        self.assertTrue(len(checkout_calls) >= 2)

    def test_whatsapp_template_parameter_building(self):
        """Test WhatsApp template parameter building"""
        whatsapp = WhatsAppChannel()
        
        # Test onboarding reminder parameters
        components = whatsapp._build_template_components(
            'ONBOARDING_REMINDER',
            partner_name="Test Partner",
            property_name="Test Hotel",
            missing_steps=["Property photos", "Services setup"],
            completion_percentage="60"
        )
        
        self.assertIsNotNone(components)
        self.assertTrue(len(components) > 0)
        
        # Verify body parameters
        body_component = next((c for c in components if c['type'] == 'body'), None)
        self.assertIsNotNone(body_component)
        self.assertEqual(len(body_component['parameters']), 4)
        
        # Test interactive buttons
        button_component = next((c for c in components if c['type'] == 'button'), None)
        self.assertIsNotNone(button_component)

    def test_currency_formatting(self):
        """Test Indian Rupees currency formatting"""
        whatsapp = WhatsAppChannel()
        
        # Test currency parameter formatting
        formatted = whatsapp._format_parameter_value('total_amount', '1500.50')
        self.assertEqual(formatted, '₹1,500.50')
        
        formatted = whatsapp._format_parameter_value('gmv', 25000)
        self.assertEqual(formatted, '₹25,000.00')
        
        # Test percentage formatting
        formatted = whatsapp._format_parameter_value('occupancy_rate', '75.5')
        self.assertEqual(formatted, '75.5%')

    def test_template_compliance(self):
        """Test template compliance with WhatsApp Business API"""
        whatsapp = WhatsAppChannel()
        
        # Test active templates
        active_templates = [
            'USER_CHECKIN_INITIATED',
            'USER_ORDER_COMPLETED', 
            'USER_ORDER_PLACED',
            'PRE_CHECKIN_CREATED'
        ]
        
        for category in active_templates:
            template_name = whatsapp._get_template_name(category)
            self.assertIsNotNone(template_name)
            
            # Test component building doesn't fail
            components = whatsapp._build_template_components(category, username="Test", order_id="123")
            # Should not raise exception

    def test_error_handling(self):
        """Test error handling in WhatsApp flows"""
        
        # Test with invalid user ID
        with self.assertLogs('notification.tasks.flow_tasks', level='ERROR'):
            check_and_update_onboarding_status("invalid-id", str(self.property.id))
        
        # Test with invalid property ID  
        with self.assertLogs('notification.tasks.flow_tasks', level='ERROR'):
            send_signup_welcome_message(str(self.partner.id), "invalid-id")

    def tearDown(self):
        """Clean up test data"""
        # Clean up is handled by Django's test framework
        pass
