from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from unittest.mock import patch

from booking.models import Reservation
from booking.utils import trigger_precheckin_and_block
from stay.models import Property
from geo.models import Location

User = get_user_model()


class TriggerPrecheckinTests(TestCase):
    def setUp(self):
        self.location = Location.objects.create(
            name='Loc', administrative_area=None, postal_code='000000', country='IN'
        )
        self.user = User.objects.create(name='Guest User', phone='9999999999')
        self.property = Property.objects.create(location=self.location, name='Hotel', rooms=5)
        self.base_check_in = timezone.now() + timezone.timedelta(days=1)
        self.base_check_out = self.base_check_in + timezone.timedelta(days=3)

    def _create_reservation(self, total=300, paid=0, room_details=None):
        return Reservation.objects.create(
            user=self.user,
            property=self.property,
            check_in=self.base_check_in,
            check_out=self.base_check_out,
            guests=2,
            total=total,
            paid=paid,
            room_details=room_details,
        )

    @patch('notification.tasks.send_notification.delay')
    @patch('booking.utils.distribute_availability_after_booking.delay')
    @patch('booking.utils.RoomBlock.objects.create')
    def test_creates_precheckin_and_side_effects(self, mock_block_create, mock_dist_delay, mock_notify_delay):
        reservation = self._create_reservation(room_details=[{'r':1},{'r':2}])
        pc = trigger_precheckin_and_block(reservation)
        self.assertIsNotNone(pc)
        self.assertEqual(pc.number_of_rooms, 2)  # len(list)
        self.assertEqual(pc.stay_duration, (reservation.check_out - reservation.check_in).days)
        self.assertEqual(pc.payment_status, 'unpaid')
        mock_block_create.assert_called_once()
        mock_dist_delay.assert_called_once_with(str(reservation.id))
        mock_notify_delay.assert_called_once()

    @patch('notification.tasks.send_notification.delay')
    @patch('booking.utils.distribute_availability_after_booking.delay')
    @patch('booking.utils.RoomBlock.objects.create')
    def test_payment_status_partial_and_completed(self, mock_block_create, mock_dist_delay, mock_notify_delay):
        # Partial
        reservation_partial = self._create_reservation(total=100, paid=40)
        pc_partial = trigger_precheckin_and_block(reservation_partial)
        self.assertEqual(pc_partial.payment_status, 'partial')
        # Completed
        reservation_completed = self._create_reservation(total=100, paid=150)
        pc_completed = trigger_precheckin_and_block(reservation_completed)
        self.assertEqual(pc_completed.payment_status, 'completed')

    @patch('notification.tasks.send_notification.delay')
    @patch('booking.utils.distribute_availability_after_booking.delay')
    @patch('booking.utils.RoomBlock.objects.create')
    def test_idempotent_returns_existing(self, mock_block_create, mock_dist_delay, mock_notify_delay):
        reservation = self._create_reservation(room_details={'rooms': [], 'mappings': []})
        pc_first = trigger_precheckin_and_block(reservation)
        self.assertIsNotNone(pc_first)
        first_id = pc_first.id
        # Ensure side effects happened on first invocation
        mock_block_create.assert_called_once()
        mock_dist_delay.assert_called_once()
        mock_notify_delay.assert_called_once()
        # Reset mocks to detect second-call side effects
        mock_block_create.reset_mock()
        mock_dist_delay.reset_mock()
        mock_notify_delay.reset_mock()
        pc_second = trigger_precheckin_and_block(reservation)        
        self.assertEqual(first_id, pc_second.id)
        mock_block_create.assert_not_called()
        mock_dist_delay.assert_not_called()
        mock_notify_delay.assert_not_called()

    @patch('notification.tasks.send_notification.delay', side_effect=Exception('notify boom'))
    @patch('booking.utils.distribute_availability_after_booking.delay', side_effect=Exception('dist boom'))
    @patch('booking.utils.RoomBlock.objects.create', side_effect=Exception('block boom'))
    def test_exceptions_do_not_prevent_return(self, mock_block_create, mock_dist_delay, mock_notify_delay):
        reservation = self._create_reservation()
        pc = trigger_precheckin_and_block(reservation)
        self.assertIsNotNone(pc)
        # Even with failures, payment status still determined
        self.assertEqual(pc.payment_status, 'unpaid')
