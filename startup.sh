#!/bin/bash

if [ -z "$1" ]; then
  echo "Usage: $0 [dev|test|prod]"
  exit 1
fi

case "$1" in
  dev)
    echo "Executing development commands..."
    if grep -q '^DJANGO_ENV=' .env; then
    sed -i "" -e "s/^DJANGO_ENV=.*/DJANGO_ENV='dev'/" .env
    else
    echo "DJANGO_ENV='dev'" >> .env
    fi \
    & cd nestafar && pytest || true \
    && cd .. &&  source venv/bin/activate && cd nestafar && python3 manage.py makemigrations && python3 manage.py migrate && python3 manage.py runserver \
    & redis-server \
    & cd nestafar && celery -A nestafar worker -l info 
    # export DISPLAY=10:10 \
    # && gnome-terminal -- bash -c "redis-server; exec bash" \
    # && gnome-terminal -- bash -c "cd nestafar && celery -A nestafar worker -l info; exec bash" \

    ;;
  test)
    echo "Executing test commands..."
    if grep -q '^DJANGO_ENV=' .env; then
    sed -i "" -e "s/^DJANGO_ENV=.*/DJANGO_ENV='test'/" .env
    else
    echo "DJANGO_ENV='test'" >> .env
    fi \
    && docker-compose build && docker-compose up
    echo  "docker exec -it <container> python manage.py createsuperuser to create super user"
    ;;
  prod)
    echo "Executing production commands..."
    if grep -q '^DJANGO_ENV=' .env; then
    sed -i "" -e "s/^DJANGO_ENV=.*/DJANGO_ENV='prod'/" .env
    else
    echo "DJANGO_ENV='prod'" >> .env
    fi \
    & cd nestafar && pytest || true \
    && cd .. && docker build . -t nestafar && docker run -p 8000:8000 nestafar
    ;;
  deploy)
    echo "Executing production commands..."
    if grep -q '^DJANGO_ENV=' .env; then
    sed -i "" -e "s/^DJANGO_ENV=.*/DJANGO_ENV='prod'/" .env
    else
    echo "DJANGO_ENV='prod'" >> .env
    fi \
    & cd nestafar && pytest \
    && cd .. && docker buildx build --platform linux/amd64 -t gcr.io/nestafar-2023/functions/nestafar-backend:latest . \
    && docker push gcr.io/nestafar-2023/functions/nestafar-backend:latest \
    && gcloud run deploy nestafar-backend --image gcr.io/nestafar-2023/functions/nestafar-backend:latest --allow-unauthenticated --port=8000 --region=asia-south1
    ;;
  *)
    echo "Invalid command: $1. Use [dev|test|prod|deploy]."
    exit 1
    ;;
esac

exit 0
