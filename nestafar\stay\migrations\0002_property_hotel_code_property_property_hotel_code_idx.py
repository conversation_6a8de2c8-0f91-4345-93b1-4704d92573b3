# Generated by Django 4.2.7 on 2025-08-04 17:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('stay', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='hotel_code',
            field=models.CharField(blank=True, help_text='Hotel/Property code in external system', max_length=100, null=True),
        ),
        migrations.AddIndex(
            model_name='property',
            index=models.Index(fields=['hotel_code'], name='property_hotel_code_idx'),
        ),
    ]
