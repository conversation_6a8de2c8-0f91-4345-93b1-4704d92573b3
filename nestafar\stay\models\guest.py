import uuid
from django.db import models
from .room import Room
from core.models import User

class Guest(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="guest")
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name="guest")
    group_id = models.CharField(max_length=100, blank=True, null=True)
    checkin_key = models.CharField(max_length=100)
    checked_in = models.BooleanField(default=False)
    check_in_date = models.DateTimeField(blank=True, null=True)
    checked_out = models.BooleanField(default=False)
    check_out_date = models.DateTimeField(blank=True, null=True)
    total_orders = models.IntegerField(default=0, blank=True, null=True)
    total_spends = models.FloatField(default=0, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTime<PERSON>ield(auto_now=True)

    def __str__(self):
        # Handle cases where room filed is not available
        property_name = getattr(self.room, 'property.name', '') if self.room else ''

        return f"{self.user.name}{'-' if property_name else ''}{property_name}"
