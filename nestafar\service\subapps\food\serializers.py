from .models import (FoodService, FoodServiceItem, FoodOrder, FoodOrderItem,
                     FoodCart, FoodCartItems)
from rest_framework.serializers import (ModelSerializer, SerializerMethodField, ValidationError)

from ...models import ServicePartner


class FoodServiceListSerializer(ModelSerializer):
    n_items = SerializerMethodField(required=False)

    def get_n_items(self, obj):
        return obj.service_items.count()

    class Meta:
        model = FoodService
        fields = '__all__'


class FoodServiceItemListSerializer(ModelSerializer):
    class Meta:
        model = FoodServiceItem
        fields = '__all__'


class FoodServiceItemCardSerializer(FoodServiceItemListSerializer):
    class Meta:
        model = FoodServiceItem
        fields = ['name', 'description', 'price', 'addon', 'is_active']


class FoodServiceItemCreateSerializer(FoodServiceItemListSerializer):
    def validate(self, attrs):
        addon = attrs.get('addon')
        if addon:
            for addon_item in addon.keys():
                try:
                    addon_price = float(addon[addon_item])
                    addon[addon_item] = addon_price
                except ValueError:
                    raise ValidationError("Invalid price for addon")
        else:
            attrs['addon'] = None
        return attrs


class FoodOrderItemCardSerializer(ModelSerializer):
    item = FoodServiceItemCardSerializer()

    class Meta:
        model = FoodOrderItem
        fields = ['item', 'quantity', 'add_ons']


class FoodCartItemSerializer(ModelSerializer):
    name = SerializerMethodField(required=False)

    def get_name(self, obj):
        return obj.name if obj.name else obj.item.name

    class Meta:
        model = FoodCartItems
        fields = ['id', 'name', 'item', 'price', 'quantity', 'add_ons', 'ordered', 'updated_at', 'created_at']


class FoodCartItemListSerializer(ModelSerializer):
    name = SerializerMethodField()

    def get_name(self, obj):
        return obj.item.name

    class Meta:
        model = FoodCartItems
        fields = '__all__'


class FoodCartSerializer(ModelSerializer):
    cart_items = FoodCartItemSerializer(many=True)

    class Meta:
        model = FoodCart
        fields = '__all__'


class FoodCartGetSerializer(ModelSerializer):
    cart_items = FoodCartItemListSerializer(many=True)
    status = SerializerMethodField()

    def get_status(self, obj):
        return FoodCart.CartStatus(obj.status).name

    class Meta:
        model = FoodCart
        fields = '__all__'


class ServicePartnerNameSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = ["id", "name", "type_of_service", "phone_number"]


class FoodOrderSerializer(ModelSerializer):
    order_items = FoodOrderItemCardSerializer(many=True)
    guest = SerializerMethodField(required=False)
    service_partner = ServicePartnerNameSerializer()

    def get_guest(self, obj):
        return {'id': obj.guest.id,
                'phone_no': obj.guest.user.phone.as_e164,
                'room_no': obj.guest.room.room_no,
                'name': obj.guest.user.name
                }

    class Meta:
        model = FoodOrder
        fields = '__all__'
