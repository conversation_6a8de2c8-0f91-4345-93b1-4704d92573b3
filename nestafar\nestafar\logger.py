import logging
import os
import uuid
from django.utils import timezone
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings

# Ensure the logs directory exists (handlers defined in settings will write here)
LOGS_DIR = os.path.join(settings.BASE_DIR, 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

class Logger:
    """Backward-compatible shim kept for existing imports.

    Real handler wiring now lives in Django's LOGGING dict (settings.LOGGING).
    We only emit a one-time debug message to confirm initialization to avoid
    the previous flood of 'Celery logger initialized' duplicates.
    """

    _initialized = False

    def __init__(self):  # noqa: D401
        if Logger._initialized:
            return
        Logger._initialized = True
        logging.getLogger('metrics').debug('Metrics logger ready')
        logging.getLogger('audit').debug('Audit logger ready')
        logging.getLogger('error').debug('Error logger ready')
        logging.getLogger('celery').debug('Celery logger ready')


class DjangoRequestLogger(MiddlewareMixin):
    metric_logger = logging.getLogger('metrics')
    audit_logger = logging.getLogger('audit')
    error_logger = logging.getLogger('error')

    _SENSITIVE_HEADER_KEYS = {
        'authorization', 'cookie', 'set-cookie', 'x-api-key', 'proxy-authorization'
    }

    def _redact_headers(self, headers: dict) -> dict:
        """Return a copy of headers with sensitive values redacted.

        Args:
            headers: mapping of header names to values (request.headers / response.headers)
        """
        redacted = {}
        for k, v in headers.items():
            if k.lower() in self._SENSITIVE_HEADER_KEYS:
                redacted[k] = '<redacted>'
            else:
                # Limit very long header values to prevent log bloat
                if isinstance(v, str) and len(v) > 500:
                    redacted[k] = v[:500] + '…'
                else:
                    redacted[k] = v
        return redacted

    def process_request(self, request):
        curr_time = timezone.now()
        request._request_id = str(uuid.uuid4())
        request.start_time = curr_time
        try:
            self.audit_logger.info({
                'path': request.path_info,
                'time': curr_time.isoformat(),
                'request_id': request._request_id,
                'data': self.get_request_information(request)
            })
        except Exception:
            pass
        return None
    
    def process_response(self, request, response):
        curr_time = timezone.now()
        response_time = curr_time - getattr(request, 'start_time', curr_time)
        duration_ms = int(response_time.total_seconds() * 1000)
        try:
            self.audit_logger.info({
                'path': request.path_info,
                'time': curr_time.isoformat(),
                'request_id': getattr(request, '_request_id', ''),
                'data': self.get_response_information(response),
            })
            self.metric_logger.info({
                'path': request.path_info,
                'time': curr_time.isoformat(),
                'request_id': getattr(request, '_request_id', ''),
                'duration_ms': duration_ms,
            })
        except Exception:
            pass
        return response
    
    def process_exception(self, request, exception):
        curr_time = timezone.now()
        # Log exceptions to the dedicated error logger so they land in error.log
        request_id = getattr(request, '_request_id', str(uuid.uuid4()))
        self.error_logger.error({
            'path': getattr(request, 'path_info', ''),
            'time': curr_time.isoformat(),
            'request_id': request_id,
            'data': self.get_request_information(request),
            'exception': repr(exception)
        }, exc_info=True)
        return None  # Pass control to the next middleware or view function

    def get_request_information(self, request):
        request_information = dict()
        request_information['path'] = request.path_info
        raw_headers = getattr(request, 'headers', None)
        if raw_headers is None:
            # Build from META fallback (Django WSGIRequest exposes headers via META keys)
            meta = getattr(request, 'META', {}) or {}
            built = {}
            for k, v in meta.items():
                # Only include HTTP_*, CONTENT_TYPE, CONTENT_LENGTH
                if k.startswith('HTTP_'):
                    header_name = k[5:].replace('_', '-').title()
                    built[header_name] = v
                elif k in ('CONTENT_TYPE', 'CONTENT_LENGTH'):
                    header_name = k.replace('_', '-').title()
                    built[header_name] = v
            raw_headers = built
        # Redact with narrow exception handling
        try:
            request_information['headers'] = self._redact_headers(raw_headers)
        except (AttributeError, TypeError):  # Only expected structural issues
            request_information['headers'] = {}
        # Do NOT log raw cookies; replace with placeholder to avoid leaking sessions
        request_information['cookies'] = '<redacted>' if getattr(request, 'COOKIES', None) else None
        request_information['method'] = request.method
        return request_information
    
    def get_response_information(self, response):
        response_information = dict()
        response_information['status_code'] = response.status_code
        response_information['headers'] = self._redact_headers(dict(response.items()))
        return response_information

# Instantiate once so legacy imports trigger readiness side-effect
_logger_setup_singleton = Logger()

# Convenience alias so application code can do: from nestafar.logger import logger
logger = logging.getLogger('error')


class ProjectConsoleFilter(logging.Filter):
    """Filter console output to only project/application loggers.

    Whitelist is driven by environment variable PROJECT_LOG_PREFIXES (comma
    separated). Defaults cover main app packages plus structured loggers.
    Any logger whose name starts with one of the prefixes OR exactly equals a
    listed name passes. This lets you hide Django / third‑party noise while
    still emitting the log statements you intentionally add in project code.
    """

    _default_prefixes = (
        'core', 'service', 'pms', 'booking', 'notification', 'geo', 'stay',
        'nestafar', 'metrics', 'audit', 'error'
    )

    def __init__(self, prefixes: str | None = None):  # type: ignore[override]
        super().__init__()
        raw = prefixes or os.getenv('PROJECT_LOG_PREFIXES')
        if raw:
            self.prefixes = tuple(p.strip() for p in raw.split(',') if p.strip())
        else:
            self.prefixes = self._default_prefixes

    def filter(self, record: logging.LogRecord) -> bool:  # noqa: D401
        name = record.name or ''
        for pref in self.prefixes:
            if name == pref or name.startswith(pref + '.'):
                return True
        return False

__all__ = [
    'Logger',
    'DjangoRequestLogger',
    'logger',
    'ProjectConsoleFilter',
]
