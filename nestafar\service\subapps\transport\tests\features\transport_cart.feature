Scenario Outline: Adding a transport service item to the cart
  Given I am an authenticated user with a transport cart (optional)
  When I add a transport service item "<item_name>" to the cart with quantity "<quantity>"
  Then the response status code is 201
  And the response data contains the added cart item details with pickup & drop off dates, no of periods
  Examples:
    | item_name       | quantity |
    | Test Item        | 1        |
    | Another Item    | 2        |
