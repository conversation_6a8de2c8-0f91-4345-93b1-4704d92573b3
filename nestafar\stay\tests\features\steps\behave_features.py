from stay.tests.features.environment import after_scenario, before_scenario
from stay.tests.features.steps.setup import *
from stay.tests.features.steps.users import *
from stay.tests.features.steps.properties import *
from stay.tests.features.steps.service import *
from stay.tests.features.steps.checkin import *
from stay.tests.features.steps.orders import *
from stay.tests.features.steps.notifications import *
import pytest
from pytest_bdd import scenarios, scenario

pytestmark = pytest.mark.django_db(transaction=True)


class Table(object):
    def __init__(self, headings, rows):
        self.headings = headings
        self.rows = rows

    def __iter__(self):
        return iter(self.rows)

    @staticmethod
    def parse(table_with_headers):
        if not table_with_headers:
            return None
        list_table_rows = list(
            filter(
                lambda x: not x.strip().startswith("#"), table_with_headers.split("\n")
            )
        )
        list_headers = list(
            map(lambda x: x.strip(), str(list_table_rows[0]).strip("|").split("|"))
        )
        table_data = []
        for i in range(1, list_table_rows.__len__()):
            list_temp = list_table_rows[i].strip("|").split("|")
            if list_temp:
                row = {}
                for j in range(len(list_headers)):
                    row[list_headers[j]] = list_temp[j].strip()
                table_data.append(row)
        return Table(list_headers, table_data)


class Context(object):
    auth_client = None
    auth_user = None
    table = None
    scratchpad = {}

    def populate_table_from_str(self, table_with_headers):
        self.table = Table.parse(table_with_headers)


@pytest.fixture
def context():
    context = Context()
    before_scenario(context, None)
    yield context
    after_scenario(context, None)


scenarios("..")
