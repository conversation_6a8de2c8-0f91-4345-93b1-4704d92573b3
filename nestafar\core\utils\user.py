from core.models import User
from django.db import IntegrityError
from django.core.exceptions import ValidationError

def get_or_create_user(name, phone, email=None):
    """
    Creates a new user with a generated username and password.

    Args:
        name (str): Name of the user.
        phone (str): Phone number of the user.
        is_partner (bool): Whether the user is a partner. Default is False.
        email (str): User's valid email. Default is None.

    Returns:
        User: The created user instance.
    """
    # Validate inputs
    if not name or len(name.strip()) == 0:
        raise ValidationError("Name cannot be empty.")
    if not phone or len(phone) < 4:
        raise ValidationError("Phone number must be at least 4 digits.")

    # Generate name prefix
    name_prefix = (name[:4] if len(name) >= 4 else name.ljust(4, "0")).upper()
    password = f"{name_prefix}@{phone[:-4]}"

    try:   
        user = User.objects.filter(phone=phone)
        if not user:
             # Create the user
            user = User.objects.create_user(
                phone=phone,
                name=name,
                password=password,
                email=email
            )
        else:
            user = user.first()
            assert not user.is_partner, "Partner cannot be checked in as Guest"
        return user
    except IntegrityError as e:
        raise IntegrityError(f"Failed to create user: {e}")

