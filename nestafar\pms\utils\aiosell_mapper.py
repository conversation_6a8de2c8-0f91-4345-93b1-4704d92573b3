"""
Data mapping utilities for AioSell integration.

This module provides utilities for transforming PMS model data into
AioSell's expected JSON format for both rate restrictions and inventory
restrictions APIs.
"""

import logging
from typing import Dict, Any, List, Tuple, Optional
from datetime import date
from django.utils import timezone
from pms.models import HotelOTAIntegration

from ..models import Calendar, RoomType, RatePlan, RoomBlock
from stay.models import Property, Room

logger = logging.getLogger(__name__)


class AioSellBatchMappingError(Exception):
    """Raised when batch calendar mapping is in strict mode and entries are skipped."""
    def __init__(self, skipped_entries: List[Any]):
        self.skipped_entries = skipped_entries
        super().__init__(
            f"Skipped {len(skipped_entries)} calendar entries due to missing room_type or rate_plan: "
            f"{[getattr(e, 'id', str(e)) for e in skipped_entries]}"
        )


class AioSellDataMapper:
    """
    Data mapper for converting PMS data to AioSell API format.
    
    This class handles the transformation of PMS model data into the JSON
    formats expected by AioSell's rate restrictions and inventory restrictions APIs.
    """
    
    def __init__(self, hotel: Property):
        """
        Initialize the data mapper with hotel configuration.
        
        Args:
            hotel: Property instance
        """
        self.hotel = hotel
        # Prefer active HotelOTAIntegration config first
        self.config: Dict[str, Any] = {}
        integration_config: Optional[Dict[str, Any]] = None
        self.integration: Optional[HotelOTAIntegration] = None
        try:
            integration = HotelOTAIntegration.objects.get(
                hotel=hotel,
                ota_platform__name='aiosell',
                is_active=True
            )
            self.integration = integration
            candidate = getattr(integration, 'config', None)
            if candidate:  # use only if non-empty/truthy
                integration_config = candidate
        except HotelOTAIntegration.DoesNotExist:
            integration_config = None

        if integration_config:
            self.config = integration_config
        else:
            # Fallback to hotel's channel manager config only if integration config missing/empty
            fallback = hotel.get_channel_manager_config('aiosell') or {}
            if fallback:
                self.config = fallback

        if not self.config:
            logger.warning(f"No AioSell configuration found for hotel {hotel.id}")
    def get_hotel_code(self) -> str:
        """Get the hotel code for AioSell API requests using cached integration if available."""
        if self.integration and getattr(self.integration, 'external_hotel_id', None):
            return self.integration.external_hotel_id
        return self.hotel.get_shortened_id() if hasattr(self.hotel, 'get_shortened_id') else str(self.hotel.id)
    
    def get_target_channels(self) -> List[str]:
        """Get the target channels for distribution."""
        return self.config.get('target_channels', ['agoda', 'booking.com'])
    
    def map_room_to_code(self, room: Room = None, room_type: RoomType = None) -> str:
        """
        Map a room or room type to AioSell room code.
        
        Args:
            room: Room instance (optional)
            room_type: RoomType instance (optional)
            
        Returns:
            AioSell room code string
        """
        room_mapping = self.config.get('room_mapping', {})
        
        # Try room-specific mapping first
        if room and room.room_no:
            mapped_code = room_mapping.get(room.room_no)
            if mapped_code:
                return mapped_code
                
        # Try room type mapping
        if room_type and room_type.name:
            mapped_code = room_mapping.get(room_type.name)
            if mapped_code:
                return mapped_code
                
        # Generate default mapping
        if room and room.room_no:
            return self._sanitize_code(room.room_no)
        elif room_type and room_type.name:
            return self._sanitize_code(room_type.name)
        else:
            logger.error("Unable to determine room code: no room or room_type provided")
            raise ValueError("Cannot map to room code: neither room nor room_type provided")

    def map_rate_plan_to_codes(self, room_code: str, rate_plan: RatePlan = None) -> List[str]:
        """
        Map a rate plan to AioSell rate plan codes.
        
        Args:
            room_code: AioSell room code
            rate_plan: RatePlan instance (optional)
            
        Returns:
            List of rate plan codes
        """
        rate_plans_config = self.config.get('rate_plans', {})
        
        # Try to get configured rate plans for this room
        if room_code in rate_plans_config:
            return rate_plans_config[room_code]
            
        # Generate from rate plan
        if rate_plan:
            rate_plan_code = f"{room_code}-{self._sanitize_code(rate_plan.name)}"
            return [rate_plan_code]
            
        # Default rate plan codes for single and double occupancy
        return [f"{room_code}-S-101", f"{room_code}-D-101"]
    
    def _sanitize_code(self, code: str) -> str:
        """
        Sanitize a code for AioSell API use.
        
        Args:
            code: Raw code string
            
        Returns:
            Sanitized code string
        """
        return code.upper().replace(' ', '_').replace('-', '_')
    
    def map_calendar_to_rate_restrictions(self, calendar_entry: Calendar) -> Dict[str, Any]:
        """
        Map a calendar entry to AioSell rate restrictions format.
        
        Args:
            calendar_entry: Calendar instance
            
        Returns:
            Rate restrictions payload for AioSell API
        """
        room_code = self.map_room_to_code(room_type=calendar_entry.room_type)
        rate_plan_codes = self.map_rate_plan_to_codes(room_code, calendar_entry.rate_plan)

        entry_restrictions = calendar_entry.restrictions or {}
        aiosell_restrictions = {
            "stopSell": entry_restrictions.get('stopSell', False),
            "exactStayArrival": entry_restrictions.get('exactStayArrival'),
            "maximumStayArrival": entry_restrictions.get('maximumStayArrival'),
            "minimumAdvanceReservation": entry_restrictions.get('minimumAdvanceReservation'),
            "minimumStay": entry_restrictions.get('minimumStay', 1),
            "closeOnArrival": entry_restrictions.get('closeOnArrival', False),
            "maximumStay": entry_restrictions.get('maximumStay'),
            "maximumAdvanceReservation": entry_restrictions.get('maximumAdvanceReservation'),
            "closeOnDeparture": entry_restrictions.get('closeOnDeparture', False)
        }
        aiosell_restrictions = {k: v for k, v in aiosell_restrictions.items() if v is not None}

        rates = [
            {
                "roomCode": room_code,
                "rateplanCode": rp_code,
                "restrictions": aiosell_restrictions
            }
            for rp_code in rate_plan_codes
        ]

        return {
            "hotelCode": self.get_hotel_code(),
            "toChannels": self.get_target_channels(),
            "updates": [
                {
                    "startDate": calendar_entry.date.isoformat(),
                    "endDate": calendar_entry.date.isoformat(),
                    "rates": rates,
                }
            ],
        }
    
    def map_calendar_batch_to_rate_restrictions(self,
                                               calendar_entries: List[Calendar],
                                               strict: bool = False):
        """
        Map multiple calendar entries to AioSell rate restrictions format.
        
        This groups entries by date range and room/rate plan combinations for efficiency.
        
        Args:
            calendar_entries: List of Calendar instances
            strict: If True, raise AioSellBatchMappingError when any entries are skipped.
            
        Returns:
            If strict is False (default): (payload: dict, skipped_entries: list)
            If strict is True and no skips: payload dict
            If strict is True and skips occur: raises AioSellBatchMappingError
        """
        if not calendar_entries:
            return ({}, []) if not strict else {}
        
        # Group entries by room type and rate plan
        grouped_entries = {}
        skipped: List[Calendar] = []
        for entry in calendar_entries:
            if not entry.room_type or not entry.rate_plan:
                logger.warning(
                    "Skipping calendar entry %s: missing room_type or rate_plan", getattr(entry, 'id', '<no-id>')
                )
                skipped.append(entry)
                continue
            key = (entry.room_type.id, entry.rate_plan.id)
            if key not in grouped_entries:
                grouped_entries[key] = []
            grouped_entries[key].append(entry)
        
        # Build updates for each group
        updates = []
        
        for (room_type_id, rate_plan_id), entries in grouped_entries.items():
            # Sort entries by date
            entries.sort(key=lambda e: e.date)
            
            # Find date ranges
            dates = [entry.date for entry in entries]
            start_date = min(dates)
            end_date = max(dates)
            
            # Use first entry for room/rate plan info and restrictions
            first_entry = entries[0]
            room_code = self.map_room_to_code(room_type=first_entry.room_type)
            rate_plan_codes = self.map_rate_plan_to_codes(room_code, first_entry.rate_plan)
            
            # Use restrictions from first entry (assume similar restrictions for the group)
            entry_restrictions = first_entry.restrictions or {}
            
            aiosell_restrictions = {
                "stopSell": entry_restrictions.get('stopSell', False),
                "exactStayArrival": entry_restrictions.get('exactStayArrival'),
                "maximumStayArrival": entry_restrictions.get('maximumStayArrival'),
                "minimumAdvanceReservation": entry_restrictions.get('minimumAdvanceReservation'),
                "minimumStay": entry_restrictions.get('minimumStay', 1),
                "closeOnArrival": entry_restrictions.get('closeOnArrival', False),
                "maximumStay": entry_restrictions.get('maximumStay'),
                "maximumAdvanceReservation": entry_restrictions.get('maximumAdvanceReservation'),
                "closeOnDeparture": entry_restrictions.get('closeOnDeparture', False)
            }
            aiosell_restrictions = {k: v for k, v in aiosell_restrictions.items() if v is not None}
            
            # Build rates array
            rates = []
            for rate_plan_code in rate_plan_codes:
                rates.append({
                    "roomCode": room_code,
                    "rateplanCode": rate_plan_code,
                    "restrictions": aiosell_restrictions
                })
            
            # Add update for this group
            updates.append({
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),
                "rates": rates
            })
        
        # Build complete payload
        payload = {
            "hotelCode": self.get_hotel_code(),
            "toChannels": self.get_target_channels(),
            "updates": updates
        }
        
        if skipped and strict:
            raise AioSellBatchMappingError(skipped)
        return (payload, skipped) if not strict else payload
    
    def map_room_block_to_inventory_restrictions(self, 
                                                room_block: RoomBlock,
                                                action: str = 'create') -> Dict[str, Any]:
        """
        Map a room block to AioSell inventory restrictions format.
        
        Args:
            room_block: RoomBlock instance
            action: Action type ('create', 'update', 'delete')
            
        Returns:
            Inventory restrictions payload for AioSell API
        """
        if not room_block.room:
            raise ValueError("Room block must have an associated room")
        
        # Resolve RoomType robustly instead of arbitrarily picking the first
        resolved_room_type: Optional[RoomType] = None

        room_obj = room_block.room

        # 1. Direct FK style attributes (commonly room.room_type or room.type)
        for attr_name in ('room_type', 'type', 'type_of_room'):
            if resolved_room_type:
                break
            if hasattr(room_obj, attr_name):
                candidate = getattr(room_obj, attr_name)
                # If it's already a RoomType instance accept it
                if isinstance(candidate, RoomType):
                    resolved_room_type = candidate
                # If it's a string identifier, try name/code lookup
                elif isinstance(candidate, str) and candidate.strip():
                    matches = RoomType.objects.filter(hotel=self.hotel, name__iexact=candidate.strip())
                    if matches.count() == 1:
                        resolved_room_type = matches.first()
                    elif matches.count() > 1:
                        logger.error(
                            "Ambiguous room type resolution for room %s identifier '%s' (multiple matches)",
                            room_obj.id, candidate
                        )
                        raise ValueError("Ambiguous room type: multiple matches for identifier '%s'" % candidate)

        # 2. Try mapping via configured room_mapping if still unresolved
        if not resolved_room_type:
            mapping = (self.config or {}).get('room_mapping', {})
            identifier = None
            if isinstance(mapping, dict):
                # Accept room_no or provided attribute for lookup
                identifier = mapping.get(getattr(room_obj, 'room_no', None)) or mapping.get(str(room_obj.id))
            if identifier:
                matches = RoomType.objects.filter(hotel=self.hotel, name__iexact=str(identifier))
                if matches.count() == 1:
                    resolved_room_type = matches.first()
                elif matches.count() > 1:
                    logger.error(
                        "Ambiguous room type resolution via mapping for room %s identifier '%s'", room_obj.id, identifier
                    )
                    raise ValueError("Ambiguous room type mapping for identifier '%s'" % identifier)

        # 3. Final attempt: unique name match using room.type_of_room textual value
        if not resolved_room_type and hasattr(room_obj, 'type_of_room') and isinstance(room_obj.type_of_room, str):
            candidate = room_obj.type_of_room.strip()
            if candidate:
                matches = RoomType.objects.filter(hotel=self.hotel, name__iexact=candidate)
                if matches.count() == 1:
                    resolved_room_type = matches.first()
                elif matches.count() > 1:
                    logger.error(
                        "Ambiguous room type resolution for room %s textual type '%s'", room_obj.id, candidate
                    )
                    raise ValueError("Ambiguous room type textual identifier '%s'" % candidate)

        if not resolved_room_type:
            raise ValueError(
                "Unable to resolve RoomType for room %s. Provide explicit mapping or ensure a unique name." % room_obj.id
            )

        room_type = resolved_room_type
        
        room_code = self.map_room_to_code(room=room_block.room, room_type=room_type)
        
        # Convert datetime to date
        if timezone.is_aware(room_block.blocked_from):
            start_date = timezone.localtime(room_block.blocked_from).date()
        else:
            start_date = room_block.blocked_from.date()

        if room_block.blocked_until:
            if timezone.is_aware(room_block.blocked_until):
                end_date = timezone.localtime(room_block.blocked_until).date()
            else:
                end_date = room_block.blocked_until.date()
        else:
            end_date = start_date
        
        # Determine stop sell based on action and block status
        stop_sell = True
        if action == 'delete' or not room_block.is_active:
            stop_sell = False
        
        # Build restrictions
        restrictions = {
            "stopSell": stop_sell,
            "exactStayArrival": None,
            "maximumStayArrival": None,
            "minimumAdvanceReservation": None,
            "minimumStay": 1,
            "closeOnArrival": False,
            "minimumStayArrival": None,
            "maximumStay": None,
            "maximumAdvanceReservation": None,
            "closeOnDeparture": False
        }
        
        # Build complete payload
        payload = {
            "hotelCode": self.get_hotel_code(),
            "toChannels": self.get_target_channels(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rooms": [
                        {
                            "roomCode": room_code,
                            "restrictions": restrictions
                        }
                    ]
                }
            ]
        }
        
        return payload
    
    def map_custom_restrictions(self,
                               room_type: RoomType,
                               rate_plan: RatePlan = None,
                               start_date: date = None,
                               end_date: date = None,
                               restrictions: Dict[str, Any] = None,
                               api_type: str = 'rate') -> Dict[str, Any]:
        """
        Map custom restrictions to AioSell API format.
        
        Args:
            room_type: RoomType instance
            rate_plan: RatePlan instance (required for rate restrictions)
            start_date: Start date for restrictions
            end_date: End date for restrictions
            restrictions: Custom restrictions dictionary
            api_type: API type ('rate' or 'inventory')
            
        Returns:
            API payload for AioSell
        """
        if not start_date:
            start_date = timezone.now().date()
        if not end_date:
            end_date = start_date
        if not restrictions:
            restrictions = {}
        
        room_code = self.map_room_to_code(room_type=room_type)
        
        # Map restrictions to AioSell format
        aiosell_restrictions = {
            "stopSell": restrictions.get('stop_sell', False),
            "exactStayArrival": restrictions.get('exact_stay_arrival'),
            "maximumStayArrival": restrictions.get('maximum_stay_arrival'),
            "minimumAdvanceReservation": restrictions.get('minimum_advance_reservation'),
            "minimumStay": restrictions.get('minimum_stay', 1),
            "closeOnArrival": restrictions.get('close_on_arrival', False),
            "maximumStay": restrictions.get('maximum_stay'),
            "maximumAdvanceReservation": restrictions.get('maximum_advance_reservation'),
            "closeOnDeparture": restrictions.get('close_on_departure', False),
        }
        aiosell_restrictions = {k: v for k, v in aiosell_restrictions.items() if v is not None}

        if api_type == 'rate':
            if not rate_plan:
                raise ValueError("Rate plan is required for rate restrictions")
            rate_plan_codes = self.map_rate_plan_to_codes(room_code, rate_plan)
            rates = [
                {
                    "roomCode": room_code,
                    "rateplanCode": rp_code,
                    "restrictions": aiosell_restrictions,
                }
                for rp_code in rate_plan_codes
            ]
            return {
                "hotelCode": self.get_hotel_code(),
                "toChannels": self.get_target_channels(),
                "updates": [
                    {
                        "startDate": start_date.isoformat(),
                        "endDate": end_date.isoformat(),
                        "rates": rates,
                    }
                ],
            }
        # inventory restrictions branch
        aiosell_restrictions["minimumStayArrival"] = restrictions.get('minimum_stay_arrival')
        aiosell_restrictions = {k: v for k, v in aiosell_restrictions.items() if v is not None}
        return {
            "hotelCode": self.get_hotel_code(),
            "toChannels": self.get_target_channels(),
            "updates": [
                {
                    "startDate": start_date.isoformat(),
                    "endDate": end_date.isoformat(),
                    "rooms": [
                        {
                            "roomCode": room_code,
                            "restrictions": aiosell_restrictions,
                        }
                    ],
                }
            ],
        }
    
    def validate_payload(self, payload: Dict[str, Any], api_type: str = 'rate') -> Tuple[bool, List[str]]:
        """
        Validate an AioSell API payload.
        
        Args:
            payload: API payload dictionary
            api_type: API type ('rate' or 'inventory')
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Check required top-level fields
        required_fields = ['hotelCode', 'toChannels', 'updates']
        for field in required_fields:
            if field not in payload:
                errors.append(f"Missing required field: {field}")
        
        if 'updates' in payload:
            if not isinstance(payload['updates'], list):
                errors.append("'updates' must be a list")
            else:
                for i, update in enumerate(payload['updates']):
                    if not isinstance(update, dict):
                        errors.append(f"Update {i} must be a dictionary")
                        continue
                    
                    # Check required update fields
                    update_required = ['startDate', 'endDate']
                    if api_type == 'rate':
                        update_required.append('rates')
                    else:
                        update_required.append('rooms')
                    
                    for field in update_required:
                        if field not in update:
                            errors.append(f"Update {i}: Missing required field '{field}'")
                    
                    # Validate date format
                    for date_field in ['startDate', 'endDate']:
                        if date_field in update:
                            try:
                                date.fromisoformat(update[date_field])
                            except ValueError:
                                errors.append(f"Update {i}: Invalid date format for '{date_field}'")
                    
                    # Validate rates/rooms structure
                    if api_type == 'rate' and 'rates' in update:
                        if not isinstance(update['rates'], list):
                            errors.append(f"Update {i}: 'rates' must be a list")
                        else:
                            for j, rate in enumerate(update['rates']):
                                rate_required = ['roomCode', 'rateplanCode', 'restrictions']
                                for field in rate_required:
                                    if field not in rate:
                                        errors.append(f"Update {i}, Rate {j}: Missing required field '{field}'")
                    
                    elif api_type == 'inventory' and 'rooms' in update:
                        if not isinstance(update['rooms'], list):
                            errors.append(f"Update {i}: 'rooms' must be a list")
                        else:
                            for j, room in enumerate(update['rooms']):
                                room_required = ['roomCode', 'restrictions']
                                for field in room_required:
                                    if field not in room:
                                        errors.append(f"Update {i}, Room {j}: Missing required field '{field}'")
        
        return len(errors) == 0, errors
    
    def get_sample_rate_restrictions_payload(self) -> Dict[str, Any]:
        """
        Get a sample rate restrictions payload for testing.
        
        Returns:
            Sample payload dictionary
        """
        return {
            "hotelCode": self.get_hotel_code(),
            "toChannels": self.get_target_channels(),
            "updates": [
                {
                    "startDate": "2023-02-22",
                    "endDate": "2023-02-24",
                    "rates": [
                        {
                            "roomCode": "EXECUTIVE",
                            "rateplanCode": "EXECUTIVE-S-101",
                            "restrictions": {
                                "stopSell": False,
                                "exactStayArrival": None,
                                "maximumStayArrival": None,
                                "minimumAdvanceReservation": None,
                                "minimumStay": 1,
                                "closeOnArrival": False,
                                "maximumStay": None,
                                "maximumAdvanceReservation": None,
                                "closeOnDeparture": False
                            }
                        }
                    ]
                }
            ]
        }
    
    def get_sample_inventory_restrictions_payload(self) -> Dict[str, Any]:
        """
        Get a sample inventory restrictions payload for testing.
        
        Returns:
            Sample payload dictionary
        """
        return {
            "hotelCode": self.get_hotel_code(),
            "toChannels": self.get_target_channels(),
            "updates": [
                {
                    "startDate": "2023-01-24",
                    "endDate": "2023-01-26",
                    "rooms": [
                        {
                            "roomCode": "SUITE",
                            "restrictions": {
                                "stopSell": False,
                                "exactStayArrival": None,
                                "maximumStayArrival": None,
                                "minimumAdvanceReservation": None,
                                "minimumStay": 1,
                                "closeOnArrival": False,
                                "minimumStayArrival": None,
                                "maximumStay": None,
                                "maximumAdvanceReservation": None,
                                "closeOnDeparture": False
                            }
                        }
                    ]
                }
            ]
        }


def get_data_mapper(hotel: Property) -> AioSellDataMapper:
    """
    Get a data mapper instance for a hotel.
    
    Args:
        hotel: Property instance
        
    Returns:
        AioSellDataMapper instance
    """
    return AioSellDataMapper(hotel)
