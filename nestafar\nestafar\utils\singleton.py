import functools
import threading

lock=threading.Lock()

def synchronized(_lock):
    """ Synchronization decorator """

    def wrapper(f):
        @functools.wraps(f)
        def inner_wrapper(*args, **kw):
            with _lock:
                return f(*args, **kw)
        return inner_wrapper
    return wrapper

class Singleton(object):

    _instances = {}

    @synchronized(lock)
    def __new__(cls, *args, **kwargs):
        if cls not in cls._instances:
            print("Creating new instance of class "+ cls.__name__)
            cls._instances[cls]=super(Singleton, cls).__new__(
                cls, *args, **kwargs)
        return cls._instances[cls]