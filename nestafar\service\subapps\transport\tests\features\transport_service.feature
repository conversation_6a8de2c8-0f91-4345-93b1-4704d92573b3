Scenario Outline: Retrieving the list of transport services
  Given I exist as a user
  When I retrieve the list of transport services
  Then the response status code is 200
  And the response data contains a list of transport services with basic details

Scenario Outline: Retrieving the details of a specific transport service
  Given a transport service exists with name "<service_name>"
  When I retrieve the details of the transport service
  Then the response status code is 200
  And the response data contains the transport service details (name, partner, type)

Scenario Outline: Filtering transport services (Optional)
  Given I exist as a user
  When I retrieve the list of transport services filtered by "<filter_field>" with value "<filter_value>" (Optional)
  Then the response status code is 200
  And the response data contains a filtered list of transport services
  Examples:
    | filter_field  | filter_value |
    | price (lt)     | 100.00       |
    | is_active     | True          |
