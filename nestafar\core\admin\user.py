from django.contrib import admin
from django.contrib.auth.models import Group
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from ..models import User, PartnerProfile, UserProfile, Role, Permission, UserRole
from .forms import *

class UserProfileInline(admin.TabularInline):
    model = UserProfile
    extra = 0

class PartnerProfileInline(admin.TabularInline):
    model = PartnerProfile
    extra = 0

class UserRoleInline(admin.TabularInline):
    model = UserRole
    extra = 1

@admin.register(User)
class UserAdmin(BaseUserAdmin):
    form = UserChangeForm
    add_form = UserCreationForm

    list_display = ["name", "email", "phone", "is_admin", "is_partner", "updated_at"]
    list_filter = ["is_admin", "is_active", "is_partner", "updated_at"]
    
    # Search by phone number, name, and email
    search_fields = ["phone", "name", "email"]
    
    ordering = ["-updated_at"]

    fieldsets = [
        (None, {"fields": ["phone", "password"]}),
        ("Personal info", {"fields": ["name", "email"]}),
        ("Permissions", {"fields": ["is_admin", "is_active", "is_partner"]}),
        ("Important dates", {"fields": ["created_at", "updated_at"]}),
    ]

    add_fieldsets = [
        (None, {
            "classes": ["wide"],
            "fields": ["name", "phone", "email", "password1", "password2", "is_partner"],
        }),
    ]

    readonly_fields = ["created_at", "updated_at"]
    inlines = [UserProfileInline, PartnerProfileInline]

    def get_inlines(self, request, obj=None):
        if obj and obj.is_partner:
            return [UserProfileInline, PartnerProfileInline, UserRoleInline]
        return [UserProfileInline, UserRoleInline]

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ["name", "description"]
    search_fields = ["name", "description"]
    filter_horizontal = ["permissions"]

@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ["name", "description"]
    search_fields = ["name", "description"]

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ["user", "role", "property_id"]
    list_filter = ["role"]
    search_fields = ["user__name", "user__email", "role__name"]
admin.site.unregister(Group)