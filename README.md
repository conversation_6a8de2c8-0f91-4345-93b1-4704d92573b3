# NESTAFAR DJANGO BACKEND

---

## Project Overview

This project is a Django-based application designed for managing locations, reservations, and other related services. It integrates external APIs (e.g.OpenAI API, Google Places API, Razerpay) and utilizes Redis for caching, Celery for task scheduling, and supports multiple database configurations, including SQLite for local development and CockroachDB for production.

---

## Features

- Location management with dynamic creation using external APIs.
- Redis caching for optimizing performance.
- Celery task queues for handling background tasks and periodic jobs.
- Flexible database configurations for local and production environments.

---

## Prerequisites

### System Requirements

1. **Redis**: Install Redis server for caching.

   ```sh
   sudo apt install redis-server
   ```

2. **Python**: Ensure Python 3.8+ is installed on your system.
3. **Database**: Install CockroachDB for production or use SQLite for local development.

### Local Environment Setup

1. **Virtual Environment**:
   - Create a virtual environment:

     ```sh
     python -m venv venv
     ```

   - Activate the virtual environment:
     - For Linux/Mac:

       ```sh
       source venv/bin/activate
       ```

     - For Windows:

       ```sh
       venv\Scripts\activate
       ```

2. **Environment Variables**:
   - Create a `.env` file in the project root directory and populate it with necessary environment variables (e.g., database credentials, API keys).

3. **Database Configuration**:
   - For local development using SQLite, create a file named `platform/nestafar/nestafar/local_settings.py` and add the following:

     ```python
     from pathlib import Path

     BASE_DIR = Path(__file__).resolve().parent.parent
     DATABASES = {
         'default': {
             'ENGINE': 'django.db.backends.sqlite3',
             'NAME': BASE_DIR / "db.sqlite3",
         }
     }
     ```

   - For CockroachDB (production), set up your database following the [CockroachDB official documentation](https://www.cockroachlabs.com/docs/). Ensure the `.postgresql/` directory is placed in the `platform/nestafar` folder.

4. **Testing Tools**:
   - Install `pytest` for running tests:

     ```sh
     pip install pytest
     ```

   - For Ubuntu:

     ```sh
     sudo apt install pytest
     ```

---

## Setup Instructions

1. **Install Dependencies**:

   - For local setup:

      ```sh
      pip install -r dev.txt
      ```

   - For prod / dev server

      ```sh
      pip install -r requirements.txt
      ```

2. **Database Migrations**:

   ```sh
   python manage.py makemigrations
   python manage.py migrate
   ```

3. **Initial Setup**:

   ```sh
   python manage.py setup
   python manage.py setup_locations
   ```

---

## Running the Application

### Local Environment

To start the local server:

```sh
python manage.py runserver
```

### Development Environment

To start the development server:

```sh
./startup.sh dev
```

### Testing Environment

To run the tests:

```sh
./startup.sh test
```

### Production Environment

To deploy in production:

```sh
./startup.sh prod
```

---

## Celery Setup

### Steps to Run Celery and Celery Beat

1. **Start Redis Server**:

   ```sh
   sudo service redis-server start
   ```

2. **Start Celery Worker**:

   ```sh
   celery -A nestafar worker -l info
   ```

3. **Start Celery Beat**:

   ```sh
   celery -A nestafar beat -l info
   ```

4. **Verify Periodic Tasks**:
   - Access the Django admin interface.
   - Check the `Periodic Tasks` section under `django_celery_beat`.

### Celery Configuration

Ensure your `.env` file includes the following Celery configurations:

```env
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

---

## Caching with Redis

This project uses Redis to cache authentication token and location data fetched from external APIs (e.g., Google Places API). Cached data improves performance by reducing redundant API calls.

### Redis Commands for Management

- **Flush Cache**:

  ```sh
  redis-cli FLUSHALL
  ```

- **Monitor Cache**:

  ```sh
  redis-cli monitor
  ```

---

## Additional Notes

- Ensure proper API key configurations for external services (e.g AWS access credentials, Google Places API)  and the in your `.env` file.
- Regularly test and monitor Celery tasks to ensure smooth background task execution.
- Use CockroachDB for production-grade deployments and SQLite for lightweight local setups.

For detailed documentation, refer to the respective service documentation and follow best practices for deployment and scaling.
