from django.contrib import admin
from .models import ShopService, ShopServiceItem, ShopCart, ShopCartItems, ShopOrder, ShopOrderItem

@admin.register(ShopService)
class ShopServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'partner', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('name', 'partner__name')
    ordering = ('-updated_at',) 

@admin.register(ShopServiceItem)
class ShopServiceItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'price', 'service', 'is_featured', 'category', 'created_at', 'updated_at')
    list_filter = ('service', 'is_featured', 'category', 'created_at', 'updated_at')
    search_fields = ('name', 'service__name', 'category')
    ordering = ('-updated_at',) 

@admin.register(ShopCart)
class ShopCartAdmin(admin.ModelAdmin):
    list_display = ('guest', 'status', 'subtotal', 'taxes', 'charges', 'total', 'created_at', 'updated_at')
    list_filter = ('status', 'created_at', 'updated_at')
    search_fields = ('guest__user__name',)
    ordering = ('-updated_at',) 

@admin.register(ShopCartItems)
class ShopCartItemsAdmin(admin.ModelAdmin):
    list_display = ('name', 'cart', 'item', 'quantity', 'price', 'created_at', 'updated_at')
    list_filter = ('cart', 'item', 'created_at', 'updated_at')
    search_fields = ('name', 'cart__guest__user__name', 'item__name')
    ordering = ('-updated_at',) 

@admin.register(ShopOrder)
class ShopOrderAdmin(admin.ModelAdmin):
    list_display = ('guest', 'service', 'cart', 'status', 'subtotal', 'commissions', 'taxes', 'charges', 'total', 'created_at', 'updated_at')
    list_filter = ('status', 'service', 'guest', 'created_at', 'updated_at')
    search_fields = ('guest__user__name', 'service__name')
    ordering = ('-updated_at',) 

@admin.register(ShopOrderItem)
class ShopOrderItemAdmin(admin.ModelAdmin):
    list_display = ('item', 'order', 'quantity', 'price', 'created_at', 'updated_at')
    list_filter = ('order', 'item', 'created_at', 'updated_at')
    search_fields = ('item__name', 'order__guest__user__name', 'order__service__name')
    ordering = ('-updated_at',) 
