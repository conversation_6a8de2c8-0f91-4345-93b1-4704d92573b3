Feature: Notifications
    Scenario: Notifications E2E
        Given Setup User
            | name | phone      | password | partner |
            | partner1 | 0812345678 | 123456   | True    |
            | guest1   | 0812345679 | 123456   | False   |
        Given Set Auth User to partner1
        Given Add max_rooms to partner1
            | max_rooms |
            | 5         |
        Given Setup Notifications
        Given Setup Firebase Tokens
            | user    | token             |
            | partner1 | 1234567890abcdef |
            | guest1   | 1234567891abcdef |
        Given Setup Locations
            | name      | description  | address     | latitude | longitude | type  | timezone     |
            | manali    | manali       | old manali  | 1.0      | 1.0       | HT | Asia/Kolkata |
            | kullu     | kullu        | kullu       | 1.0      | 1.0       | HT | Asia/Kolkata |
            | kasol     | kasol        | kasol       | 1.0      | 1.0       | HT | Asia/Kolkata |
            | bhuntar   | bhuntar      | bhuntar     | 1.0      | 1.0       | HT | Asia/Kolkata |
        Given Setup Properties
            | name      | description  | location | type  | avg_price | po_address | rooms  | rating  | meal_cost  |  directions  |
            | property1 | property1    | manali   | hotel | 1000      | manali     | 100    | 4.5     | 100        |  manali      |
        Then Validate Properties
            | name      | description  | location | type  | avg_price | po_address | rooms  | rating  | meal_cost  |  directions  |
            | property1 | property1    | manali   | hotel | 1000      | manali     | 100    | 4.5     | 100        |  manali      |
        Given Setup Rooms
            | property  | room_no  | description  | bed | max_guests  | rate   | type_of_room  |
            | property1 | 101      | room1        | 1   | 2           | 1000   | 1             |
            | property1 | 102      | room2        | 1   | 2           | 1000   | 1             |
            | property1 | 103      | room3        | 1   | 2           | 1000   | 1             |
            | property1 | 104      | room4        | 1   | 2           | 1000   | 1             |
            | property1 | 105      | room5        | 1   | 2           | 1000   | 1             |
        Given Setup Service Partners
            | name           | location     | type | description    | commission | delivery_charges | pickup_charges |
            | himalyan dhaba | manali       |  1   | himalyan dhaba | 10         | 50               | 50             |
        Given Setup Services
            | name          | partner       | charges  | tax_rate  | service_type  |
            | ala carte     | himalyan dhaba | 10      | 5         | foodservice   |
        Given Setup Service Items
            | name          | service       | description   |  addon        |price   | service_type  | vegetarian    |
            | dal makhani   | ala carte     | dal makhani   | {"butter":20} | 100    | foodservice   | True          |
            | rajma         | ala carte     | rajma         |            | 120    | foodservice   | True          |
            | aloo gobhi    | ala carte     | aloo gobhi    |             | 80     | foodservice   | True          |
            | roti          | ala carte     | roti          |             | 10     | foodservice   | True          |
            | naan          | ala carte     | naan          |             | 20     | foodservice   | True          |
            | maggi         | ala carte     | maggi         | {"masala":20} | 50     | foodservice   | True          |
        Then Validate Service Items
            | name          | service       | description   |  addon        |price   | service_type     |
            | dal makhani   | ala carte     | dal makhani   | {"butter":20} | 100    | foodservice   |
            | rajma         | ala carte     | rajma         |             | 120    | foodservice   |           
            | aloo gobhi    | ala carte     | aloo gobhi    |             | 80     | foodservice   |           
            | roti          | ala carte     | roti          |             | 10     | foodservice   |           
            | naan          | ala carte     | naan          |             | 20     | foodservice   |           
            | maggi         | ala carte     | maggi         | {"masala":20} | 50     | foodservice   | 
        Given Validate Catalog for property property1
            | name          | service       | description   |  addon        |price   | service_type     |
            | dal makhani   | ala carte     | dal makhani   | {"butter":20} | 100    | foodservice   |
            | rajma         | ala carte     | rajma         | {}            | 120    | foodservice   |           
            | aloo gobhi    | ala carte     | aloo gobhi    | {}            | 80     | foodservice   |           
            | roti          | ala carte     | roti          | {}            | 10     | foodservice   |           
            | naan          | ala carte     | naan          | {}            | 20     | foodservice   |           
            | maggi         | ala carte     | maggi         | {"masala":20} | 50     | foodservice   | 
        Given Initiate Checkin
            | room_no   | guests                                             |
            | 101       | {"raju":"9874204200","shyam": "9874214210"}        |
        Then Validate Notifications
            | user    | message  | type  | status  |
            | raju    | raju and shyam have checked in to room 101 | checkin | 1      |
            | shyam   | raju and shyam have checked in to room 101 | checkin | 1      |