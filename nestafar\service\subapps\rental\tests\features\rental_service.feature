Scenario Outline: Listing rental services
  When I get the list of rental services
  Then the response status code is 200
  And the response data contains a list of rental services

  Examples:
    | Filter       | Description                                         |
    |----------------|----------------------------------------------------|
    | All services  | Get all services                                      |
    | Active only    | Get only active services                              |
    | Filter by name | Get services with a specific name (partial match)     |
    | Filter by type | Get services of a specific type (bike, car, etc.)      |

Scenario Outline: Retrieving rental service details
  Given a rental service exists with ID "<service_id>"
  When I retrieve the rental service details
  Then the response status code is 200
  And the response data contains the rental service details

  Examples:
    | service_id |
    | 1          |
