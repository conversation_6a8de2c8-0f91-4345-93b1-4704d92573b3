import datetime
from django.db import transaction
from rest_framework.viewsets import ModelViewSet
from stay.serializers import RoomSerial<PERSON>, GuestSerializer, RoomRetrieveSerializer, RoomCreateSerializer, RoomUpdateSerializer
from stay.models import Room, RoomPhotos, Guest, RoomAmenities
from nestafar.responses import SuccessResponse, CreateResponse, BadRequestResponse
from core.permissions import PartnerPermission, PropertyPermission
from django_filters import rest_framework as filters
from rest_framework.filters import Ordering<PERSON><PERSON>er, SearchFilter
from rest_framework.decorators import action
from service import service_factory
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser, JSONParser

from service.models import BaseCart


class RoomFilter(filters.FilterSet):
    class Meta:
        model = Room
        fields = ['type_of_room', 'rate', 'checked_in']

class RoomModelViewSet(ModelViewSet):
    permission_classes = [PartnerPermission, PropertyPermission]
    serializer_class = RoomSerializer
    filter_backends = [filters.DjangoFilterBackend, OrderingFilter, SearchFilter]
    filterset_class = RoomFilter
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return RoomRetrieveSerializer
        elif self.action == 'create' or self.action == 'update':
            return RoomCreateSerializer
        else:
            return RoomSerializer

    def create(self, request, *args, **kwargs):
        if hasattr(request, "property"):
            serializers = RoomCreateSerializer(data=request.data, context={'property': request.property})
            if not serializers.is_valid():
                return BadRequestResponse(data=serializers.errors)
            max_rooms = request.user.partner_profile.max_rooms
            if len(Room.objects.filter(property=request.property)) >= max_rooms:
                return BadRequestResponse(message="Max rooms for this account reached")
            with transaction.atomic():
                room = Room(**serializers.validated_data, property=request.property)
                room.save()
                for file in request.FILES.values():
                    photo = RoomPhotos.objects.create(room=room)
                    photo.image.save(room.property.name + '/' + file.name, file.file)
            return CreateResponse(message="Room created successfully")
        else:
            return BadRequestResponse(message="Property not found")

    def update(self, request, *args, **kwargs):
        if hasattr(request, "property"):
            room = self.get_object()
            serializer = RoomUpdateSerializer(instance=room, data=request.data, context={'property': request.property})
            if not serializer.is_valid():
                return BadRequestResponse(data=serializer.errors)

            with transaction.atomic():
                Room.objects.filter(id=room.id).update(**serializer.validated_data)
                if 'image' in request.FILES:
                    # Delete existing images
                    RoomPhotos.objects.filter(room=room).delete()
                    # Add new images
                    for file in request.FILES.getlist('image'):
                        photo = RoomPhotos.objects.create(room=room)
                        photo.image.save(room.property.name + '/' + file.name, file.file)
                        photo.save()
                return SuccessResponse(message="Room updated successfully")
        else:
            return BadRequestResponse(message="Property not found")

    def get_queryset(self):
        if hasattr(self.request, "property"):
            return Room.objects.filter(property=self.request.property)
        else:
            return Room.objects.all()

    @action(methods=['GET'], detail=True, url_path='running_bill', url_name='running_bill')
    def running_bill(self, request, pk=None):
        try:
            room = self.get_object()
            guests = Guest.objects.filter(room=room, checked_in=True, checked_out=False)
            response = {}
            total_bill = 0
            if not guests.exists():
                return BadRequestResponse(message="Guest not checked in yet")
            if room.rate and room.rate > 0:
                earliest_checkin_dt = min(guests.values_list('check_in_date', flat=True))
                stay_duration = datetime.date.today() - earliest_checkin_dt.date()
                response['stay'] = {
                    'checkin_date': earliest_checkin_dt.isoformat(),
                    'duration': stay_duration.days,
                    'rate': room.rate,
                    'total': round(room.rate * stay_duration.days, 2)
                }
            for service_name, service_type in service_factory.url_mappings.items():
                orders = service_factory.service_cart_model.get(service_type).objects.filter(guest__in=guests,
                                         status=BaseCart.CartStatus.COMPLETED.value).order_by('-created_at')
                total_bill += round(sum(orders.values_list('total', flat=True)))
                response[service_name] = service_factory.service_cart_serializer.get(service_type)(orders, many=True).data
            response['total_bill'] = total_bill
            room.running_total = total_bill
            room.save()
            return SuccessResponse(data=response, message="Running Bill")
        except Room.DoesNotExist:
            return BadRequestResponse(message="Room not found")
        except Guest.DoesNotExist:
            return BadRequestResponse(message="Guest not found")
        except Exception:
            return BadRequestResponse(message="An unexpected error occurred. Please try again later.")

    @action(methods=['GET'], detail=True, url_path='guests', url_name='guests')
    def guests(self, request, pk=None):
        room = self.get_object()
        guests = Guest.objects.filter(room=room, checked_in=True, checked_out=False)
        guests = GuestSerializer(guests, many=True)
        return SuccessResponse(data=guests.data, message="Guests")

    @action(methods=['PUT'], detail=True, url_path='amenities', url_name='amenities')
    def amenities(self, request, pk=None):
        instance = self.get_object()
        amenities = request.data
        success = RoomAmenities.validate_amenities(amenities)
        if not success:
            return BadRequestResponse(data={}, message="Invalid amenities")
        instance.amenities = amenities
        instance.save()
        return SuccessResponse(message="Amenities updated successfully")
