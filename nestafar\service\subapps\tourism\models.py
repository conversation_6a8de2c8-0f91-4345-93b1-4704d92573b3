from django.db import models
from service.models import *
from geo.models import Location


class TourismService(BaseService):
    class VehicleType(models.TextChoices):
        AUTO = 'Auto'
        HATCHBACK = 'Hatchback'
        SEDAN = 'Sedan'
        SUV = 'SUV'
        VAN = 'Van'
        BUS = 'Bus'

    local = models.BooleanField(default=False)
    per_person = models.BooleanField(default=False)
    vehicle_type = models.CharField(max_length=100, choices=VehicleType.choices, default=VehicleType.SEDAN)
    disclaimer = models.CharField(max_length=500, null=True, blank=True)

    def __str__(self):
        return self.name


class TourismServiceItem(BaseServiceItem):
    class DurationType(models.TextChoices):
        HOUR = 'Hour'
        DAY = 'Day'

    service = models.ForeignKey(TourismService, on_delete=models.CASCADE, related_name='service_items')
    start_time = models.TimeField(default=None, null=True, blank=True)
    duration = models.FloatField(default=0)
    duration_type = models.Char<PERSON><PERSON>(max_length=100, choices=DurationType.choices, default=DurationType.HOUR)

    def __str__(self):
        return self.name


class ItinerarySpot(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    image = models.ImageField(upload_to='itinerary_spot_images', blank=True, null=True)
    description = models.TextField(null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='itinerary_spots')
    package = models.ForeignKey(TourismServiceItem, on_delete=models.CASCADE, related_name='itinerary_spots')
    stop_no = models.PositiveIntegerField(default=0)
    wait_time = models.FloatField(default=0)
    duration_type = models.CharField(max_length=100, choices=TourismServiceItem.DurationType.choices,
                                     default=TourismServiceItem.DurationType.HOUR)


class TourismCart(BaseCart):
    pickup_time = models.DateTimeField(blank=True, null=True)
    special_request = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.guest.user.name + "_" + self.guest.room.room_no + "_" + str(self.total)


class TourismCartItems(BaseCartItems):
    cart = models.ForeignKey(TourismCart, on_delete=models.CASCADE, related_name='cart_items')
    item = models.ForeignKey(TourismServiceItem, on_delete=models.CASCADE, related_name='cart_items')

    class Meta:
        verbose_name_plural = "Tourism cart items"

    def __str__(self):
        return self.item.name + " " + str(self.quantity) + " " + str(self.price)

    def add_item(self, commission, charges):
        self.cart.reset_cart()
        return super().add_item(commission, charges)

    def add_quantity(self, quantity):
        # Disabled adding quantity for tourism cart items
        return self


class TourismOrder(BaseOrder):
    cart = models.ForeignKey(TourismCart, on_delete=models.CASCADE, related_name='orders')
    service = models.ForeignKey(TourismService, on_delete=models.CASCADE, related_name='orders')
    pickup_time = models.DateTimeField(blank=True, null=True)
    special_request = models.TextField(null=True, blank=True)

    def __str__(self):
        return self.guest.user.name + "_" + self.service.name


class TourismOrderItem(BaseOrderItem):
    item = models.ForeignKey(TourismServiceItem, on_delete=models.PROTECT, related_name='order_items')
    order = models.ForeignKey(TourismOrder, on_delete=models.PROTECT, related_name='order_items')

    def __str__(self):
        return self.item.name
