from celery import shared_task
from .models import FoodService
from django_celery_beat.models import PeriodicTask, CrontabSchedule
from celery.utils.log import get_task_logger
import json

logger = get_task_logger(__name__)

DAY_MAPPING = {
    "Monday": 1,
    "Tuesday": 2,
    "Wednesday": 3,
    "Thursday": 4,
    "Friday": 5,
    "Saturday": 6,
    "Sunday": 0,
}

@shared_task
def show_service(service_id):
    try:
        service = FoodService.objects.get(id=service_id)
        service.is_active = True
        service.save()
    except FoodService.DoesNotExist:
        logger.error(f"FoodService with id {service_id} does not exist")
    except Exception as e:
        logger.error(f"Error showing service {service_id}: {e}")

@shared_task
def hide_service(service_id):
    try:
        service = FoodService.objects.get(id=service_id)
        service.is_active = False
        service.save()
    except FoodService.DoesNotExist:
        logger.error(f"FoodService with id {service_id} does not exist")
    except Exception as e:
        logger.error(f"Error hiding service {service_id}: {e}")

def setup_foodservice_schedule(service):
    try:
        day_integers = [DAY_MAPPING[day] for day in service.active_days]
    except KeyError as e:
        print(f"Invalid day in active_days for service {service.id}: {e}")
        return  # Skip the service with invalid active_days

    try:
        # Parse opening and closing times
        if not service.opening_time or not service.closing_time:
            logger.error(f"Missing opening or closing time for service {service.id}")
            return

        # Handle both string and datetime objects for opening_time
        if hasattr(service.opening_time, 'strftime'):
            opening_hour, opening_minute = map(int, service.opening_time.strftime('%H:%M').split(':'))
        else:
            opening_hour, opening_minute = map(int, service.opening_time.split(':'))
            
        # Handle both string and datetime objects for closing_time
        if hasattr(service.closing_time, 'strftime'):
            closing_hour, closing_minute = map(int, service.closing_time.strftime('%H:%M').split(':'))
        else:
            closing_hour, closing_minute = map(int, service.closing_time.split(':'))

        # Create schedule for opening time
        opening_schedule, created = CrontabSchedule.objects.get_or_create(
            hour=opening_hour,
            minute=opening_minute,
            day_of_week=','.join(map(str, day_integers)),
            timezone='Asia/Kolkata'
        )
        PeriodicTask.objects.update_or_create(
            crontab=opening_schedule,
            name=f'show_service_{service.id}',
            defaults={
                'task': 'service.subapps.food.tasks.show_service',
                'args': json.dumps([str(service.id)]), 
            },
        )

        # Create schedule for closing time
        closing_schedule, created = CrontabSchedule.objects.get_or_create(
            hour=closing_hour,
            minute=closing_minute,
            day_of_week=','.join(map(str, day_integers)),
            timezone='Asia/Kolkata'
        )
        PeriodicTask.objects.update_or_create(
            crontab=closing_schedule,
            name=f'hide_service_{service.id}',
            defaults={
                'task': 'service.subapps.food.tasks.hide_service',
                'args': json.dumps([str(service.id)]),  
            },
        )
    except Exception as e:
        logger.error(f"Error setting up schedules for service {service.id}: {e}")
