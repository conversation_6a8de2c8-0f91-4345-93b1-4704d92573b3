from typing import Optional, TYPE_CHECKING
from booking.models import PreCheckin
from pms.models import RoomBlock
from django.db import transaction
from decimal import Decimal, InvalidOperation
from datetime import timedelta
import logging
from notification.tasks import send_notification

if TYPE_CHECKING:
    from booking.models import Reservation
logger = logging.getLogger(__name__)


def determine_payment_status(paid: float, total: float) -> str:
    """Return normalized payment status based on amounts.

    Rules:
      - unpaid: paid <= 0
      - completed: total > 0 and paid >= total (overpayment counts as completed)
      - partial: otherwise
    """
    try:
        # Keep amounts non-negative and use Decimal for money-safe comparisons
        paid_val = max(Decimal('0'), (paid if isinstance(paid, Decimal) else Decimal(str(paid or 0))))
        total_val = max(Decimal('0'), (total if isinstance(total, Decimal) else Decimal(str(total or 0))))
    except (InvalidOperation, TypeError, ValueError):
        return 'unpaid'

    if paid_val <= 0:
        return 'unpaid'
    if total_val > 0 and paid_val >= total_val:
        return 'completed'
    return 'partial'


def trigger_precheckin_and_block(reservation: 'Reservation') -> Optional[PreCheckin]:
    """
    Create a PreCheckin and a temporary RoomBlock for a new reservation.
    Also enqueue availability distribution.
    Idempotent: if a PreCheckin already exists for this reservation, returns it.
    """
    # Idempotency: return existing PreCheckin if it already exists
    try:
        return reservation.precheckin  # reverse OneToOne
    except PreCheckin.DoesNotExist:
        pass
    try:
        with transaction.atomic():
            if not reservation.check_in or not reservation.check_out:
                raise ValueError(f"Invalid check-in/check-out dates for reservation {reservation.id}")
            
            # Calculate stay duration and effective end date
            if reservation.check_out <= reservation.check_in:
                logger.warning(f"Check-out before check-in for reservation {reservation.id}, using 1 day")
            stay_days = max(1, (reservation.check_out - reservation.check_in).days)
            effective_end_date = reservation.check_in + timedelta(days=stay_days)
            
            number_of_rooms = 1
            if reservation.room_details:
                try:
                    number_of_rooms = max(1, len(reservation.room_details))
                except Exception as e:
                    logger.error(f"Failed to determine number of rooms for reservation {reservation.id}: {e}")

            # Normalize monetary values to Decimal
            try:
                total_dec = Decimal(str(reservation.total or 0))
            except (InvalidOperation, TypeError, ValueError):
                total_dec = Decimal('0')
            
            try:
                paid_dec = Decimal(str(reservation.paid or 0))
            except (InvalidOperation, TypeError, ValueError):
                paid_dec = Decimal('0')

            pc = PreCheckin.objects.create(
                property=reservation.property,
                reservation=reservation,
                number_of_rooms=number_of_rooms,
                stay_duration=stay_days,
                expected_checkin=reservation.check_in,
                total_amount=total_dec,
                amount_paid=paid_dec,
                pending_balance=max(Decimal('0'), total_dec - paid_dec),
                payment_status=determine_payment_status(paid_dec, total_dec),
                status='pending'
            )

            # Create room block using effective end date
            RoomBlock.objects.create(
                property=reservation.property,
                reservation=reservation,
                block_type='reservation',
                start_date=reservation.check_in,
                end_date=effective_end_date,
                number_of_rooms=number_of_rooms
            )

    except Exception as e:
        logger.error(f"Failed to create PreCheckin or RoomBlock for reservation {reservation.id}: {e}")
        raise

    # Enqueue availability distribution after commit
    try:
        transaction.on_commit(
            lambda: distribute_availability_after_booking.delay(str(reservation.id))  # type: ignore
        )
    except Exception:
        logger.error(f"Failed to register availability distribution hook for reservation {reservation.id}")

    # Send notification to guest to complete pre-checkin
    try:
        user = reservation.user
        if user and user.id:
            send_notification.delay(
                user_id=str(user.id),
                event='PRE_CHECKIN_CREATED',
                data={
                    'guest_name': getattr(user, 'name', 'Guest'),
                    'property_owner_name': getattr(reservation.property, 'name', 'Property'),
                    'expected_date': reservation.check_in.strftime('%d %b %Y'),
                    'room_number': 'N/A'
                }
            )
    except Exception as e:
        logger.error(f"Failed to send notification for reservation {reservation.id}: {e}")
        pass
    return pc
