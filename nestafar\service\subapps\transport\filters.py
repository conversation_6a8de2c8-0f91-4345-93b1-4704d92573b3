from .models import *
from django_filters import rest_framework as django_filters
from service.filters import BaseCartFilter


class TransportItemFilterSet(django_filters.FilterSet):
    class Meta:
        model = TransportServiceItem
        fields = ['service', 'price', 'is_active', 'rating']


class TransportOrderFilter(django_filters.FilterSet):
    class Meta:
        model = TransportOrder
        fields = ['status', 'guest', 'cart']


class TransportCartFilter(BaseCartFilter):
    class Meta:
        model = TransportCart
        fields = ['status', 'guest']
