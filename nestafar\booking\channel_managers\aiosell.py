"""
AIOSell Channel Manager adapter for handling reservations.
"""

import logging
from datetime import datetime
from decimal import Decimal, InvalidOperation
from typing import Dict, Any, Tuple, Optional
from django.db import transaction
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.utils import timezone
from rest_framework.response import Response
from rest_framework import status
import re
from datetime import timed<PERSON><PERSON>

from .base import BaseChannelManager
from booking.models import Reservation, AllotedRoom
from stay.models import Property, Room
from pms.models import RoomBlock, RoomType, HotelOTAIntegration
from core.utils import get_or_create_user
from booking.utils import trigger_precheckin_and_block

logger = logging.getLogger(__name__)


class AIOSellChannelManager(BaseChannelManager):
    """
    AIOSell-specific implementation of the Channel Manager interface.
    
    This adapter handles the AIOSell webhook data format and implements
    all reservation operations according to AIOSell's specifications.
    """
    
    def __init__(self):
        super().__init__('aiosell')
    
    def validate_webhook_data(self, data: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """
        Validate AIOSell webhook data format.
        
        Args:
            data: Raw webhook data dictionary
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Valid action types
        VALID_ACTIONS = {'book', 'modify', 'cancel', 'inventory'}
        
        # Check required base fields
        required_fields = ['action', 'hotelCode', 'bookingId']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            return False, f'Missing required fields: {", ".join(missing_fields)}'
        
        # Validate action-specific fields
        action = data.get('action', '').lower()
        
        if action not in VALID_ACTIONS:
            return False, f'Invalid action: {action}. Must be one of: {", ".join(VALID_ACTIONS)}'
        
        if action == 'book':
            booking_required_fields = [
                'checkin', 'checkout', 'guest', 'rooms', 'amount'
            ]
            missing_booking_fields = [
                field for field in booking_required_fields 
                if field not in data
            ]
            if missing_booking_fields:
                return False, f'Missing required fields for booking: {", ".join(missing_booking_fields)}'
        
        return True, None
    
    def parse_guest_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse guest information from AIOSell webhook data.
        
        Args:
            data: Raw webhook data dictionary
            
        Returns:
            Standardized guest data dictionary
        """
        guest_data = data.get('guest', {})
        address_data = guest_data.get('address', {})
        
        return {
            'first_name': guest_data.get('firstName', ''),
            'last_name': guest_data.get('lastName', ''),
            'email': guest_data.get('email', ''),
            'phone': guest_data.get('phone', ''),
            'address': {
                'line1': address_data.get('line1', ''),
                'city': address_data.get('city', ''),
                'state': address_data.get('state', ''),
                'country': address_data.get('country', ''),
                'zip_code': address_data.get('zipCode', '')
            }
        }
    
    def parse_room_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse room details from AIOSell webhook data.
        
        Args:
            data: Raw webhook data dictionary
            
        Returns:
            Standardized room data dictionary
        """
        rooms_data = data.get('rooms', [])
        total_guests = 0
        
        for room in rooms_data:
            occupancy = room.get('occupancy', {})
            try:
                adults = int(occupancy.get('adults', 0))
                children = int(occupancy.get('children', 0))
                total_guests += max(0, adults) + max(0, children)
            except (ValueError, TypeError):
                logger.warning(f"Invalid occupancy data in room: {room}")
        
        return {
            'rooms_data': rooms_data,
            'total_guests': total_guests
        }
    
    def _map_rooms_to_property(self, incoming_rooms: list, property_obj: 'Property', check_in: datetime, check_out: datetime) -> list:
        """
        Enhanced room mapping logic to map incoming room codes to property rooms.
        
        Args:
            incoming_rooms: List of room data from webhook
            property_obj: Property object
            check_in: Check-in datetime
            check_out: Check-out datetime
            
        Returns:
            List of room mapping dictionaries
        """

        room_mappings = []        
        # Get all rooms for this property
        property_rooms_qs = Room.objects.filter(property=property_obj)
        property_rooms = list(property_rooms_qs.values('id', 'room_no', 'type_of_room', 'max_guests'))

        # Get all room types for this property for better mapping
        room_types = list(RoomType.objects.filter(hotel=property_obj).values('id', 'name', 'max_occupancy'))
        room_types_by_name = {rt['name'].lower().strip(): rt for rt in room_types}

        # Build indexes for efficient lookup
        by_room_no = {}
        by_type = {}
        by_room_no_exact = {}
        
        for pr in property_rooms:
            # Index by room number (case-insensitive)
            if pr['room_no']:
                room_no_lower = str(pr['room_no']).lower().strip()
                by_room_no.setdefault(room_no_lower, []).append(pr)
                by_room_no_exact[pr['room_no']] = pr
                
            # Index by room type (case-insensitive)
            if pr['type_of_room']:
                type_lower = str(pr['type_of_room']).lower().strip()
                by_type.setdefault(type_lower, []).append(pr)

        # Get property-specific configuration
        cm_config = property_obj.get_channel_manager_config(self.channel_name) or {}
        mapping_cfg = cm_config.get('room_mapping', {})  # Manual overrides {external_code: internal_room_identifier}
        force_review_unmatched = bool(cm_config.get('force_review_unmatched', False))
        rateplan_pattern = cm_config.get('rateplan_room_extraction_regex', r'([A-Za-z0-9]+)$')
        
        # Compile regex pattern for rateplan extraction
        compiled_pattern = None
        try:
            compiled_pattern = re.compile(rateplan_pattern)
        except re.error:
            logger.warning(f"Invalid rateplan regex in config: {rateplan_pattern}; using default fallback")
            compiled_pattern = re.compile(r'([A-Za-z0-9]+)$')

        def pick_best_match(candidates: list, criteria: str) -> tuple:
            """
            Pick the best room from candidates based on criteria.
            Returns (room_dict, error_message)
            """
            if not candidates:
                return None, 'no candidates found'
            if len(candidates) == 1:
                return candidates[0], None
            
            # If multiple candidates, prefer rooms that are not currently occupied
            available_candidates = [c for c in candidates if not c.get('occupied', False)]
            if available_candidates:
                if len(available_candidates) == 1:
                    return available_candidates[0], None
                # Still multiple available, return first one but log warning
                logger.warning(f"Multiple available rooms match {criteria}, selecting first one")
                return available_candidates[0], f'multiple matches for {criteria}, selected first available'
            
            # All rooms occupied, return first one with warning
            logger.warning(f"All matching rooms are occupied for {criteria}")
            return candidates[0], f'multiple matches for {criteria}, all occupied'

        def check_room_availability(room_id: str) -> bool:
            """Check if a specific room is available between check_in and check_out.
            Conditions for NOT available:
            - Active RoomBlock overlapping the window
            - Room is already alloted in a PreCheckin with overlapping window (and not cancelled)
            Validation: room_id must be provided and convertible to int.
            """

            # Validate room_id
            if room_id is None:
                raise ValueError("room_id is required")
            try:
                room_id_int = int(room_id)
            except (TypeError, ValueError):
                raise ValueError(f"room_id must be an integer-convertible value, got {room_id!r}")

            # Overlapping if start < other_end and other_start < end
            # Check active blocks
            has_block = RoomBlock.objects.filter(
                room_id=room_id_int,
                is_active=True
            ).filter(
                Q(blocked_until__isnull=True, blocked_from__lt=check_out) |
                Q(blocked_until__isnull=False, blocked_from__lt=check_out, blocked_until__gt=check_in)
            ).exists()
            if has_block:
                return False

            # Check allotments via PreCheckin windows (exclude null pre_checkin to avoid None lookups)
            allotments = AllotedRoom.objects.select_related('pre_checkin').filter(
                room_id=room_id_int, pre_checkin__isnull=False
            )
            for ar in allotments:
                pc = ar.pre_checkin
                if not pc:
                    continue
                # Derive precheckin window
                pc_start = pc.expected_checkin
                if pc_start is None:
                    # Missing expected_checkin -> treat as unavailable (or raise); choose to return False
                    logger.warning("PreCheckin %s missing expected_checkin for room %s; treating as unavailable", getattr(pc, 'id', '?'), room_id_int)
                    return False
                try:
                    pc_end = pc.get_expected_checkout()
                except Exception as e:
                    # Fallback: compute from expected_checkin + stay_duration
                    try:
                        duration_days = int(pc.stay_duration or 1)
                    except (TypeError, ValueError):
                        duration_days = 1
                    duration_days = max(1, duration_days)
                    pc_end = pc_start + timezone.timedelta(days=duration_days)
                    logger.debug(
                        "Fallback checkout computation for PreCheckin %s due to error %s; using %s",
                        getattr(pc, 'id', '?'), e, pc_end
                    )
                if pc.status != 'cancelled' and pc_start < check_out and check_in < pc_end:
                    return False
            return True

        # Process each incoming room
        for room_data in incoming_rooms:
            if not isinstance(room_data, dict):
                logger.warning(f"Invalid room data structure (expected dict): {room_data}")
                room_mappings.append({
                    'raw_data': room_data, 
                    'warning': 'invalid structure',
                    'mapped': False
                })
                continue

            room_code = str(room_data.get('roomCode', '')).strip()
            rateplan_code = str(room_data.get('rateplanCode', '')).strip()
            guest_name = room_data.get('guestName', '')
            occupancy = room_data.get('occupancy', {})
            prices = room_data.get('prices', [])
            
            warnings = []
            matched_room = None
            mapping_method = None

            # Step 1: Check manual mapping override first
            if room_code and room_code in mapping_cfg:
                manual_target = mapping_cfg[room_code]
                # Manual target could be room_no or type_of_room
                candidates = (by_room_no.get(str(manual_target).lower(), []) + 
                             by_type.get(str(manual_target).lower(), []))
                matched_room, warning = pick_best_match(candidates, f'manual mapping: {manual_target}')
                if warning:
                    warnings.append(warning)
                if matched_room:
                    mapping_method = 'manual_override'

            # Step 2: Exact room number match
            if not matched_room and room_code:
                candidates = by_room_no.get(room_code.lower(), [])
                matched_room, warning = pick_best_match(candidates, f'room number: {room_code}')
                if warning:
                    warnings.append(warning)
                if matched_room:
                    mapping_method = 'exact_room_number'

            # Step 3: Room type match
            if not matched_room and room_code:
                candidates = by_type.get(room_code.lower(), [])
                matched_room, warning = pick_best_match(candidates, f'room type: {room_code}')
                if warning:
                    warnings.append(warning)
                if matched_room:
                    mapping_method = 'room_type_match'

            # Step 4: Pattern extraction from rateplan code
            if not matched_room and rateplan_code and compiled_pattern:
                match = compiled_pattern.search(rateplan_code)
                if match:
                    extracted_token = match.group(1).lower()
                    # Try both room number and type with extracted token
                    candidates = (by_room_no.get(extracted_token, []) + 
                                 by_type.get(extracted_token, []))
                    matched_room, warning = pick_best_match(candidates, f'rateplan extraction: {extracted_token}')
                    if warning:
                        warnings.append(warning)
                    if matched_room:
                        mapping_method = 'rateplan_pattern'
                else:
                    warnings.append(f'no pattern match in rateplan: {rateplan_code}')

            # Step 5: Fuzzy matching for partial matches (optional enhancement)
            if not matched_room and room_code:
                # Try partial matches for room codes (e.g., "SUITE" matches "SUITE-DELUXE")
                for room_type_key, candidates in by_type.items():
                    if room_code.lower() in room_type_key or room_type_key in room_code.lower():
                        matched_room, warning = pick_best_match(candidates, f'fuzzy type match: {room_type_key}')
                        if warning:
                            warnings.append(warning)
                        if matched_room:
                            mapping_method = 'fuzzy_type_match'
                            break

            # Validate guest capacity if room is matched
            if matched_room:
                try:
                    adults_val = int(occupancy.get('adults', 0))
                    children_val = int(occupancy.get('children', 0))
                except (TypeError, ValueError):
                    adults_val = 0
                    children_val = 0
                adults = max(0, adults_val)
                children = max(0, children_val)
                total_occupancy = adults + children
                max_guests = matched_room.get('max_guests', 0)
                if total_occupancy > max_guests:
                    warnings.append(f'occupancy ({total_occupancy}) exceeds room capacity ({max_guests})')

            # Check availability
            if matched_room and not check_room_availability(matched_room['id']):
                warnings.append('room not available for selected dates')
                # Don't unmatch the room, just flag it - hotel can handle overbooking

            # Add room type mapping information
            matched_room_type = None
            room_type_id = None
            
            # Try to map to RoomType using room code
            if room_code:
                # First try exact match
                matched_room_type = room_types_by_name.get(room_code.lower().strip())
                
                # If no exact match, try fuzzy matching
                if not matched_room_type:
                    for type_name, room_type in room_types_by_name.items():
                        if room_code.lower() in type_name or type_name in room_code.lower():
                            matched_room_type = room_type
                            break
                
                if matched_room_type:
                    room_type_id = str(matched_room_type['id'])

            # Create mapping entry with enhanced room type information
            mapping_entry = {
                'roomCode': room_code or None,
                'rateplanCode': rateplan_code or None,
                'guestName': guest_name or None,
                'occupancy': occupancy,
                'prices': prices,
                'mapped': matched_room is not None,
                'mapping_method': mapping_method,
                'matched_room_id': str(matched_room['id']) if matched_room else None,
                'matched_room_no': matched_room['room_no'] if matched_room else None,
                'matched_type_of_room': matched_room['type_of_room'] if matched_room else None,
                'matched_max_guests': matched_room['max_guests'] if matched_room else None,
                'room_type_id': room_type_id,
                'room_type_name': matched_room_type['name'] if matched_room_type else None,
                'room_type_max_occupancy': matched_room_type['max_occupancy'] if matched_room_type else None,
            }

            # Add warnings and review flags
            if not matched_room:
                warnings.append('no matching room found')
                if force_review_unmatched:
                    mapping_entry['requires_review'] = True
                    
            if warnings:
                mapping_entry['warnings'] = warnings

            room_mappings.append(mapping_entry)

        # Log mapping summary
        mapped_count = sum(1 for m in room_mappings if m.get('mapped', False))
        total_rooms = len(room_mappings)
        logger.info(f"Room mapping summary: {mapped_count}/{total_rooms} rooms mapped successfully")
        
        if mapped_count < total_rooms:
            unmapped_codes = [m.get('roomCode', 'N/A') for m in room_mappings if not m.get('mapped', False)]
            logger.warning(f"Unmapped room codes: {unmapped_codes}")

        return room_mappings
    
    def create_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Create a new reservation from AIOSell webhook data.
        
        Args:
            data: Validated webhook data dictionary
            
        Returns:
            Django REST framework Response object
        """
        try:
            with transaction.atomic():
                # Get property by hotel code via HotelOTAIntegration
                hotel_code = data.get('hotelCode')
                try:
                    hotel_integration = HotelOTAIntegration.objects.get(external_hotel_id=hotel_code)
                    property_obj = hotel_integration.hotel
                except HotelOTAIntegration.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Hotel Configurations not found for hotel code: {hotel_code}'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # Atomic check for duplicate booking with row-level lock
                external_booking_id = data.get('bookingId')
                
                # Use select_for_update to prevent race conditions
                existing_reservation = Reservation.objects.select_for_update().filter(
                    external_booking_id=external_booking_id
                ).first()
                
                if existing_reservation:
                    return Response({
                        'success': False,
                        'message': f'Reservation already exists for booking ID: {external_booking_id}'
                    }, status=status.HTTP_409_CONFLICT)
                
                # Parse dates
                try:
                    check_in_date = datetime.strptime(data.get('checkin'), '%Y-%m-%d')
                    check_out_date = datetime.strptime(data.get('checkout'), '%Y-%m-%d')
                    
                    # Convert to timezone-aware datetime objects
                    check_in = timezone.make_aware(check_in_date)
                    check_out = timezone.make_aware(check_out_date)
                    
                    # Validate date range
                    if check_out <= check_in:
                        return Response({
                            'success': False,
                            'message': 'Check-out date must be after check-in date'
                        }, status=status.HTTP_400_BAD_REQUEST)
                        
                except ValueError:
                    return Response({
                        'success': False,
                        'message': 'Invalid date format. Use YYYY-MM-DD'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Parse booked_on date
                booked_on = None
                if data.get('bookedOn'):
                    try:
                        booked_on_naive = datetime.strptime(data.get('bookedOn'), '%Y-%m-%d %H:%M:%S')
                        booked_on = timezone.make_aware(booked_on_naive)
                    except ValueError:
                        logger.warning(f"Invalid bookedOn date format: {data.get('bookedOn')}")
                
                # Parse guest data
                guest_info = self.parse_guest_data(data)
                
                # Create user name from first and last name
                full_name = f"{guest_info['first_name']} {guest_info['last_name']}".strip()
                if not full_name:
                    full_name = "Guest User"  # Fallback name
                
                try:
                    user = get_or_create_user(
                        name=full_name,
                        phone=guest_info['phone'],
                        email=guest_info['email']
                    )
                except ValidationError as e:
                    logger.error(f"User validation error: {str(e)}")
                    return Response({
                        'success': False,
                        'message': f'Invalid guest data: {str(e)}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    logger.error(f"Error creating user: {str(e)}")
                    return Response({
                        'success': False,
                        'message': 'Error processing guest information'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
                # Parse room data
                room_info = self.parse_room_data(data)
                
                # Enhanced room mapping with deterministic strategy & availability check
                incoming_rooms = room_info['rooms_data']
                room_mappings = []
                
                if incoming_rooms:
                    room_mappings = self._map_rooms_to_property(
                        incoming_rooms, 
                        property_obj, 
                        check_in, 
                        check_out
                    )

                # Validate guest count
                if room_info['total_guests'] <= 0:
                    return Response({
                        'success': False,
                        'message': 'Invalid guest count: must be at least 1'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if room_info['total_guests'] > 20:  # Reasonable upper limit
                    return Response({
                        'success': False,
                        'message': 'Invalid guest count: exceeds maximum allowed (20)'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                # Parse amount data with proper decimal conversion
                amount_data = data.get('amount', {})
                total_amount = float(amount_data.get('amountAfterTax', 0))
                
                try:
                    amount_before_tax = Decimal(str(amount_data.get('amountBeforeTax', 0)))
                    tax_amount = Decimal(str(amount_data.get('tax', 0)))
                except (InvalidOperation, ValueError):
                    return Response({
                        'success': False,
                        'message': 'Invalid amount format in request data'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                currency = amount_data.get('currency', 'INR')
                # Validate currency code
                VALID_CURRENCIES = {'INR', 'USD', 'EUR', 'GBP'}  # Add more as needed
                if currency not in VALID_CURRENCIES:
                    logger.warning(f"Invalid currency code: {currency}, defaulting to INR")
                    currency = 'INR'

                # Determine payment status
                pah = data.get('pah', False)
                paid_amount = 0 if pah else total_amount
                
                # Create the reservation
                try:
                    reservation = Reservation.objects.create(
                        user=user,
                        property=property_obj,
                        check_in=check_in,
                        check_out=check_out,
                        guests=room_info['total_guests'],
                        total=total_amount,
                        paid=paid_amount,
                        requests=data.get('specialRequests', ''),
                        status='confirmed',
                        booking_details=data,
                        
                        # AIOSell specific fields
                        external_booking_id=external_booking_id,
                        cm_booking_id=data.get('cmBookingId'),
                        channel=data.get('channel'),
                        segment=data.get('segment', 'OTA'),
                        pah=pah,
                        booked_on=booked_on,
                        amount_before_tax=amount_before_tax,
                        tax_amount=tax_amount,
                        currency=currency,
                        room_details={
                            'rooms': room_info['rooms_data'],
                            'mappings': room_mappings
                        }
                    )
                except ValidationError as e:
                    logger.error(f"Validation error creating reservation: {str(e)}")
                    return Response({
                        'success': False,
                        'message': f'Validation error: {str(e)}'
                    }, status=status.HTTP_400_BAD_REQUEST)
                except Exception as e:
                    logger.error(f"Database error creating reservation: {str(e)}")
                    return Response({
                        'success': False,
                        'message': 'Database error while creating reservation'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
                logger.info(f"AIOSell reservation created: {reservation.id} for booking {external_booking_id}")

                # Trigger pre-checkin workflow and smart room block
                try:
                    trigger_precheckin_and_block(reservation)
                except Exception as e:
                    logger.error(f"Failed to trigger precheckin workflow for reservation {reservation.id}: {str(e)}")

                return Response({
                    'success': True,
                    'message': 'Reservation Created Successfully'
                }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"AIOSell booking creation error: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': 'Failed to create reservation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def modify_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Modify an existing reservation from AIOSell webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        try:
            with transaction.atomic():
                external_booking_id = data.get('bookingId')

                # Find existing reservation
                try:
                    reservation = Reservation.objects.get(external_booking_id=external_booking_id)
                except Reservation.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Reservation not found for booking ID: {external_booking_id}'
                    }, status=status.HTTP_404_NOT_FOUND)

                # Parse updated dates if provided
                if data.get('checkin'):
                    try:
                        checkin_naive = datetime.strptime(data.get('checkin'), '%Y-%m-%d')
                        reservation.check_in = timezone.make_aware(checkin_naive)
                    except ValueError:
                        return Response({
                            'success': False,
                            'message': 'Invalid checkin date format. Use YYYY-MM-DD'
                        }, status=status.HTTP_400_BAD_REQUEST)

                if data.get('checkout'):
                    try:
                        checkout_naive = datetime.strptime(data.get('checkout'), '%Y-%m-%d')
                        reservation.check_out = timezone.make_aware(checkout_naive)
                    except ValueError:
                        return Response({
                            'success': False,
                            'message': 'Invalid checkout date format. Use YYYY-MM-DD'
                        }, status=status.HTTP_400_BAD_REQUEST)

                # Validate date consistency after any date updates
                if reservation.check_out <= reservation.check_in:
                    return Response({
                        'success': False,
                        'message': 'Check-out date must be after check-in date'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Update amount if provided
                if data.get('amount'):
                    amount_data = data.get('amount')
                    reservation.total = float(amount_data.get('amountAfterTax', reservation.total))
                    
                    try:
                        if amount_data.get('amountBeforeTax') is not None:
                            reservation.amount_before_tax = Decimal(str(amount_data.get('amountBeforeTax')))
                        if amount_data.get('tax') is not None:
                            reservation.tax_amount = Decimal(str(amount_data.get('tax')))
                    except (InvalidOperation, ValueError):
                        return Response({
                            'success': False,
                            'message': 'Invalid amount format in request data'
                        }, status=status.HTTP_400_BAD_REQUEST)
                    
                    reservation.currency = amount_data.get('currency', reservation.currency)

                # Update special requests if provided
                if data.get('specialRequests'):
                    reservation.requests = data.get('specialRequests')

                # Update room details and guest count if provided
                if data.get('rooms'):
                    room_info = self.parse_room_data(data)
                    if room_info['total_guests'] > 0:
                        reservation.guests = room_info['total_guests']
                    reservation.room_details = room_info['rooms_data']

                # Update PAH status
                if 'pah' in data:
                    reservation.pah = data.get('pah')
                    if not reservation.pah and reservation.paid == 0:
                        reservation.paid = reservation.total  # Mark as prepaid

                # Update booking details with new data
                if not reservation.booking_details:
                    reservation.booking_details = {}
                reservation.booking_details.update(data)

                reservation.save()

                logger.info(f"AIOSell reservation modified: {reservation.id} for booking {external_booking_id}")

                return Response({
                    'success': True,
                    'message': 'Reservation Modified Successfully'
                }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"AIOSell booking modification error: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': 'Failed to modify reservation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def cancel_reservation(self, data: Dict[str, Any]) -> Response:
        """
        Cancel an existing reservation from AIOSell webhook data.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        try:
            with transaction.atomic():
                external_booking_id = data.get('bookingId')

                # Find existing reservation
                try:
                    reservation = Reservation.objects.get(external_booking_id=external_booking_id)
                except Reservation.DoesNotExist:
                    return Response({
                        'success': False,
                        'message': f'Reservation not found for booking ID: {external_booking_id}'
                    }, status=status.HTTP_404_NOT_FOUND)

                # Check if reservation can be cancelled
                if reservation.status == 'cancelled':
                    return Response({
                        'success': True,
                        'message': 'Reservation already cancelled'
                    }, status=status.HTTP_200_OK)

                if reservation.status == 'checked_out':
                    return Response({
                        'success': False,
                        'message': 'Cannot cancel a checked-out reservation'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Update reservation status
                reservation.status = 'cancelled'

                # Update booking details with cancellation data
                if reservation.booking_details:
                    reservation.booking_details.update(data)
                else:
                    reservation.booking_details = data

                reservation.save()

                logger.info(f"AIOSell reservation cancelled: {reservation.id} for booking {external_booking_id}")

                return Response({
                    'success': True,
                    'message': 'Reservation Cancelled Successfully'
                }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"AIOSell booking cancellation error: {str(e)}", exc_info=True)
            return Response({
                'success': False,
                'message': 'Failed to cancel reservation'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update_inventory(self, data: Dict[str, Any]) -> Response:
        """
        Update room inventory from AIOSell webhook data.

        Note: This is a placeholder implementation as inventory management
        is not currently implemented in the system. This method can be
        extended when inventory management features are added.

        Args:
            data: Validated webhook data dictionary

        Returns:
            Django REST framework Response object
        """
        logger.info(f"AIOSell inventory update received: {data}")

        return Response({
            'success': True,
            'message': 'Inventory update received (not implemented yet)'
        }, status=status.HTTP_200_OK)
