from django.contrib.auth.backends import BaseBackend
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.core.exceptions import PermissionDenied

from rest_framework.authentication import SessionAuthentication


class CsrfExemptSessionAuthentication(SessionAuthentication):
    def enforce_csrf(self, request):
        return  # To not perform the csrf check previously happening


class OtpAuthBackend(BaseBackend):
    """
    Authenticate against the OTP or password.

    Use the login name and a hash of the password and otp.
    Either otp or password is mandatory to login
    """

    def authenticate(self, request, phone=None, password=None, otp=None, **kwargs):
        try:
            if kwargs.get("username") and phone is None:
                phone = kwargs.get("username")
            user = get_user_model().objects.get(phone=phone)
            if user.check_password(password):
                return user
            else:
                generated_otp = cache.get(phone)
                if generated_otp and otp and generated_otp == otp:
                    user.is_verified = True
                    user.save()
                    return user
                else:
                    raise PermissionDenied("Generate OTP to login")
        except get_user_model().DoesNotExist:
            return

    def get_user(self, user_id):
        try:
            return get_user_model().objects.get(pk=user_id)
        except get_user_model().DoesNotExist:
            return None
