from django.contrib import admin
from .models import TransportService, TransportServiceItem, TransportCart, TransportCartItems, TransportOrder, TransportOrderItem

@admin.register(TransportService)
class TransportServiceAdmin(admin.ModelAdmin):
    list_display = ('name', 'waiting_charge_rate', 'night_service', 'outstation', 'vehicle_type', 'created_at', 'updated_at')
    list_filter = ('night_service', 'outstation', 'vehicle_type', 'created_at', 'updated_at')
    search_fields = ('name',)
    ordering = ('-updated_at',)

@admin.register(TransportServiceItem)
class TransportServiceItemAdmin(admin.ModelAdmin):
    list_display = ('name', 'service', 'stop_location', 'created_at', 'updated_at')
    list_filter = ('service', 'stop_location', 'created_at', 'updated_at')
    search_fields = ('name', 'service__name', 'stop_location__name')
    ordering = ('-updated_at',)

@admin.register(TransportCart)
class TransportCartAdmin(admin.ModelAdmin):
    list_display = ('guest', 'pickup_time', 'pickup_location', 'drop_location', 'subtotal', 'taxes', 'charges', 'total', 'created_at', 'updated_at')
    list_filter = ('pickup_time', 'pickup_location', 'drop_location', 'created_at', 'updated_at')
    search_fields = ('guest__user__name', 'pickup_location__name', 'drop_location__name')
    ordering = ('-updated_at',)

@admin.register(TransportCartItems)
class TransportCartItemsAdmin(admin.ModelAdmin):
    list_display = ('item', 'cart', 'waiting_time', 'waiting_charge', 'stop_no', 'quantity', 'price', 'created_at', 'updated_at')
    list_filter = ('cart', 'item', 'created_at', 'updated_at')
    search_fields = ('item__name', 'cart__guest__user__name')
    ordering = ('-updated_at',)

@admin.register(TransportOrder)
class TransportOrderAdmin(admin.ModelAdmin):
    list_display = ('guest', 'service', 'cart', 'pickup_time', 'pickup_location', 'drop_location', 'subtotal', 'commissions', 'taxes', 'charges', 'total', 'created_at', 'updated_at')
    list_filter = ('service', 'guest', 'pickup_time', 'pickup_location', 'drop_location', 'created_at', 'updated_at')
    search_fields = ('guest__user__name', 'service__name', 'pickup_location__name', 'drop_location__name')
    ordering = ('-updated_at',)

@admin.register(TransportOrderItem)
class TransportOrderItemAdmin(admin.ModelAdmin):
    list_display = ('item', 'order', 'waiting_time', 'waiting_charge', 'stop_no', 'quantity', 'price', 'created_at', 'updated_at')
    list_filter = ('order', 'item', 'created_at', 'updated_at')
    search_fields = ('item__name', 'order__guest__user__name', 'order__service__name')
    ordering = ('-updated_at',)
