from rest_framework.fields import Serializer<PERSON>ethod<PERSON>ield
from rest_framework.serializers import ModelSerializer, ValidationError
from .models import *
from ...models import ServicePartner


class TourismServiceSerializer(ModelSerializer):
    n_items = SerializerMethodField(required=False)

    def get_n_items(self, obj):
        return obj.service_items.count()

    class Meta:
        model = TourismService
        fields = '__all__'


class ItinerarySerializer(ModelSerializer):
    class Meta:
        model = ItinerarySpot
        fields = '__all__'

class TourismServiceItemSerializer(ModelSerializer):
    def validate(self, attrs):
        addon = attrs.get('addon')
        if addon:
            for addon_item in addon.keys():
                try:
                    addon_price = float(addon[addon_item])
                    addon[addon_item] = addon_price
                except ValueError:
                    raise ValidationError("Invalid price for addon")
        else:
            attrs['addon'] = None
        return attrs

    class Meta:
        model = TourismServiceItem
        fields = '__all__'


class TourismServiceItemLocationSerializer(TourismServiceItemSerializer):
    itinerary_spots = ItinerarySerializer(many=True)
    vehicle_type = SerializerMethodField(required=False)

    def get_vehicle_type(self, obj):
        return obj.service.vehicle_type

    class Meta:
        model = TourismServiceItem
        fields = '__all__'


class TourismOrderItemSerializer(ModelSerializer):
    item = TourismServiceItemSerializer()

    class Meta:
        model = TourismOrderItem
        fields = '__all__'


class ServicePartnerNameSerializer(ModelSerializer):
    class Meta:
        model = ServicePartner
        fields = ["id", "name", "type_of_service", "phone_number"]


class TourismOrderSerializer(ModelSerializer):
    order_items = TourismOrderItemSerializer(many=True)
    service_partner = ServicePartnerNameSerializer()
    guest = SerializerMethodField(required=False)

    def get_guest(self, obj):
        return {'id': obj.guest.id,
                'phone_no': obj.guest.user.phone.as_e164,
                'room_no': obj.guest.room.room_no,
                'name': obj.guest.user.name
                }

    class Meta:
        model = TourismOrder
        fields = '__all__'


class TourismCartItemSerializer(ModelSerializer):
    name = SerializerMethodField(required=False)

    def get_name(self, obj):
        return obj.name if obj.name else obj.item.name

    class Meta:
        model = TourismCartItems
        exclude = ['cart']


class TourismCartSerializer(ModelSerializer):
    cart_items = TourismCartItemSerializer(many=True)

    class Meta:
        model = TourismCart
        fields = '__all__'
