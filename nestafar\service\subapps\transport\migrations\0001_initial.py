# Generated by Django 4.2.7 on 2025-07-19 07:37

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('service', '0001_initial'),
        ('stay', '0001_initial'),
        ('geo', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TransportCart',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Ordered'), (2, 'Partially Accepted'), (3, 'Accepted'), (4, 'Ongoing'), (5, 'Partially Rejected'), (6, 'Rejected'), (7, 'Cancelled'), (8, 'Completed'), (9, 'Incomplete')], default=0)),
                ('subtotal', models.FloatField(default=0)),
                ('taxes', models.FloatField(default=0)),
                ('charges', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
                ('order_created_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pickup_time', models.DateTimeField(blank=True, null=True)),
                ('drop_location', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='drop_carts', to='geo.location')),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest')),
                ('pickup_location', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pickup_carts', to='geo.location')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransportOrder',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.PositiveSmallIntegerField(choices=[(0, 'Pending'), (1, 'Accepted'), (2, 'Ongoing'), (3, 'Rejected'), (4, 'Cancelled'), (5, 'Completed')], default=0)),
                ('subtotal', models.FloatField(default=0)),
                ('commissions', models.FloatField(default=0)),
                ('taxes', models.FloatField(default=0)),
                ('charges', models.FloatField(default=0)),
                ('total', models.FloatField(default=0)),
                ('rating', models.FloatField(default=0)),
                ('review', models.TextField(default='')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('pickup_time', models.DateTimeField(blank=True, null=True)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='transport.transportcart')),
                ('drop_location', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='drop_orders', to='geo.location')),
                ('guest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stay.guest')),
                ('pickup_location', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='pickup_orders', to='geo.location')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransportService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('charges', models.FloatField(blank=True, null=True)),
                ('tax_rate', models.FloatField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('waiting_charge_rate', models.FloatField(default=0)),
                ('night_service', models.BooleanField(default=False)),
                ('outstation', models.BooleanField(default=False)),
                ('vehicle_type', models.CharField(choices=[('Auto', 'Auto'), ('Hatchback', 'Hatchback'), ('Sedan', 'Sedan'), ('SUV', 'Suv'), ('Van', 'Van'), ('Bus', 'Bus')], default='Sedan', max_length=100)),
                ('partner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='service.servicepartner')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransportServiceItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(blank=True, null=True, upload_to='service_item_images')),
                ('addon', models.JSONField(blank=True, null=True)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('price', models.FloatField()),
                ('is_active', models.BooleanField(default=True)),
                ('rating', models.FloatField(blank=True, default=0, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='service_items', to='transport.transportservice')),
                ('stop_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transport_stops', to='geo.location')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='TransportOrderItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.IntegerField(default=1)),
                ('add_ons', models.JSONField(blank=True, default=dict, null=True)),
                ('price', models.FloatField(default=0)),
                ('rating', models.FloatField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('waiting_time', models.FloatField(default=0)),
                ('waiting_charge', models.FloatField(default=0)),
                ('stop_no', models.IntegerField(default=1)),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='order_items', to='transport.transportserviceitem')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='order_items', to='transport.transportorder')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='transportorder',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='transport.transportservice'),
        ),
        migrations.AddField(
            model_name='transportorder',
            name='service_partner',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='service.servicepartner'),
        ),
        migrations.CreateModel(
            name='TransportCartItems',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('quantity', models.IntegerField(default=1)),
                ('price', models.FloatField(default=0)),
                ('ordered', models.BooleanField(default=False)),
                ('add_ons', models.JSONField(blank=True, default=list, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('waiting_time', models.FloatField(default=0)),
                ('waiting_charge', models.FloatField(default=0)),
                ('stop_no', models.IntegerField(default=1)),
                ('cart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='transport.transportcart')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cart_items', to='transport.transportserviceitem')),
            ],
            options={
                'verbose_name_plural': 'Transport cart items',
            },
        ),
        migrations.AddIndex(
            model_name='transportorder',
            index=models.Index(fields=['guest', 'cart'], name='transport_t_guest_i_a54a3a_idx'),
        ),
    ]
