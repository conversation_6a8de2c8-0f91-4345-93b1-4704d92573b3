from django_filters import rest_framework as django_filters
from .models import *
from service.filters import BaseCartFilter


class ShopServiceItemFilter(django_filters.FilterSet):
    class Meta:
        model = ShopServiceItem
        fields = ['category', 'is_featured',
                  'service', 'price', 'is_active', 'rating']


class ShopOrderFilter(django_filters.FilterSet):
    class Meta:
        model = ShopOrder
        fields = ['status', 'guest', 'cart']


class ShopCartFilter(BaseCartFilter):
    class Meta:
        model = ShopCart
        fields = ['status', 'guest']
