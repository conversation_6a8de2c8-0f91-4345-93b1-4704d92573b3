from pytest_bdd import scenario, given, then, when
from transport.models import *
from transport.tests import transportest

@scenario("features/tests/transport_order_test.feature")
def test_manage_transport_orders(client, user):
    pass

@given("I am an authenticated user with a transport cart containing items")
def create_transport_cart_with_items(client, user, create_transport_service_item=transportest.create_transport_service_item):
    cart, _ = TransportCart.objects.get_or_create(guest=user.guest, defaults={"status": "OPEN"})
    item = create_transport_service_item
    TransportCartItems.objects.create(cart=cart, item=item, quantity=1)
    return cart

@when("I create a transport order")
def create_transport_order(client, user, cart):
    response = client.post(f"/api/transport-carts/{cart.id}/orders/", format="json")
    response.json()  # trigger data parsing

@then("Then the response status code is 201")
def check_create_order_status_code(response):
    assert response.status_code == 201

@then("And a new transport order is created with the cart items details")
def check_create_order_response(response, cart):
    data = response.json()
    assert data["cart"] == cart.id
    # Assert presence of other order details based on your serializer (e.g., service, status)
    order = TransportOrder.objects.get(id=data["id"])
    assert order.cart == cart
    # Use rental_order_steps.py assertions for ordered items (consider modifying for transport specific details)
    transportest.check_rental_order_items(order.order_items.all())

@given("a transport order exists with ID '<order_id>'")
def get_transport_order_by_id(order_id):
    order = TransportOrder.objects.get(pk=order_id)
    return order

@when("I retrieve the details of the transport order")
def retrieve_transport_order_details(client, order):
    response = client.get(f"/api/transport-orders/{order.id}/")
    response.json()  # trigger data parsing

@then("Then the response status code is 200")
def check_get_order_details_status_code(response):
    assert response.status_code == 200

@then("And the response data contains the transport order details, including:")
def check_get_order_details_response(response, order):
    data = response.json()
    assert data["id"] == order.id
    # Assert guest information (consider modifying for your user model)
    assert data["guest"]["name"] == order.guest.user.name
    assert data["guest"]["phone_no"] == order.guest.user.phone.as_e164
    # Assert service information
    assert data["service"]["name"] == order.service.name
    # Assert ordered transport service items (consider modifying for transport specific details)
    transportest.check_rental_order_items(TransportOrderItem.objects.filter(order=order))

# Optional steps for updating a transport order
@when("I update the transport order with '<field_to_update>' set to '<new_value>'")
def update_transport_order(client, order, field_to_update, new_value):
    data = {field_to_update: new_value}
    response = client.patch(f"/api/transport-orders/{order.id}/", data=data, format="json")
    response.json()  # trigger data parsing

@then("Then the transport order is updated with the new value")
def check_update_order_response(response, order, field_to_update, new_value):
    assert response.status_code == 200
    order.refresh_from_db()  # Reload order data from database
    if field_to_update == "pickup_location" or field_to_update == "drop_location":
        assert getattr(order, field_to_update).id == int(new_value)
    elif field_to_update == "pickup_time":
        assert order.pickup_time.strftime("%Y-%m-%dT%H:%M:%S") == new_value
    else:
        pass
     #To handle other updatable fields based on the API

