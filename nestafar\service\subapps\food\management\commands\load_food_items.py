import json
import pandas as pd
import traceback
import urllib
from django.core.management import BaseCommand
from django.db import transaction
from django.db import models
from django.core.files.base import ContentFile
from openai import OpenAI
from service.subapps.food.models import FoodService, FoodServiceItem

class Command(BaseCommand):
    help = "Load food items from CSV or generate images for missing items."

    def add_arguments(self, parser):
        parser.add_argument('service_id', type=str, help="ID of the FoodService to add items to")
        parser.add_argument('filepath', type=str, nargs='?', help="Optional: CSV file path to load food items")

    def handle(self, *args, **kwargs):
        service_id = kwargs['service_id']
        filepath = kwargs.get('filepath')
        
        try:
            service = FoodService.objects.get(pk=service_id)
        except FoodService.DoesNotExist:
            self.stdout.write(self.style.ERROR(f"FoodService with id '{service_id}' does not exist."))
            return

        if filepath:
            self.load_food_items_from_csv(filepath, service)
        else:
            self.generate_images_for_missing_items(service)

    def load_food_items_from_csv(self, filepath, service):
        try:
            df = pd.read_csv(filepath)
            self.stdout.write(self.style.SUCCESS(f"Processing {len(df)} food items from {filepath}..."))

            created_count = 0
            def process_row(row):
                nonlocal created_count
                self.validate_row(row)  
                self.create_food_item(row, service)
                created_count += 1

            df.apply(lambda row: process_row(row), axis=1)
            
            self.stdout.write(self.style.SUCCESS(f'Successfully added {created_count} items to service {service.name}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error loading CSV: {e}\n{traceback.format_exc()}"))

    @transaction.atomic
    def create_food_item(self, row, service):
        try:
            # Check if the item already exists for the given service
            item, created = FoodServiceItem.objects.get_or_create(
                service=service,
                name=row['name'],
                defaults={
                    'price': row['price'],
                    'category': row.get('category', 'GEN'),
                    'vegetarian': row.get('is_veg', False),
                    'description': row.get('description', ''),
                    'addon': self.parse_addon(row['addon'])
                }
            )

            if created:
                self.stdout.write(self.style.SUCCESS(f"Created food item: {item.name}"))
            else:
                # If the item already exists, update the relevant fields
                self.stdout.write(self.style.WARNING(f"Food item '{item.name}' already exists. Updating details."))

                # Update the existing item's fields with the current CSV data
                item.price = row['price']
                item.category = row.get('category', 'GEN')
                item.vegetarian = row.get('is_veg', False)
                item.description = row.get('description', '')
                item.addon = self.parse_addon(row['addon']) 

                # Save the updates
                item.save()
                self.stdout.write(self.style.SUCCESS(f"Updated food item: {item.name}"))

            # Handle the image if not already set or if an image_url is provided
            if not item.image and 'image_url' in row:
                self.load_image_for_item(item, row.get('image_url'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error processing item '{row['name']}': {e}"))
            self.stdout.write(self.style.ERROR(traceback.format_exc()))

    def parse_addon(self, addon):
        """Helper method to parse addon field."""
        if isinstance(addon, str):
            addon = addon.replace("'", '"')  
            return json.loads(addon)
        return None

    def load_image_for_item(self, item, image_url=None):
        if image_url:
            try:
                result = urllib.request.urlretrieve(image_url)
                with open(result[0], 'rb') as img_file:
                    image_content = ContentFile(img_file.read())
                    item.image.save(f'{item.name}_image.png', image_content)
                    item.save()
                self.stdout.write(self.style.SUCCESS(f"Image saved for {item.name}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error downloading image for {item.name}: {e}"))
        else:
            self.stdout.write(self.style.ERROR(f"Invalid image URL provided for: {item.name}"))

    def generate_images_for_missing_items(self, service):
        items_without_images = FoodServiceItem.objects.filter(service=service).filter(models.Q(image__isnull=True) | models.Q(image=''))
        if not items_without_images.exists():
            self.stdout.write(self.style.SUCCESS("No food items with missing images found."))
            return

        for item in items_without_images:
            self.generate_image_from_openai(item)

        self.stdout.write(self.style.SUCCESS('Image generation completed for all items with missing images.'))

    def generate_image_from_openai(self, item):
        prompt = f"{item.name}: {item.description}"
        self.stdout.write(f"Generating image for '{item.name}' with prompt: {prompt}")

        try:
            client = OpenAI()

            # Generating image using OpenAI API
            response = client.images.generate(
                model="dall-e-2",
                prompt=prompt,
                size="512x512",
                quality="standard",
                n=1,
            )

            # Retrieving the generated image URL
            image_url = response['data'][0]['url']
            result = urllib.request.urlretrieve(image_url)

            # Saving the generated image
            with open(result[0], 'rb') as img_file:
                image_content = ContentFile(img_file.read())
                item.image.save(f'{item.name}_generated.png', image_content)
                item.save()

            self.stdout.write(self.style.SUCCESS(f"Image successfully generated and saved for {item.name}")) 

        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error generating image for {item.name}: {e}"))

    def validate_row(self, row):
        """
        Validate the CSV row to ensure no missing or NaN values in essential fields.
        Throws a detailed error if any required field is missing.
        """
        required_fields = ['name', 'price', 'category', 'is_veg', 'description']
        for field in required_fields:
            if pd.isnull(row[field]) or row[field] == '':
                raise ValueError(f"Missing value in field '{field}' for row: {row.to_dict()}")
