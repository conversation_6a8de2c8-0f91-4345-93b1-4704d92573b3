from .models import Notification, NotificationSubscription, NotificationCategory, NotificationTemplates, NotificationLog, NotificationChannelHandler
from core.models import Partner<PERSON><PERSON><PERSON><PERSON>, User
from celery import shared_task
from django.utils import timezone
import importlib
import logging

logger = logging.getLogger(__name__)


def subscribe_user_to_notifications(profile):
    if isinstance(profile, PartnerProfile):
        notifications=Notification.objects.filter(
            user_type=Notification.UserTypeOptions.PARTNER
        )
        for notification in notifications:
            NotificationSubscription.objects.create(
                partner=profile,
                notification=notification
            )
    else:
        notifications=Notification.objects.filter(
            user_type=Notification.UserTypeOptions.USER
        )
        for notification in notifications:
            NotificationSubscription.objects.create(
                user=profile,
                notification=notification
            )

def validate_data_for_notification(category, data):
    notification_template=NotificationTemplates.get(category.name)
    if not notification_template:
        return False, 'No template found for ' + category.name
    template, datav = notification_template
    for key in datav:
        if key not in data:
            return False, 'Missing ' + key + ' in data'
    return True, template(**data)

def load_handler_from_channel_name(channel_name:str):
    try:
        handler_string = NotificationChannelHandler.get(channel_name)
        if not handler_string:
            logger.warning(f"No handler configured for channel: {channel_name}")
            return None
        
        mods = handler_string.split('.')
        module = importlib.import_module('.'.join(mods[:-1]))
        handler = getattr(module, mods[-1])
        return handler
    except (ImportError, AttributeError) as e:
        logger.error(f"Failed to load handler for channel {channel_name}: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error loading handler for channel {channel_name}: {str(e)}")
        return None


@shared_task
def send_notification(user_id:str, event:str, data:dict):
    category=NotificationCategory.from_name(event)
    if not category:
        raise ValueError('Invalid event name ' + event)
    
    notification_types=Notification.objects.filter(
        category=category.name
    )
    validated, template = validate_data_for_notification(category, data)
    if not validated:
        error_msg="Invalid data for notification"
        return False, error_msg
    title, body = template
    user = User.objects.get(id=user_id)
    if user.is_partner:
        notification_profile=user.partner_profile.notification_profile
    else:
        notification_profile=user.user_profile.notification_profile
    
    # Track notification results
    total_notifications = 0
    successful_notifications = 0
    failed_notifications = 0
    skipped_notifications = 0
    errors = []
    
    for notification in notification_types:
        handler=load_handler_from_channel_name(notification.channel)
        if handler is None:
            skipped_notifications += 1
            continue
        
        total_notifications += 1
        
        # Create notification log entry
        notification_log=NotificationLog.objects.create(
            notification=notification,
        )
        if user.is_partner:
            notification_log.partner_notification_profile=notification_profile
        else:
            notification_log.user_notification_profile=notification_profile
        
        try:
            result = handler().send_message(user.id, title, body)
            if isinstance(result, tuple) and len(result) == 2:
                success, response = result
            else:
                success = False
                response = "Invalid handler response format"

            if success:
                notification_log.is_sent = True
                notification_log.message_status = 'sent'
                successful_notifications += 1
                # Store message ID if available (for WhatsApp)
                if isinstance(response, str) and response:
                    notification_log.message_id = response
            else:
                notification_log.is_sent = False
                notification_log.message_status = 'failed'
                notification_log.error_msg = response
                failed_notifications += 1
                errors.append(f"{notification.channel}: {response}")
        except Exception as e:
            error_msg = str(e)
            notification_log.is_sent = False
            notification_log.message_status = 'failed'
            notification_log.error_msg = error_msg
            failed_notifications += 1
            errors.append(f"{notification.channel}: {error_msg}")
        
        notification_log.save()
    
    # Return appropriate status based on results
    if total_notifications == 0 and skipped_notifications == 0:
        return False, 'No notification channels configured for this event'
    elif total_notifications == 0 and skipped_notifications > 0:
        return False, f'All {skipped_notifications} notification channel(s) skipped (no handlers available)'
    elif successful_notifications == total_notifications:
        skip_msg = f' ({skipped_notifications} skipped)' if skipped_notifications > 0 else ''
        return True, f'All {successful_notifications} notification(s) sent successfully{skip_msg}'
    elif successful_notifications > 0:
        skip_msg = f' ({skipped_notifications} skipped)' if skipped_notifications > 0 else ''
        return True, f'{successful_notifications} of {total_notifications} notification(s) sent successfully{skip_msg}. Failures: {"; ".join(errors)}'
    else:
        skip_msg = f' ({skipped_notifications} skipped)' if skipped_notifications > 0 else ''
        return False, f'All {total_notifications} notification(s) failed{skip_msg}. Errors: {"; ".join(errors)}'


@shared_task
def send_service_partner_notification(service_partner_id: str, event: str, data: dict):
    """
    Send notification to a ServicePartner using their phone number
    """
    from service.models.service import ServicePartner
    
    category = NotificationCategory.from_name(event)
    if not category:
        raise ValueError('Invalid event name ' + event)
    
    error_msg = None
    
    notification_types = Notification.objects.filter(
        category=category.name,
        user_type=Notification.UserTypeOptions.PARTNER
    )
    
    validated, template = validate_data_for_notification(category, data)
    if not validated:
        error_msg = "Invalid data for notification"
        return False, error_msg
    
    title, body = template
    
    try:
        service_partner = ServicePartner.objects.get(id=service_partner_id)
        
        # Check if service partner has phone number
        if not service_partner.phone_number:
            return False, 'ServicePartner has no phone number'
        
        # Track notification results
        total_notifications = 0
        successful_notifications = 0
        failed_notifications = 0
        skipped_notifications = 0
        errors = []
        
        for notification in notification_types:
            handler = load_handler_from_channel_name(notification.channel)
            if handler is None:
                skipped_notifications += 1
                continue
            
            total_notifications += 1
            
            # Create notification log entry
            notification_log = NotificationLog.objects.create(
                notification=notification,
                service_partner=service_partner
            )
            
            try:
                # For WhatsApp notifications, we need to pass phone number directly
                result = handler().send_message_to_phone(
                    str(service_partner.phone_number), body, title=title
                )
                if isinstance(result, tuple) and len(result) == 3:
                    success, response, message_id = result
                else:
                    success = False
                    response = "Invalid handler response format"
                    message_id = None
                if success:
                    notification_log.is_sent = True
                    notification_log.message_status = 'sent'
                    successful_notifications += 1
                    if message_id:
                        notification_log.message_id = message_id
                else:
                    notification_log.is_sent = False
                    notification_log.message_status = 'failed'
                    notification_log.error_msg = response
                    failed_notifications += 1
                    errors.append(f"{notification.channel}: {response}")
                                
            except Exception as e:
                error_msg = str(e)
                notification_log.is_sent = False
                notification_log.message_status = 'failed'
                notification_log.error_msg = error_msg
                failed_notifications += 1
                errors.append(f"{notification.channel}: {error_msg}")
            
            notification_log.save()
        
        # Return appropriate status based on results
        if total_notifications == 0 and skipped_notifications == 0:
            return False, 'No notification channels configured for ServicePartner'
        elif total_notifications == 0 and skipped_notifications > 0:
            return False, f'All {skipped_notifications} notification channel(s) skipped for ServicePartner (no handlers available)'
        elif successful_notifications == total_notifications:
            skip_msg = f' ({skipped_notifications} skipped)' if skipped_notifications > 0 else ''
            return True, f'All {successful_notifications} notification(s) sent successfully to ServicePartner{skip_msg}'
        elif successful_notifications > 0:
            skip_msg = f' ({skipped_notifications} skipped)' if skipped_notifications > 0 else ''
            return True, f'{successful_notifications} of {total_notifications} notification(s) sent to ServicePartner{skip_msg}. Failures: {"; ".join(errors)}'
        else:
            skip_msg = f' ({skipped_notifications} skipped)' if skipped_notifications > 0 else ''
            return False, f'All {total_notifications} notification(s) failed for ServicePartner{skip_msg}. Errors: {"; ".join(errors)}'
        
    except ServicePartner.DoesNotExist:
        return False, f'ServicePartner with id {service_partner_id} not found'
    except Exception as e:
        return False, str(e)


@shared_task
def update_message_status(message_id, status, timestamp=None):
    """
    Update message status based on webhook data
    """
    try:
        # Find the notification log with this message ID
        notification_log = NotificationLog.objects.filter(message_id=message_id).first()
        
        if notification_log:
            # Update status
            notification_log.message_status = status
            
            # Update timestamps based on status
            if status == 'delivered' and timestamp:
                notification_log.delivery_timestamp = timezone.datetime.fromtimestamp(
                    int(timestamp), tz=timezone.get_current_timezone()
                )
            elif status == 'read' and timestamp:
                notification_log.read_timestamp = timezone.datetime.fromtimestamp(
                    int(timestamp), tz=timezone.get_current_timezone()
                )
                notification_log.is_read = True
            
            # Mark as sent if delivered or read
            if status in ['delivered', 'read']:
                notification_log.is_sent = True
            elif status == 'failed':
                notification_log.is_sent = False
                notification_log.error_msg = 'Message delivery failed'
            
            notification_log.save()
            logger.info(f"Updated message {message_id} status to {status}")
            
        else:
            logger.warning(f"NotificationLog not found for message_id: {message_id}")
            
    except Exception as e:
        logger.error(f"Error updating message status: {str(e)}")