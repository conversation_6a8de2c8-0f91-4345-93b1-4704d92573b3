# Generated by Django 4.2.7 on 2025-07-19 07:37

from django.db import migrations, models
import django.db.models.deletion
import phonenumber_field.modelfields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('geo', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServicePartner',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200)),
                ('type_of_service', models.PositiveSmallIntegerField(choices=[(1, 'Food'), (2, 'Laundry'), (3, 'Transport'), (4, 'Rental'), (5, 'Others'), (6, 'Shop'), (7, 'Tourism')], default=5)),
                ('description', models.TextField(blank=True, null=True)),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=128, null=True, region=None)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='geo.location')),
            ],
        ),
    ]
